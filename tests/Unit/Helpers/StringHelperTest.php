<?php

namespace Tests\Unit\Helpers;

use App\Helpers\StringHelper;
use PHPUnit\Framework\TestCase;

class StringHelperTest extends TestCase
{
    /**
     * Test the extractDigits method
     */
    public function test_extract_digits_from_string(): void
    {
        // Test with the example string
        $this->assertEquals('73929558021', StringHelper::extractDigits('739-29-55-8021'));
        
        // Test with a string containing no digits
        $this->assertEquals('', StringHelper::extractDigits('abc-def-ghi'));
        
        // Test with a string containing only digits
        $this->assertEquals('12345', StringHelper::extractDigits('12345'));
        
        // Test with a string containing mixed characters
        $this->assertEquals('123', StringHelper::extractDigits('a1b2c3'));
        
        // Test with an empty string
        $this->assertEquals('', StringHelper::extractDigits(''));
    }
}
