<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_tasks', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('installation');
            $table->unsignedBigInteger('partner_id');
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('executed_by')->nullable()->default(null);
            $table->string('name');
            $table->string('number')->nullable()->default(null);
            $table->text('comment')->nullable()->default(null);
            $table->string('location')->nullable()->default(null);
            $table->unsignedBigInteger('location_id')->nullable()->default(null);
            $table->string('location_model')->nullable()->default(null);
            $table->date('planned_date_at')->nullable();
            $table->date('done_at')->nullable();
            $table->boolean('status')->default(1);
            $table->json('configuration')->default(null)->nullable();
            $table->timestamps();
            $table->index('installation');
            $table->index(['installation', 'partner_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_tasks');
    }
};
