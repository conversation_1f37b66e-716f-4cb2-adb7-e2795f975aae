<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('manufacturers', function (Blueprint $table) {
            $table->index('hash', 'manufacturers_hash_index');
            $table->index(['hash', 'installation'], 'manufacturers_hash_installation_index');
            $table->index(['installation'], 'manufacturers_installation_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('manufacturers', function (Blueprint $table) {
            $table->dropIndex('manufacturers_hash_index');
            $table->dropIndex('manufacturers_hash_installation_index');
            $table->dropIndex('manufacturers_installation_index');
        });
    }
};
