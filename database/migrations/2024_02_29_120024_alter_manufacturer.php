<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('manufacturers', function (Blueprint $table){
            $table->string('phone', 100)->nullable(true)->default(null)->after('address');
            $table->string('email', 60)->nullable(true)->default(null)->after('phone');
            $table->string('contact_name')->nullable(true)->default(null)->after('email');
            $table->string('website')->nullable(true)->default(null)->after('contact_name');
            $table->text('address')->nullable(true)->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('manufacturers', static function (Blueprint $table){
            $table->dropColumn([
                'phone',
                'email',
                'contact_name',
                'website'
            ]);
        });
    }
};
