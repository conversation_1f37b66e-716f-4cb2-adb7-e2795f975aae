<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', static function (Blueprint $table){
            $table->decimal('price_per_unit', 8,2,true)
                ->nullable(true)->after('manufacturer_id');
            $table->string('basic_unit', 48)
                ->nullable(true)->after('price_per_unit');
            $table->integer('volume_ml', false, true)
                ->nullable(true)->after('basic_unit');
            $table->integer('weight_gr', false, true)
                ->nullable(true)->after('volume_ml');
            $table->boolean('limited_stock')
                ->nullable(false)->default(true)->after('weight_gr');
            $table->boolean('below_stock')
                ->nullable(false)->default(false)
                ->after('limited_stock')
                ->comment('Can be issued from starage when current state is 0');
            $table->string('ext_link')->nullable(true)->change();
            $table->string('gtin')->nullable(true)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', static function (Blueprint $table){
            $table->dropColumn([
                'price_per_unit',
                'basic_unit',
                'volume_ml',
                'weight_gr',
                'limited_stock',
                'below_stock'
            ]);
        });
    }
};
