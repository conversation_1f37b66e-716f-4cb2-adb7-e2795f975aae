<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('warehouse_docs', function(Blueprint $table){
            $table->string('signatory_name')->nullable(true)->default(null)->change();
            $table->string('signatory_last_name')->nullable(true)->default(null)->change();
            $table->integer('doc_number')->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('warehouse_docs', function(Blueprint $table){
            $table->string('signatory_name')->nullable(false)->change();
            $table->string('signatory_last_name')->nullable(false)->change();
            $table->string('doc_number', 40)->nullable(false)->change();
        });
    }
};
