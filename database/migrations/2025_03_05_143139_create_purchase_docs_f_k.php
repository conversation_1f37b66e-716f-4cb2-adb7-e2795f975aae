<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    use App\Traits\ForeignKeysTrait;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        self::createForeignKey(
            'purchase_doc_items',
            'purchase_doc_uuid',
            'purchase_docs',
            'uuid'
        );
        self::createFore<PERSON><PERSON><PERSON>(
            'purchase_doc_metas',
            'purchase_doc_uuid',
            'purchase_docs',
            'uuid'
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        self::dropForeignKey(
            'purchase_doc_items',
            self::createForeignKeyName('purchase_doc_items', ['purchase_doc_uuid'])
        );
        self::dropForeignKey(
            'purchase_doc_metas',
            self::createForeignKeyName('purchase_doc_metas', ['purchase_doc_uuid'])
        );
    }
};
