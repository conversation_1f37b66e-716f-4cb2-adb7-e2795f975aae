<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trade_doc_items', function (Blueprint $table) {
            $table
                ->uuid('source_id')
                ->nullable()
                ->default(null)
                ->after('trade_doc_uuid');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trade_doc_items', function (Blueprint $table) {
            $table->dropColumn('source_id');
        });
    }
};
