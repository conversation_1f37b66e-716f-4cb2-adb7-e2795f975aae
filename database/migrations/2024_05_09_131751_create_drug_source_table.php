<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('drug_source', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->string('name');
            $table->string('long_name');
            $table->string('gtin', 20)->nullable()->default(null);
            $table->bigInteger('system_id', false)->nullable()->default(null);
            $table->boolean('removed')->default(false);
            $table->json('json')->nullable()->default(null);
            $table->timestamps();
            $table->index('name');
            $table->index('gtin');
            $table->index('system_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('drug_source');
    }
};
