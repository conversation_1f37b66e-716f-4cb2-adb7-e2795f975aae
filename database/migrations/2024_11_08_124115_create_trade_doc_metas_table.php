<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trade_doc_metas', function (Blueprint $table) {
            $table->id();
            $table->uuid('trade_doc_uuid');
            $table->json('meta');
            $table->timestamps();
            $table->softDeletes();
            $table->index(['trade_doc_uuid']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trade_doc_metas');
    }
};
