<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('partners', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('installation', false, true);
            $table->string('hash', 64);
            $table->string('name', 64);
            $table->text('address')->nullable(true);
            $table->string('postcode', 100)->nullable(true)->default(null);
            $table->string('city', 100)->nullable(true)->default(null);
            $table->string('country_id', 4)->nullable(true)->default(null);
            $table->string('phone', 100)->nullable(true)->default(null);
            $table->string('email', 60)->nullable(true)->default(null);
            $table->string('contact_name')->nullable(true)->default(null);
            $table->string('website')->nullable(true)->default(null);
            $table->string('bank_name')->nullable(true)->default(null);
            $table->string('bank_iban')->nullable(true)->default(null);
            $table->string('bank_swift')->nullable(true)->default(null);
            $table->string('bank_account')->nullable(true)->default(null);
            $table->string('vat_id')->nullable(true)->default(null);
            $table->smallInteger('vat_type', false, true)->nullable(false)->default(1);
            $table->smallInteger('business_type', false, true)->nullable(false)->default(1);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });


    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('partners');
    }
};
