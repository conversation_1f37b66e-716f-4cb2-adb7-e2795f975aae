<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('document_series', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('installation', false)->nullable(false);
            $table->integer('doc_type')->unsigned()->nullable(false);
            $table->string('name')->nullable(false);
            $table->string('pattern', 100)->nullable(false);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(true);
            $table->tinyText('switch');
            $table->tinyText('counter_key')->default(null)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_series');
    }
};
