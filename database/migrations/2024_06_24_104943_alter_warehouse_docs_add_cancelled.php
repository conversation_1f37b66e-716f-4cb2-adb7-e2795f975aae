<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('warehouse_docs', function (Blueprint $table) {
            $table->boolean('cancelled')->default(false);
            $table->integer('cancelled_by', false, true)->default(null)->nullable();
            $table->dateTime('cancelled_at')->default(null)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('warehouse_docs', function (Blueprint $table) {
            $table->dropColumn(['cancelled','cancelled_at', 'cancelled_by']);
        });
    }
};
