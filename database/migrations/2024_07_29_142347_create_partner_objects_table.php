<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('partner_objects', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('partner_id')->unsigned()->nullable(false);
            $table->string('name')->nullable(false);
            $table->string('short_name')->nullable()->default(null);
            $table->text('address')->nullable(true);
            $table->decimal('lat', 15, 10)->nullable(true)->default(null);
            $table->decimal('lon', 15, 10)->nullable(true)->default(null);
            $table->json('metadata')->nullable(true)->default(null);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('partner_objects');
    }
};
