<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    use App\Traits\ForeignKeysTrait;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        self::createForeignKey('trade_doc_items', 'trade_doc_uuid', 'trade_docs', 'uuid');
        self::createForeignKey('trade_doc_metas', 'trade_doc_uuid', 'trade_docs', 'uuid');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        self::dropForeign<PERSON>ey('trade_doc_items', self::createForeignKeyName('trade_doc_items', ['trade_doc_uuid']));
        self::dropForeignKey('trade_doc_metas', self::createForeignKeyName('trade_doc_metas', ['trade_doc_uuid']));
    }
};
