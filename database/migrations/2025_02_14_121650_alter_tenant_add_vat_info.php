<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tenants', function (Blueprint $table) {
            $table->smallInteger('vat_type', false, true)->nullable()->after('vat_id');
            $table->string('tax_residency_country', 4)->nullable()->after('vat_type');
            $table->smallInteger('tax_type', false, true)->nullable()->after('tax_residency_country');
            $table->smallInteger('accounting_type', false, true)->nullable()->after('tax_type');
            $table->smallInteger('business_type', false, true)->nullable()->after('accounting_type');
            $table->dropColumn('bank_name');
            $table->dropColumn('bank_iban');
            $table->dropColumn('bank_swift');
            $table->dropColumn('bank_account');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tenants', function (Blueprint $table) {
            $table->dropColumn('tax_residency_country');
            $table->dropColumn('accounting_type');
            $table->dropColumn('vat_type');
            $table->dropColumn('tax_type');
            $table->string('bank_name')->nullable();
            $table->string('bank_iban')->nullable();
            $table->string('bank_swift')->nullable();
            $table->string('bank_account')->nullable();
        });
    }
};
