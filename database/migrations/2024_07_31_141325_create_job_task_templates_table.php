<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_task_templates', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('installation');
            $table->string('name')->nullable(false);
            $table->json('configuration')->default(null)->nullable();
            $table->timestamps();
            $table->index('installation');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_task_templates');
    }
};
