<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_demands', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id', false, true);
            $table->bigInteger('product_id', false, true);
            $table->integer('amount', false, true);
            $table->date('demand_at');
            $table->smallInteger('status');
            $table->bigInteger('installation', false, true);
            $table->timestamps();
            $table->index('installation');
            $table->index('product_id');
            $table->index('user_id');
            $table->index('demand_at');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_demands');
    }
};
