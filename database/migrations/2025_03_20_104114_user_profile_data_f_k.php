<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    use \App\Traits\ForeignKeysTrait;

    public function up(): void
    {

        Schema::table('profile_data', static function (Blueprint $table) {
            $table->bigInteger('user_id', false, true)->change();
        });

        self::createForeignKey(
            'profile_data',
            'user_id',
            'users',
            'id'
        );
    }

    public function down(): void
    {
        self::dropForeignKey(
            'profile_data',
            self::createForeignKeyName('profile_data', ['id'])
        );
    }
};
