<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    protected $table_name = 'stocktaking';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create($this->table_name, function (Blueprint $table) {
            $table->id();
            $table->string('hash', 24)->nullable(false);
            $table->date('for_date')->nullable(false);
            $table->bigInteger('warehouse_id', false, true)->nullable(false);
            $table->bigInteger('creator_id', false, true)->nullable(false);
            $table->bigInteger('user_id', false, true)->nullable(true);
            $table->decimal('expected_value', 10, 2)->default(0);
            $table->decimal('real_value', 10, 2)->default(0);
            $table->decimal('difference_value', 10, 2)->default(0);
            $table->dateTime('accepted_at')->default(null)->nullable(true);
            $table->bigInteger('installation', false, true);
            $table->timestamps();
            $table->index(['installation', 'warehouse_id']);
            $table->index('installation');
            $table->index('accepted_at');
            $table->index(['installation', 'warehouse_id', 'for_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if(Schema::hasTable($this->table_name)) {
            Schema::drop($this->table_name);
        }
    }
};
