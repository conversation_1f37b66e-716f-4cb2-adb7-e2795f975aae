<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_doc_items', function (Blueprint $table) {
            $table->uuid()->primary();
            $table->uuid('purchase_doc_uuid');
            $table->uuid('source_id')->nullable()->default(null);
            $table->bigInteger('installation')->unsigned();
            $table->text('label');
            $table->integer('net_unit_price');
            $table->integer('gross_unit_price');
            $table->integer('discount_type');
            $table->integer('discount_value');
            $table->integer('discounted_unit_price');
            $table->decimal('amount', 16, 6);
            $table->string('unit_type')->default('szt.');
            $table->integer('net_value')->nullable()->default(null);
            $table->integer('vat_rate')->nullable()->default(null);
            $table->string('vat_label', 20)->nullable()->default(null);
            $table->integer('vat_value')->nullable()->default(null);
            $table->integer('gross_value');
            $table->json('tags')->default(null)->nullable();
            $table->timestamps();
            $table->index('installation');
            $table->index(['installation', 'purchase_doc_uuid']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_doc_items');
    }
};
