<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('warehouse_docs', function (Blueprint $table) {
            $table->string('full_doc_number')->nullable()->after('doc_number');
            $table->integer('document_series_id', false, true)->nullable()->after('full_doc_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        try {
            Schema::table('warehouse_docs', function (Blueprint $table) {
                $table->dropColumn('full_doc_number');
                $table->dropColumn('document_series_id');
            });
        } catch (Exception $e) {
        }
    }
};
