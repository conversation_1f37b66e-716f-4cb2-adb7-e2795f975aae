<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currency_rates', function (Blueprint $table) {
            $table->id();
            $table->string('base_code', 8)->nullable(false);
            $table->string('dest_code', 8)->nullable(false);
            $table->decimal('rate', 14, 8)->nullable(false);
            $table->smallInteger('factor', false, false)
                ->nullable(false)
                ->default(0);
            $table->date('rate_date')->nullable(false);
            $table->string('provider', 8)->nullable(false);
            $table->timestamps();
            $table->index(['base_code', 'dest_code']);
            $table->index(['base_code', 'dest_code', 'rate_date', 'provider']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currency_rates');
    }
};
