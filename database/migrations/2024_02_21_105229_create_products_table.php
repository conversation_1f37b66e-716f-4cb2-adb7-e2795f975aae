<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('hash', 64);
            $table->bigInteger('manufacturer_id')->nullable(false);
            $table->text('description')->default('');
            $table->string('gtin', 20);
            $table->string('ext_link')->default('');
            $table->boolean('is_active')->default(false);
            $table->bigInteger('installation')->nullable(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
