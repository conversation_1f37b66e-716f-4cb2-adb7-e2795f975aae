<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('warehouse_logs', function (Blueprint $table){
            $table->decimal('price', 8, 2, true)->change();
            $table->bigInteger('installation', false, true)->nullable(false);
            $table->dropColumn('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('warehouse_logs', function (Blueprint $table){
            $table->decimal('price', 8, 2, true)->change();
            $table->drop('installation');
            $table->string('name')->nullable();
        });
    }
};
