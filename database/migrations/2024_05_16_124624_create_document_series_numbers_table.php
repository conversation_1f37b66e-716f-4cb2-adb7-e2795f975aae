<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('document_series_numbers', function (Blueprint $table) {
            $table->id();
            $table->integer('document_series_id')->unsigned()->nullable(false);
            $table->smallInteger('year', false, true)->nullable(false);
            $table->smallInteger('month', false, true)->nullable(false);
            $table->integer('counter', false, true)->nullable(false)->default(0);
            $table->unique(['document_series_id', 'year', 'month'], 'series_year_month');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_series_numbers');
    }
};
