<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('warehouse_items', function(Blueprint $table){
            $table->string('gtin', 100)->nullable(true)->default(null)->change();
        });
        Schema::table('warehouse_logs', function(Blueprint $table){
            $table->string('gtin', 100)->nullable(true)->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('warehouse_items', function(Blueprint $table){
            $table->string('gtin', 100)->nullable(false)->default('0000')->change();
        });
        Schema::table('warehouse_logs', function(Blueprint $table){
            $table->string('gtin', 100)->nullable(false)->default('0000')->change();
        });
    }
};
