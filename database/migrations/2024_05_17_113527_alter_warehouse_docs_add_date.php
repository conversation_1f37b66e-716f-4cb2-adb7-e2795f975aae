<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('warehouse_docs', function (Blueprint $table) {
            $table->date('doc_date')->nullable()->default(null)->after('full_doc_number');
            $table->integer('doc_number')->nullable(true)->default(null)->change();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('warehouse_docs', function (Blueprint $table) {
            $table->dropColumn('doc_date');
            $table->integer('doc_number')->nullable(false)->change();
        });
    }
};
