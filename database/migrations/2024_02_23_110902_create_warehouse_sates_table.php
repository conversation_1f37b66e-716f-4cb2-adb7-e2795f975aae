<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouse_sates', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('warehouse_id', false, true)->nullable(false);
            $table->bigInteger('product_id', false, true)->nullable(false);
            $table->integer('amount')->default(0);
            $table->integer('price')->default(0);
            $table->boolean('is_net')->default(false);
            $table->string('hash', 81)->nullable(false);
            $table->string('name', 100)->nullable(true);
            $table->string('gtin', 100)->default('0000');
            $table->string('series_no', 100)->nullable(true);
            $table->string('transaction_id', 100)->nullable(false);
            $table->date('exp_date')->nullable(true);
            $table->timestamps();
            $table->index('warehouse_id', 'warehouse');
            $table->index('product_id', 'product');
            $table->index('gtin');
            $table->index('series_no');
            $table->index('transaction_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouse_sates');
    }
};
