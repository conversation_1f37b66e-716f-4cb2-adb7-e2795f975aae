<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouses', function (Blueprint $table) {
            $table->id();
            $table->string('hash', 81)->nullable(false);
            $table->string('name', 100)->nullable(false);
            $table->text('address')->nullable(true);
            $table->smallInteger('owner_type', false, true)->nullable(false)->default(1)
            ->comment('1: tenant, 2: employee');
            $table->bigInteger('owner_identifier', false, true);
            $table->bigInteger('installation', false, true);
            $table->boolean('is_active')->default(false);
            $table->index(['owner_type', 'owner_identifier'], 'owner');
            $table->index('installation', 'tenant');
            $table->index('hash', 'hash');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouses');
    }
};
