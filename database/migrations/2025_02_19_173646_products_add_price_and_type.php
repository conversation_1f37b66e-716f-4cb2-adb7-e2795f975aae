<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->boolean('is_net')
                ->after('price_per_unit')
                ->default(1);
            $table->integer('vat_rate')
                ->after('is_net')
                ->nullable()->default(null);
            $table->string('vat_label')
                ->after('vat_rate')
                ->nullable()->default(null);
            $table->smallInteger('item_type')
                ->after('below_stock')
                ->default(1);
            $table->string('extra_code')
                ->after('gtin')
                ->nullable()->default(null);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('is_net');
            $table->dropColumn('vat_rate');
            $table->dropColumn('vat_label');
            $table->dropColumn('item_type');
            $table->dropColumn('extra_code');
        });
    }
};
