<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trade_docs', function (Blueprint $table) {
            $table->uuid('final_invoice_trade_doc_id')
                ->after('status')
                ->nullable()
                ->default(null);
            $table->boolean('is_final_invoice')
                ->after('final_invoice_trade_doc_id')
                ->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('trade_docs', 'final_invoice_trade_doc_id')) {
            Schema::table('trade_docs', function (Blueprint $table) {
                $table->dropColumn('final_invoice_trade_doc_id');
                $table->dropColumn('is_final_invoice');
            });
        }
    }
};
