<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('profile_data', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id');
            $table->string(column: 'name', length: 255)->default(null)->nullable();
            $table->string(column: 'surname', length: 255)->default(null)->nullable();
            $table->text(column: 'adress')->default(null)->nullable();
            $table->text(column: 'number')->default(null)->nullable();
            $table->string(column: 'lang', length: 5)->default('pl');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('profile_data');
    }
};
