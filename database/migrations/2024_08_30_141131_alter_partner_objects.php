<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('partner_objects', 'partner_work_id')) {
            Schema::table('partner_objects', function (Blueprint $table) {
                $table->bigInteger('partner_work_id', false, true)
                    ->default(null)
                    ->nullable()
                    ->after('partner_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('partner_objects', 'partner_work_id')) {
            Schema::dropColumns('partner_objects', ['partner_work_id']);
        }
    }
};
