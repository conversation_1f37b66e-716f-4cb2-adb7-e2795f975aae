<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tenants', function (Blueprint $table){
            $table->string('postcode', 100)->nullable(true)->default(null)->after('address');
            $table->string('city', 100)->nullable(true)->default(null)->after('postcode');
            $table->string('phone', 100)->nullable(true)->default(null)->after('city');
            $table->string('email', 60)->nullable(true)->default(null)->after('phone');
            $table->string('contact_name')->nullable(true)->default(null)->after('email');
            $table->string('website')->nullable(true)->default(null)->after('contact_name');
            $table->string('system_domain')->nullable(true)->default(null)->after('website');
            $table->string('bank_name')->nullable(true)->default(null)->after('system_domain');
            $table->string('bank_iban')->nullable(true)->default(null)->after('bank_name');
            $table->string('bank_swift')->nullable(true)->default(null)->after('bank_iban');
            $table->string('bank_account')->nullable(true)->default(null)->after('bank_swift');
            $table->string('vat_id')->nullable(true)->default(null)->after('bank_swift');
            $table->text('address')->nullable(true)->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tenants', static function (Blueprint $table){
            $table->dropColumn([
                'postcode',
                'city',
                'phone',
                'email',
                'contact_name',
                'website',
                'system_domain',
                'bank_name',
                'bank_iban',
                'bank_swift',
                'bank_account',
                'vat_id',
            ]);
        });
    }
};
