<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('warehouse_docs', function (Blueprint $table){
            $table->bigInteger('target_warehouse_id', false, true)
                ->nullable()->default(null)
                ->after('warehouse_id');
            $table->index(['warehouse_id']);
            $table->index(['target_warehouse_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::whenTableHasColumn('warehouse_docs', 'target_warehouse_id', function (Blueprint $table){
            $table->dropColumn('target_warehouse_id');
            $table->dropIndex(['warehouse_id']);
        });
    }
};
