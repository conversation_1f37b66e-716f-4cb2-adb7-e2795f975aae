<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('warehouse_logs', function (Blueprint $table){
            $table->string('source_doc_id', 100,)->nullable(true)->default(null)->after('document_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('warehouse_logs', function (Blueprint $table){
            try{
                $table->dropColumn(['source_doc_id']);
            } catch (\Exception $e) {

            }
        });
    }
};
