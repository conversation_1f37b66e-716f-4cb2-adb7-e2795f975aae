<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('partners', function (Blueprint $table) {
            $table->smallInteger('vat_type', false, true)
                ->nullable(true)
                ->default(null)
                ->change();
            $table->smallInteger('business_type', false, true)
                ->nullable(true)
                ->default(null)
                ->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('null', function (Blueprint $table) {
            $table->smallInteger('vat_type', false, true)
                ->nullable(false)
                ->default(1)
                ->change();
            $table->smallInteger('business_type', false, true)
                ->nullable(false)
                ->default(1)
                ->change();
        });
    }
};
