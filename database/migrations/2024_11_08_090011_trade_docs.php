<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trade_docs', function (Blueprint $table) {
            $table->uuid()->primary();
            $table->string('source_id', 36)->nullable()->default(null);
            $table->bigInteger('installation')->unsigned();
            $table->bigInteger('issuer_id')->unsigned();
            $table->bigInteger('seller_id')->unsigned()->nullable()->default(null);
            $table->bigInteger('buyer_id')->unsigned();
            $table->string('type');
            $table->unsignedInteger('document_series_id');
            $table->string('full_doc_number');
            $table->unsignedInteger('doc_number');
            $table->string('transaction_id', 40);

            $table->tinyInteger('payment_type');
            $table->string('payment_type_label')->nullable()->default(null);
            $table->smallInteger('payment_credit_days', false, true);
            $table->date('payment_due_date');
            $table->date('payment_date')->nullable()->default(null);
            $table->boolean('is_paid')->default(false);

            $table->string('currency')->default('PLN');
            $table->decimal('exchange_rate', 16, 6)->default(1);
            $table->date('currency_rate_date',)->nullable(true)->default(null);
            $table->smallInteger('vat_method');
            $table->integer('net')->nullable()->default(null);
            $table->integer('vat_amount')->nullable()->default(null);
            $table->integer('gross');

            $table->date('issued_at');
            $table->date('sells_date');

            $table->string('ksef_ref')->nullable(true)->default(null);
            $table->smallInteger('ksef_status')->nullable(false)->default(0);
            $table->string('ksef_inv_number')->nullable(true)->default(null);

            $table->string('notes')->nullable()->default(null);
            $table->tinyInteger('status')->default(0);
            $table->boolean('is_accepted')->default(false);
            $table->boolean('has_correction')->default(false);
            $table->boolean('is_cancelled')->default(false);
            $table->dateTime('cancelled_at')->nullable();
            $table->unsignedBigInteger('cancelled_by')->nullable();

            $table->unsignedBigInteger('creator_id')->unsigned();

            $table->timestamps();
            $table->softDeletes();
            $table->index('installation');
            $table->index(['installation', 'creator_id']);
            $table->index(['installation', 'issued_at']);
            $table->index(['installation', 'sells_date']);
            $table->index(['installation', 'is_paid']);
            $table->index(['installation', 'full_doc_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trade_docs');
    }
};
