<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_demands', function (Blueprint $table) {
            $table->string('context')->nullable()->default(null)->after('user_id');
            $table->string('context_identifier')->nullable()->default(null)->after('context');
            $table->index(['context', 'context_identifier']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_demands', function (Blueprint $table) {
            $table->dropColumn(['context','context_identifier']);
        });
    }
};
