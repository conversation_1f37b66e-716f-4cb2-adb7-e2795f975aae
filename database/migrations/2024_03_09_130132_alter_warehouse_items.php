<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('warehouse_items', function (Blueprint $table){
            $table->boolean('reserved')->nullable(false)->default(false)->after('transaction_id');
            $table->string('reserved_by', 100,)->nullable(true)->default(null)->after('reserved');
            $table->index(['reserved']);
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        try{
            Schema::table('warehouse_items', function (Blueprint $table){
                $table->dropIndex(['reserved']);
            });
        } catch (\Exception) {}

        Schema::whenTableHasColumn('warehouse_items', 'reserved', function (Blueprint $table){
                $table->dropColumn(['reserved']);
        });
        Schema::whenTableHasColumn('warehouse_items', 'reserved_by', function (Blueprint $table){
                $table->dropColumn(['reserved_by']);
        });

        Schema::whenTableHasColumn('warehouse_items', 'deleted_at', function (Blueprint $table){
            $table->dropColumn(['deleted_at']);
        });
    }
};
