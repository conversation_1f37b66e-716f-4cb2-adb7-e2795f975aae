<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    protected $table_name = 'stocktaking_items';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create($this->table_name, function (Blueprint $table) {
            $table->id();
            $table->string('hash', 24)->nullable(false);
            $table->bigInteger('stocktaking_id', false, true)->nullable(false);
            $table->bigInteger('product_id', false, true)->nullable(false);
            $table->string('gtin', 100)->default(null)->nullable();
            $table->string('series_no', 100)->nullable(true);
            $table->integer('base_amount')->default(0);
            $table->integer('real_amount')->default(0);
            $table->integer('difference_amount')->default(0);
            $table->decimal('price')->default(0);
            $table->decimal('expected_value')->default(0);
            $table->decimal('real_value')->default(0);
            $table->timestamps();
            $table->index('stocktaking_id');
            $table->index('product_id');
            $table->index('gtin');
            $table->index('series_no');
        });
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if(Schema::hasTable($this->table_name)) {
            Schema::drop($this->table_name);
        }
    }
};
