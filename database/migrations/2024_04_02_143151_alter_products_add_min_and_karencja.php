<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function(Blueprint $table){
            $table->text('grace_period')->after('description')->nullable(true)->default(null);
            $table->smallInteger('minimum_stock')->after('grace_period')->nullable(true)->default(5);
            $table->smallInteger('minimum_exp_date')->after('minimum_stock')->nullable(true)->default(null);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function(Blueprint $table){
            $table->dropColumn(['minimum_stock', 'grace_period', 'minimum_exp_date']);
        });
    }
};
