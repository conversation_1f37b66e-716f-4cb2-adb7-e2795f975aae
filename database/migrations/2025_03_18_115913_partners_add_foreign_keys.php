<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    use App\Traits\ForeignKeysTrait;

    public function up(): void
    {
        self::createForeign<PERSON>ey(
            'partner_works',
            'partner_id',
            'partners',
            'id'
        );

        self::createFore<PERSON><PERSON><PERSON>(
            'partner_objects',
            'partner_id',
            'partners',
            'id'
        );

        self::createFore<PERSON><PERSON><PERSON>(
            'partner_metas',
            'partner_id',
            'partners',
            'id'
        );

        self::createForeign<PERSON><PERSON>(
            'partner_objects',
            'partner_work_id',
            'partner_works',
            'id'
        );
    }

    public function down(): void
    {
        self::dropForeign<PERSON>ey(
            'partner_works',
            self::createForeign<PERSON>eyName('partner_works', ['id'])
        );

        self::dropForeign<PERSON><PERSON>(
            'partner_objects',
            self::createForeignKeyName('partner_objects', ['id'])
        );

        self::dropForeign<PERSON><PERSON>(
            'partner_metas',
            self::createForeignKeyName('partner_metas', ['id'])
        );

        self::dropForeign<PERSON>ey(
            'partner_objects',
            self::createForeign<PERSON>eyName('partner_objects', ['id'])
        );
    }
};
