<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('job_tasks', 'objects')) {
            Schema::table('job_tasks', function (Blueprint $table) {
                $table->json('objects')
                    ->default(null)
                    ->nullable()
                    ->after('location_model');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('job_tasks', 'objects')) {
            Schema::dropColumns('job_tasks', 'objects');
        }
    }
};
