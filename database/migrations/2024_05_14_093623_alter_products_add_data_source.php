<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->string('data_source')->default(null)->nullable()->after('installation');
            $table->string('data_source_identifier')->default(null)->nullable()->after('data_source');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            try{
                $table->dropColumn('data_source');
                $table->dropColumn('data_source_identifier');
            } catch (Exception $exception) {

            }
        });
    }
};
