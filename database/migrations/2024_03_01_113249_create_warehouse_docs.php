<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouse_docs', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('installation', false, true)->nullable(false);
            $table->bigInteger('warehouse_id', false, true)->nullable(false);
            $table->bigInteger('user_id', false, true)->nullable(false)
                ->comment('Creator of the document');
            $table->bigInteger('signatory_id', false, true)->nullable(true)
                ->comment('Signatory of the document - if exists in DB. Usually the same as creator');
            $table->smallInteger('type', false, true)->nullable(false)->comment('PZ, WZ, MMp, MMm, PW, ZW, RW');
            $table->string('signatory_name')->nullable(false);
            $table->string('signatory_last_name')->nullable(false);
            $table->text('issuer_data')->nullable(true);
            $table->bigInteger('partner_id', false, true)->nullable(false);
            $table->text('partner_data')->nullable(true);
            $table->dateTime('items_issue_date')->nullable(true);
            $table->string('doc_number', 40)->nullable(false);
            $table->string('transaction_id', 40)->nullable(false);
            $table->string('notes')->nullable(true);
            $table->boolean('accepted')->nullable(false)->default(false);
            $table->dateTime('accepted_at')->nullable(true)->default(null);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouse_docs');
    }
};
