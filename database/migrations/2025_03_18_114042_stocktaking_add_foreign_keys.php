<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    use App\Traits\ForeignKeysTrait;

    public function up(): void
    {
        self::createForeignKey(
            'stocktaking_items',
            'stocktaking_id',
            'stocktaking',
            'id'
        );
    }

    public function down(): void
    {
        self::dropForeignKey(
            'stocktaking_items',
            self::createForeignKeyName('stocktaking_items', ['id'])
        );
    }
};
