<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('warehouse_items', function(Blueprint $table){
            $table->smallInteger('minimum_exp_date')->after('exp_date')->nullable(true)->default(null);
            $table->smallInteger('minimum_stock')->after('minimum_exp_date')->nullable(true)->default(5);
        });

        \App\Models\WarehouseItem::with(['product'])->each(function (\App\Models\WarehouseItem $item){
            $item->minimum_exp_date = $item->product->minimum_exp_date;
            $item->minimum_stock = $item->product->minimum_stock;
            $item->save();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('warehouse_items', function(Blueprint $table){
            $table->dropColumn(['minimum_stock', 'minimum_exp_date']);
        });
    }
};
