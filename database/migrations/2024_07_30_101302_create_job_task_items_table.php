<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_task_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('job_task_id');
            $table->unsignedBigInteger('product_id');
            $table->unsignedBigInteger('installation');
            $table->string('product_name');
            $table->decimal('expected_quantity');
            $table->decimal('quantity')->nullable()->default(null);
            $table->string('quantity_unit');
            $table->boolean('make_demand')->default(false);
            $table->timestamps();
            $table->index('installation');
            $table->index(['installation', 'job_task_id']) ;
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_task_items');
    }
};
