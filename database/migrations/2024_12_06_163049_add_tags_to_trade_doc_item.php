<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trade_doc_items', function (Blueprint $table) {
            $table->json('tags')->default(null)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('trade_doc_items', 'tags')) {
            Schema::table('trade_doc_item', function (Blueprint $table) {
                $table->dropColumn('tags');
            });
        }
    }
};
