<?php

namespace Database\Seeders;

use App\Helpers\Identifiers;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserHashSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        $users = User::all();
        foreach ($users as $user){
            $user->update([
               'hash' => Identifiers::getRandomHash(5)
            ]);
        }
    }
}
