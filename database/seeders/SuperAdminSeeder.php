<?php

namespace Database\Seeders;

use App\Helpers\Identifiers;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('users')->insert([
            'name' => 'Sadmin',
            'email' => '<EMAIL>',
            'hash' => Identifiers::getRandomHash(5),
            'password' => Hash::make('F4jN3H4Sl0'),
        ]);
    }
}
