<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use function Laravel\Prompts\table;

class BasicSystemRolesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        Schema::disableForeignKeyConstraints();
        DB::table('roles')->truncate();

        $superAdmin = Role::create(['id' => 1, 'name' => 'Super Admin']);
        $superAdmin->givePermissionTo(Permission::all());

        Role::create(['id' => 2, 'name' => 'Tenant Admin']);
        Role::create(['id' => 3, 'name' => 'Employee']);

        if (empty(User::where('name', 'Sadmin')->first()))
        {
            $this->call(SuperAdminSeeder::class);
        }

        $sAdminUser = User::where('name', 'Sadmin')->first();
        $sAdminUser->assignRole('Super Admin');

        Schema::enableForeignKeyConstraints();
    }
}
