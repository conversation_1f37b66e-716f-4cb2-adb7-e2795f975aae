<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class UpdateDocDate extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::update('UPDATE warehouse_docs SET doc_date = DATE_FORMAT(created_at, "%Y-%m-%d") WHERE doc_date IS NULL');
    }
}
