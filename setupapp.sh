#!/bin/bash

TEST_ONLY=1
COMPOSE_FILE=0
ENV_FILE=0
LARAVEL_LOG=0
QUEUE_LOG=0
QUEUE_ERROR_LOG=0
SSL_CERT=0

if [[ $# -eq 1 ]] && [[ "$1" == "force" ]]; then
    TEST_ONLY=0
fi

if [ ! -f "server.docker-compose.yml" ]; then
    echo "No server.docker-compose.yml file found"
else echo "server.docker-compose.yml file found" && COMPOSE_FILE=1
fi


if [ ! -f ".env" ]; then
  echo "No .env file found. Exiting..."
  echo "Use cp .env.example .env to create a .env file"
else echo ".env file found" && ENV_FILE=1
fi


if [ ! -f "storage/logs/laravel.log" ]; then
    echo "No storage/logs/laravel.log file found. Will be created..."
    else echo "storage/logs/laravel.log file found" && LARAVEL_LOG=1
fi

if [ ! -f "storage/logs/queue.log" ]; then
    echo "No storage/logs/queue.log file found. Will be created..."
else echo "storage/logs/queue.log file found" && QUEUE_LOG=1
fi

if [ ! -f "storage/logs/queue_error.log" ]; then
    echo "No storage/logs/queue_error.log file found. Will be created..."
else echo "storage/logs/queue_error.log file found" && QUEUE_ERROR_LOG=1
fi

if [ ! -f "docker/nginx/tf2ssl/cf_certificate.pem" ]; then
    echo "No docker/nginx/tf2ssl/cf_certificate.pem file found. exiting..."
else echo "docker/nginx/tf2ssl/cf_certificate.pem file found" && SSL_CERT=1
fi


function is_docker_running() {
    docker > /dev/null 2>&1; dr=$?

    case $dr in
        0) echo "Docker is running";;
        1) echo "Docker exit with status 1" && exit 1;;
        *) echo "Docker is not running or not installed. Exit code: $dr" && exit 1;;
    esac

    docker compose > /dev/null 2>&1; dcr=$?

    case $dcr in
        0) echo "Docker compose is running";;
        1) echo "Docker compose exit with status 1" && exit 1;;
        *) echo "Docker compose is not running or not installed. Exit code: $dcr" && exit 1;;
    esac
}

is_docker_running


if [[ $TEST_ONLY -eq 1 ]]; then
    echo "Test only mode. If you want to process with changes run with 'force' parameter. Exiting..."
    exit 0
fi

if [ $COMPOSE_FILE -eq 0 ]; then
    echo "No docker-compose.yml file found. exiting..."
    exit 1
fi

if [ $SSL_CERT -eq 0 ]; then
    echo "No certificate file found. exiting..."
    exit 1
fi

if [ $ENV_FILE -eq 0 ]; then
  echo "No .env file found. Exiting..."
  echo "Use cp .env.example .env to create a .env file"
  exit 1
fi

if [ $LARAVEL_LOG -eq 0 ]; then
    touch storage/logs/laravel.log
fi

if [ $QUEUE_LOG -eq 0 ]; then
    touch storage/logs/queue.log
fi

if [ $QUEUE_ERROR_LOG -eq 0 ]; then
    touch storage/logs/queue_error.log
fi


echo "Building images base php image..."
docker-compose build --no-cache app
echo "Building images app images..."
docker-compose build --no-cache nginx

echo "Building completed"
echo "Running containers..."
docker-compose up -d
USER_ID=$(id -u)
GROUP_ID=$(id -g)
docker-compose exec -T -u $USER_ID:$GROUP_ID app composer install
docker-compose exec -T -u $USER_ID:$GROUP_ID app php artisan key:generate
docker-compose exec -T -u $USER_ID:$GROUP_ID app php artisan migrate --step
docker-compose exec -T -u $USER_ID:$GROUP_ID app php artisan db:seed BasicSystemRolesSeeder
docker-compose exec -T -u $USER_ID:$GROUP_ID app php artisan wh:sync-roles-permissions --force

if [ ! -d "public/storage" ]; then
    docker compose exec -T -u $USER_ID:$GROUP_ID app php artisan storage:link
fi

