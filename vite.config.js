import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/css/app.css', 'resources/js/app.js', 'resources/css/filament/app/theme.css'],
            refresh: true,
        }),
    ],
    build: {
        rollupOptions: {
            output: {
                // Wyłączenie haszowania nazw plików
                entryFileNames: `assets/[name].js`,
                chunkFileNames: `assets/[name].[ext]`,
                assetFileNames: `assets/[name].[ext]`,
            },
        },
    },
});
