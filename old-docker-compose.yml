services:
    php:
        build:
            context: ./docker/8.3
            dockerfile: php.dockerfile
            args:
                WWWGROUP: '${WWWGROUP}'
                WWWUSER: '${WWWUSER}'
        image: sail-8.3/php
        networks:
            - sail
        profiles:
            - buildOnly
    app:
        build:
            context: ./docker/8.3
            dockerfile: app.dockerfile
            args:
                WWWGROUP: '${WWWGROUP}'
                WWWUSER: '${WWWUSER}'
        image: sail-8.3/app
        extra_hosts:
            - 'host.docker.internal:host-gateway'
        ports:
            - '${APP_PORT:-80}:80'
            - '${VITE_PORT:-5173}:${VITE_PORT:-5173}'
        environment:
            WWWUSER: '${WWWUSER}'
            LARAVEL_SAIL: 1
            XDEBUG_MODE: '${SAIL_XDEBUG_MODE:-off}'
            XDEBUG_CONFIG: '${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}'
            IGNITION_LOCAL_SITES_PATH: '${PWD}'
        volumes:
            - '.:/var/www/html'
        networks:
            - sail
        container_name: app
        depends_on:
            - mariadb
            - redis
#            - meilisearch

    mariadb:
        image: 'mariadb:10'
        ports:
            - '${FORWARD_DB_PORT:-3306}:3306'
        environment:
            MYSQL_ROOT_PASSWORD: '${DB_PASSWORD}'
            MYSQL_ROOT_HOST: '%'
            MYSQL_DATABASE: '${DB_DATABASE}'
            MYSQL_USER: '${DB_USERNAME}'
            MYSQL_PASSWORD: '${DB_PASSWORD}'
            MYSQL_ALLOW_EMPTY_PASSWORD: 'yes'
        volumes:
            - './docker/mysql/data:/var/lib/mysql'
            - './docker/mysql/schema:/schema'
            - './docker/mysql/scripts:/scripts'
        networks:
            - sail
        container_name: mariadb
        healthcheck:
            test: ["CMD", "mysqladmin", "ping", "-p${DB_PASSWORD}"]
            retries: 3
            timeout: 5s

    redis:
        image: 'redis:alpine'
        ports:
            - '${FORWARD_REDIS_PORT:-6379}:6379'
        volumes:
            - 'sail-redis:/data'
        networks:
            - sail
        container_name: redis
        healthcheck:
            test: ["CMD", "redis-cli", "ping"]
            retries: 3
            timeout: 5s

    cron:
        build:
            context: ./docker/8.3
            dockerfile: cron.dockerfile
            args:
                WWWGROUP: '${WWWGROUP}'
                WWWUSER: '${WWWUSER}'
        container_name: cron
        image: sail-8.3/cron
        volumes:
            - '.:/var/www/html'
        extra_hosts:
            - 'host.docker.internal:host-gateway'
        environment:
            WWWUSER: '${WWWUSER}'
        depends_on:
            - app
        networks:
            - sail

    mailhog_server:
        image: 'mailhog/mailhog:latest'
        logging:
            driver: 'none'
        ports:
            - '1025:1025'
            - '8025:8025'
        networks:
            - sail
        restart: always


#    mongodb:
#        image: mongo
#        restart: always
#        environment:
#            MONGO_INITDB_ROOT_USERNAME: warehouse
#            MONGO_INITDB_ROOT_PASSWORD: warehouse
#            MONGO_INITDB_DATABASE: warehouse
#        ports:
#            - '27017:27017'
#        volumes:
#            - 'sail-mongodb:/data'
#        networks:
#            - sail

#    meilisearch:
#        image: 'getmeili/meilisearch:latest'
#        ports:
#            - '${FORWARD_MEILISEARCH_PORT:-7700}:7700'
#        environment:
#            MEILI_NO_ANALYTICS: '${MEILISEARCH_NO_ANALYTICS:-false}'
#        volumes:
#            - 'sail-meilisearch:/meili_data'
#        networks:
#            - sail
#        healthcheck:
#            test:
#                - CMD
#                - wget
#                - '--no-verbose'
#                - '--spider'
#                - 'http://localhost:7700/health'
#            retries: 3
#            timeout: 5s

networks:
    sail:
        driver: bridge
volumes:
    sail-mysql:
        driver: local
    sail-mariadb:
        driver: local
    sail-redis:
        driver: local
#    sail-meilisearch:
#        driver: local
#    sail-mongodb:
#        driver: local
    sail-mailhog:
        driver: local
