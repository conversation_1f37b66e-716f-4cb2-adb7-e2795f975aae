<?php

namespace App\Repositories;

use App\Enums\DocumentGeneralTypes;
use App\Enums\DocumentPatternMarkers;
use App\Enums\DocumentTypes;
use App\Enums\PartnerVATTypes;
use App\Models\DocumentSeriesPattern;
use App\Models\Tenant;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DocumentSeriesRepository
{

    public static $validation_error = '';

    public static function seedDefaultSeries(Tenant $tenant, $docType = 'warehouse'): void
    {
        switch ($docType) {
            default:
            case 'warehous':
                self::seedDefaultWarehouseDocSeries($tenant);
                break;
            case 'trade':
                self::seedDefaultTradeDocSeries($tenant);
                break;
        }
    }

    public static function createDefaultDocSeries(Tenant $tenant, DocumentTypes $doc_type): DocumentSeriesPattern
    {
        $seriesPattern = new DocumentSeriesPattern();
        $seriesPattern->installation = $tenant->id;
        $seriesPattern->doc_type = $doc_type;
        $seriesPattern->name = $doc_type->label();
        $seriesPattern->pattern = $doc_type->getDefaultSeriesPattern();
        $seriesPattern->switch = $doc_type->getDefaultSeriesSwitch();
        $seriesPattern->save();
        $seriesPattern->createSeriesNumberRow(now());
        return $seriesPattern;
    }


    public static function seedDefaultWarehouseDocSeries(Tenant $tenant)
    {
        foreach (DocumentTypes::getDocTypesOf(DocumentGeneralTypes::WAREHOUSE) as $doc_type) {
            self::createDefaultDocSeries($tenant, $doc_type);
        }
    }

    public static function seedDefaultTradeDocSeries(Tenant $tenant)
    {
        foreach (DocumentTypes::getSellsTypeDocs(match ($tenant->vat_type) {
            PartnerVATTypes::NOTVAT => 'NotVATDocType',
            default => 'VATDocType'
        }) as $doc_type) {
            self::createDefaultDocSeries($tenant, $doc_type);
        }
    }


    public static function getDefaultTDocTypesForTenant(Tenant $tenant): array
    {
        return DocumentTypes::getSellsTypeDocs(match ($tenant->vat_type) {
            PartnerVATTypes::NOTVAT => 'NotVATDocType',
            default => 'VATDocType'
        });
    }

    /**
     * Return array of options for select field when Create TradeDoc is called
     * @param Tenant $tenant
     * @param array $except Lisa of document types to exclude (int) DocumentTypes::*->value
     * @return array Array of options for select field [value => label]
     */
    public static function getTDocForCreateSelect(Tenant $tenant, array $except = []): array
    {
        $subtype = match ($tenant->vat_type) {
            PartnerVATTypes::NOTVAT => 'NotVATDocType',
            default => 'VATDocType'
        };
        return self::getSeriesForTenant(
            tenant(),
            collect(DocumentTypes::getSellsTypeDocs($subtype))->pluck('value')->toArray()
        )
            ->where('is_blocked', false)
            ->when(filled($except), fn(Collection $query) => $query->whereNotIn('doc_type.value', $except))
            ->map(
                fn(DocumentSeriesPattern $dsp) => $dsp->setAttribute('label', $dsp->doc_type->label())
            )
            ->pluck('label', 'doc_type.value')->toArray();
    }

    public static function getTDocForFilterSelect(Tenant $tenant, array $except = []): array
    {
        $subtype = match ($tenant->vat_type) {
            PartnerVATTypes::NOTVAT => 'NotVATDocType',
            default => 'VATDocType'
        };
        return self::getSeriesForTenant(
            tenant(),
            collect(DocumentTypes::getSellsTypeDocs())->pluck('value')->toArray()
        )
//            ->where('is_blocked', false)
            ->when(filled($except), fn(Collection $query) => $query->whereNotIn('doc_type.value', $except))
            ->map(
                fn(DocumentSeriesPattern $dsp) => $dsp->setAttribute('label', $dsp->doc_type->label())
            )
            ->pluck('label', 'doc_type.value')->toArray();
    }

    public static function createDefaultWHDocSeries(Tenant $tenant, DocumentTypes $doc_type): DocumentSeriesPattern
    {
        return self::createDefaultDocSeries($tenant, $doc_type);
    }

    public static function createDefaultTradeDocSeries(Tenant $tenant, DocumentTypes $doc_type): DocumentSeriesPattern
    {
        return self::createDefaultDocSeries($tenant, $doc_type);
    }

    public static function blockTenantDocSeries(Tenant $tenant, DocumentTypes $doc_type): void
    {
        DocumentSeriesPattern::where('installation', $tenant->id)
            ->where('doc_type', $doc_type->value)
            ->update(['is_blocked' => true]);
    }


    public static function unblockTenantDocSeries(Tenant $tenant, DocumentTypes $doc_type): void
    {
        DocumentSeriesPattern::where('installation', $tenant->id)
            ->where('doc_type', $doc_type->value)
            ->update(['is_blocked' => false]);
    }

    public static function checkSeriesPattern(string $pattern): bool
    {
        $string = implode('|', array_column(DocumentPatternMarkers::cases(), 'value'));
        $markers_pattern = '/(' . $string . ')/';
        preg_match_all($markers_pattern, $pattern, $matches, PREG_PATTERN_ORDER, 0);
        if (false === in_array('%L%', $matches[0])) {
            self::$validation_error = 'Znacznik licznika (\'%L%\') musi pojawić się we wzorcu';
            return false;
        }
        $g_pattern = '/[[[:alnum:]\/\-_]+/u';
        $string_pattern = preg_replace($markers_pattern, '', $pattern);
        $test = preg_match($g_pattern, $string_pattern, $matches1);
        if ($string_pattern !== $matches1[0]) {
            self::$validation_error = 'We wzorcu są niedozwolone znaki';
            return false;
        }
        return true;
    }

    /**
     * @param Model|Tenant $tenant
     * @param array|null $types
     * @return Collection<DocumentSeriesPattern>
     */
    public static function getSeriesForTenant(Model|Tenant $tenant, ?array $types = null): Collection
    {
        $qry = DocumentSeriesPattern::where('installation', $tenant->id);
        if (filled($types)) {
            $qry->whereIn('doc_type', $types);
        }
        return $qry->get();
    }

    public static function getSeriesForDocType(
        DocumentTypes|int $docsType,
        bool $activeOnly = false
    ): Collection {
        return match ($activeOnly) {
            default => DocumentSeriesPattern::docType($docsType)->get(),
            true => DocumentSeriesPattern::docType($docsType)->active()->get(),
        };
    }

    public static function getDefaultSeriesForDocType(DocumentTypes|int $docsType): ?DocumentSeriesPattern
    {
        return DocumentSeriesPattern::default()->docType($docsType)->first();
    }

    public static function getDocNumber(
        DocumentSeriesPattern|int $series_id,
        ?\DateTime $date = null
    ): DocumentSeriesPattern {
        $dsp = match (is_int($series_id)) {
            true => DocumentSeriesPattern::find($series_id),
            default => $series_id
        };

        return $dsp->generateNextDocNumber($date ?? now());
    }

    public static function setDefaultForDocType(int $new_default_series, int $document_type): bool
    {
        DB::enableQueryLog();
        try {
            DB::transaction(static function () use ($new_default_series, $document_type) {
                DB::table('document_series')
                    ->where('doc_type', $document_type)
                    ->whereNot('id', $new_default_series)
                    ->where('installation', auth()->user()->installation())
                    ->update(['is_default' => 0]);
                DB::table('document_series')
                    ->where('id', $new_default_series)
                    ->where('installation', auth()->user()->installation())
                    ->update(['is_default' => 1]);
            });
            return true;
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return false;
        }
    }

    public static function deleteOrSoftDeletePattern(DocumentSeriesPattern $pattern): bool
    {
        if ($pattern->isEnabledToRemove()) {
            $pattern->seriesCounters()->forceDelete();
            $pattern->forceDelete();
            return true;
        }
        return false;
    }


    public static function togglePatternState(DocumentSeriesPattern $pattern): bool
    {
        if ($pattern->is_default && $pattern->is_active) {
            return false;
        }

        $pattern->is_active = !$pattern->is_active;
        $pattern->save();
        return true;
    }

    public static function handleUpdate(DocumentSeriesPattern $record, array $data): DocumentSeriesPattern
    {
        $record->update($data);
        $record->seriesCounters()->delete();
        return $record->refresh();
    }

    public static function toArrayWithLabels()
    {
        return DocumentSeriesPattern::pluck('name', 'id')->toArray();
    }


    public static function isLastInNumberInSeries(int $series_id, int $number, \DateTimeInterface $doc_date): bool
    {
        $dsp = DocumentSeriesPattern::query()->find($series_id);
        $last = $dsp->getLastNumberInSeries($doc_date);
        return $number === $last;
    }

    public static function rollbackDocNumber(
        DocumentSeriesPattern|int $series_id,
        $currentCounter,
        ?\DateTime $date = null,
    ): DocumentSeriesPattern {
        $dsp = match (is_int($series_id)) {
            true => DocumentSeriesPattern::find($series_id),
            default => $series_id
        };

        return $dsp->rollbackNumber($date ?? now(), $currentCounter);
    }


    public static function getPDocForCreateSelect(array $except = []): array
    {
        $response = [];
        collect(DocumentTypes::getSellsTypeDocs('PurchaseTypeDoc'))
            ->each(function (DocumentTypes $dsp) use (&$response, $except) {
                if (in_array($dsp, $except, true)) {
                    return;
                }
                $response[$dsp->value] = $dsp->label();
            });
        return $response;
    }
}
