<?php

namespace App\Repositories;

use App\Enums\DocumentTypes;
use App\Filament\Filters\WHSelectFilter;
use App\Helpers\Identifiers;
use App\Models\DocumentSeriesPattern;
use App\Models\Partner;
use App\Models\Products;
use App\Models\WarehouseDoc;
use App\Models\WarehouseItem;
use App\Models\WarehouseLog;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class WarehouseDocsRepository
{

    public static $error = '';
    public static $error_code = 0;

    public static function canDocBeRemoved(WarehouseDoc $doc): bool
    {

        switch ($doc->type) {
            case DocumentTypes::PZ:
            case DocumentTypes::PW:
            case DocumentTypes::ZW:
            case DocumentTypes::WZ:
            case DocumentTypes::RW:
                return
                    DocumentSeriesRepository::isLastInNumberInSeries(
                        $doc->document_series_id,
                        $doc->doc_number,
                        $doc->doc_date
                    ) &&
                    !$doc->isAccepted();
            case DocumentTypes::MP:
                $mw = $doc->getCounterpart();
                $mwok = false;
                if (filled($mw)) {
                    if ($mw?->isCancelled() &&
                        DocumentSeriesRepository::isLastInNumberInSeries(
                            $mw?->document_series_id,
                            $mw?->doc_number,
                            $mw?->doc_date
                        )) {
                        $mwok = true;
                    }
                } else {
                    $mwok = true;
                }
                $mpok = false;
                if (($doc->isCancelled() || !$doc->isAccepted()) &&
                    DocumentSeriesRepository::isLastInNumberInSeries(
                        $doc->document_series_id,
                        $doc->doc_number,
                        $doc->doc_date
                    )) {
                    $mpok = true;
                }

                return $mwok === true && $mpok === true;

            case DocumentTypes::MW:
                //@TODO Check if it works correctly
                $mp = $doc->getCounterpart();
                $mpok = false;

                if (filled($mp)) {
                    if ($mp?->isCancelled() &&
                        DocumentSeriesRepository::isLastInNumberInSeries(
                            $mp?->document_series_id,
                            $mp?->doc_number,
                            $mp?->doc_date
                        )) {
                        $mpok = true;
                    }
                } else {
                    $mpok = true;
                }

                $mwok = false;
                if (($doc->isCancelled() || !$doc->isAccepted()) &&
                    DocumentSeriesRepository::isLastInNumberInSeries(
                        $doc->document_series_id,
                        $doc->doc_number,
                        $doc->doc_date
                    )) {
                    $mwok = true;
                }
                return $mwok === true && $mpok === true;
        }

        return false;
    }

    public static function canDocBeCancelled(WarehouseDoc $doc): bool
    {
        if ($doc->isCancelled()) {
            return false;
        }
        $doc->load('log');
        switch ($doc->type) {
            case DocumentTypes::PZ:
            case DocumentTypes::PW:
            case DocumentTypes::ZW:
                if (!$doc->isAccepted()) {
                    return true;
                }

                foreach ($doc->log as $docItem) {
                    if ($docItem->items()->where('warehouse_id', $doc->warehouse_id)->count() !==
                        (int)$docItem->amount) {
                        return false;
                    }
                }
                return true;
            case DocumentTypes::WZ:
            case DocumentTypes::RW:
                if (!$doc->isAccepted()) {
                    return true;
                }
                break;
            case DocumentTypes::MW:
            case DocumentTypes::MP:
                if (!$doc->isAccepted()) {
                    return true;
                }
                if ($doc->isAccepted() &&
                    ($doc->getCounterpart()?->isAccepted() ?? false)
                ) {
                    return false;
                }
                return true;
        }
        return false;
    }

    public static function canDocBeEdited(WarehouseDoc $doc): bool
    {
        switch ($doc->type) {
            default:
                return !($doc->isAccepted() || $doc->isCancelled());
            case DocumentTypes::MP:
                return false;
        }
    }


    public static function mutateFilamentDataOnEmployeeMMDoc($data): array
    {
        $data['header']['type'] = DocumentTypes::MW->value;
        $data['header']['signatory_name'] = auth()->user()->profile->name;
        $data['header']['signatory_last_name'] = auth()->user()->profile->surname;
        $data['header']['signatory_id'] = auth()->user()->id;
        $data['header']['autogenerate'] = true;
        $data['header']['doc_date'] = now('Europe/Warsaw')->format('Y-m-d');
        $data['header']['document_series_id'] = DocumentSeriesRepository::getDefaultSeriesForDocType(
            DocumentTypes::MW
        )->id;
        $data['header'] = self::mutateFilamentDataOnDocCreate($data['header']);
        return $data;
    }

    public static function ProcessEmployeeMMDoc($data): bool
    {
        $data = self::mutateFilamentDataOnEmployeeMMDoc($data);
        if (null === $doc = self::createWarehouseDocModel($data['header'])) {
            return false;
        }
        $items = [];
        foreach ($data['items'] as $item) {
            $items[] = self::CreateWarehouseDocItemModel($item, $doc);
        }

        $doc = $doc->refresh();

        if (!self::acceptDoc($doc)) {
            return false;
        }

        if (!self::updateItemsAfterAccept($doc)) {
            self::revertAcceptDoc($doc);
            return false;
        }

        if (null === $MMpdoc = self::makeMPonMWDoc($doc)) {
            return false;
        }

        if (!self::acceptDoc($MMpdoc)) {
            return false;
        }
        if (!self::updateItemsAfterAccept($MMpdoc)) {
            self::revertAcceptDoc($MMpdoc);
            return false;
        }
        return true;
    }


    public static function mutateFilamentDataOnEmployeeWZ($data): array
    {
        $data = self::mutateFilamentDataOnDocCreate($data);
        $data['partner_data'] = Partner::find($data['partner_id'])->getAddressText();
        return $data;
    }

    public static function ProcessEmployeeExternalIssueDoc($data): bool|WarehouseDoc
    {
        $data['header']['type'] = DocumentTypes::WZ->value;
        $data['header']['autogenerate'] = true;
        $data['header']['doc_date'] = now('Europe/Warsaw')->format('Y-m-d');
        $data['header']['document_series_id'] = DocumentSeriesRepository::getDefaultSeriesForDocType(
            DocumentTypes::WZ
        )->id;
        $header = self::mutateFilamentDataOnEmployeeWZ($data['header']);

        if (null === $doc = self::createWarehouseDocModel($header)) {
            return false;
        }

        $items = [];
        foreach ($data['items'] as $item) {
            $items[] = self::CreateWarehouseDocItemModel($item, $doc, 'filament:employee');
        }

        $doc = $doc->refresh();

        if (!self::acceptDoc($doc)) {
            return false;
        }

        if (!self::updateItemsAfterAccept($doc)) {
            self::revertAcceptDoc($doc);
            return false;
        }

        return $doc;
    }


    public static function mutateFilamentDataOnDocCreate($data): array
    {
        $data['installation'] = auth()->user()->installation();
        $data['user_id'] = auth()->user()->id;
        $data['issuer_data'] = auth()->user()->getTenant()->getAddressText();
        $data['transaction_id'] = self::CreateDocTransactionId($data);
        $data['warehouse_id'] = (int)WHSelectFilter::deconstructCompoundIndex($data['warehouse_id'])['id'];

        if ($data['target_warehouse_id'] ?? false) {
            $data['target_warehouse_id'] = (int)WHSelectFilter::deconstructCompoundIndex(
                $data['target_warehouse_id']
            )['id'];
        }

        if ($data['autogenerate'] ?? false) {
            self::CreateDocNumber($data);
        }
        unset($data['autogenerate']);

        if (in_array((int)$data['type'], [
            DocumentTypes::MW->value,
            DocumentTypes::MP->value,
            DocumentTypes::PW->value,
            DocumentTypes::ZW->value,
            DocumentTypes::RW->value,
        ], true)
        ) {
            $data['partner_id'] = 0;
            $data['partner_data'] = $data['issuer_data'];
        }

        return $data;
    }


    public static function createWarehouseDocModel($data): ?WarehouseDoc
    {
        try {
            $model = new WarehouseDoc($data);
            $model->saveOrFail();
            return $model;
        } catch (\Throwable $e) {
            self::$error = $e->getMessage();
        }
        return null;
    }

    public static function deleteWarehouseDoc(WarehouseDoc $doc): bool
    {
        if (false === self::canDocBeRemoved($doc)) {
            self::$error = 'Istnieje nowszy dokument w serii lub ten został już zaakceptowany';
            return false;
        }
        /**
         * @var WarehouseLog $log
         */
        DB::enableQueryLog();
        try {
            DB::transaction(static function () use ($doc) {
                /**
                 * @var DocumentSeriesPattern $dsp
                 */
                $dsp = $doc->documentSeries;
                if (!$dsp->rollbackNumber($doc->doc_date, $doc->doc_number)) {
                    throw new \Exception('Błąd serii dokumentu!', 1088);
                }
                foreach ($doc->log as $log) {
                    if (null !== $log->source_doc_id) {
                        WarehouseRepository::removeReservationOnItems(
                            $doc->warehouse_id,
                            $log->source_doc_id,
                            $log->transaction_id
                        );
                    }
                    $log->delete();
                }
                $doc->delete();
            });

            return true;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            self::$error_code = $e->getCode();
            return false;
        }
    }

    public static function cancelWarehouseDoc(WarehouseDoc $doc, ?string $reason = null): bool
    {
        if (false === self::canDocBeCancelled($doc)) {
            self::$error = 'Dokument nie może być anulowany';
            return false;
        }
        /**
         * @var WarehouseLog $log
         */

        switch ($doc->type) {
            case DocumentTypes::PZ:
            case DocumentTypes::PW:
            case DocumentTypes::ZW:
                foreach ($doc->log as $log) {
                    $log->items()->where('warehouse_id', $doc->warehouse_id)->delete();
                }
                $doc->cancel($reason)->save();
                break;
            case DocumentTypes::WZ:
            case DocumentTypes::RW:
                foreach ($doc->log as $log) {
                    if (null === $log->source_doc_id) {
                        continue;
                    }
                    WarehouseRepository::removeReservationOnItems(
                        $doc->warehouse_id,
                        $log->source_doc_id,
                        $log->transaction_id
                    );
                }
                $doc->cancel($reason)->save();
                break;
            case DocumentTypes::MP:
                $mw = $doc->getCounterpart();
                $mw?->cancel($reason)->save();
                foreach ($doc->log as $log) {
                    WarehouseRepository::removeReservationOnItems(
                        warehouse_id: $mw->warehouse_id ?? 0,
                        item_transaction_id: $log->source_doc_id,
                        reserved_by: $log->transaction_id
                    );
                }
                $doc->cancel($reason)->save();
                break;
            case DocumentTypes::MW:
                $mp = $doc->getCounterpart();
                if (null !== $mp) {
                    foreach ($mp->log as $log) {
                        WarehouseRepository::removeReservationOnItems(
                            $doc->warehouse_id,
                            $log->source_doc_id,
                            $log->transaction_id
                        );
                    }
                    $mp->cancel($reason)->save();
                } else {
                    foreach ($doc->log as $log) {
                        WarehouseRepository::removeReservationOnItems(
                            $doc->warehouse_id,
                            $log->source_doc_id,
                            $log->transaction_id
                        );
                    }
                }
                $doc->cancel($reason)->save();
                break;
            default:
                $doc->cancel($reason)->save();
                break;
        }
        return true;
    }

    public static function updateWarehouseDocModel(WarehouseDoc $record, array $data): WarehouseDoc|Model
    {
        if (self::isDifferentWarehouse($record, $data)) {
            switch ($record->type) {
                case DocumentTypes::PZ:
                case DocumentTypes::PW:
                case DocumentTypes::ZW:
                    $record->log()->update(['warehouse_id' => $data['warehouse_id']]);
                    break;
                default:
                    $record->log()->delete();
                    break;
            }
        }

        if ($data['autogenerate'] ?? false) {
            self::CreateDocNumber($data);
        }

        if (isset($data['autogenerate'])) {
            unset($data['autogenerate']);
        }

        $record->update($data);
        return $record;
    }

    protected static function isDifferentWarehouse(WarehouseDoc $record, array $data): bool
    {
        return (int)$record->getRawOriginal('warehouse_id') !== (int)$data['warehouse_id'];
    }


    public static function acceptDocProcess(WarehouseDoc $model): ?WarehouseDoc
    {

        if (!self::acceptDoc($model)) {
            return null;
        }

        if (!self::updateItemsAfterAccept($model)) {
            self::revertAcceptDoc($model);
            return null;
        }


        if ($model->type === DocumentTypes::MW) {
            if (null === self::makeMPonMWDoc($model)) {
                self::revertAcceptDoc($model);
                return null;
            }
        }
        return $model;
    }


    public static function acceptDoc(WarehouseDoc $model): bool
    {
        try {
            !empty($model->items_issue_date) ?: $model->items_issue_date = now()->format('Y-m-d H:i:s');
            if (empty($model->doc_number)) {
                self::CreateDocNumberOnModel($model);
            }
            $model->accept()->save();
        } catch (\Throwable $e) {
            self::$error = $e->getMessage();
            return false;
        }
        return true;
    }

    public static function revertAcceptDoc(WarehouseDoc $model): bool
    {
        try {
            $model->items_issue_date = null;
            $model->accepted = false;
            $model->accepted_at = null;
            $model->save();
        } catch (\Throwable $e) {
            self::$error = $e->getMessage();
            return false;
        }
        return true;
    }


    public static function updateItemsAfterAccept(WarehouseDoc $model): bool
    {
        if (!WarehouseRepository::updateWarehouseItemsByDoc($model)) {
            self::$error = WarehouseRepository::$error;
            return false;
        }
        return true;
    }


    public static function CreateWarehouseDocItemModel($data, WarehouseDoc|Model $doc, $context = 'filament', bool $reserveItems = true): ?WarehouseLog
    {
        try {
            $hydrated = match ($context) {
                'filament:employee' => self::hydrateFilamentLogModel($data, $doc, $context),
                default => self::hydrateFilamentLogModel($data, $doc)
            };

            $record = new WarehouseLog();
            $record->fill($hydrated);
            $record->save();
        } catch (\Throwable $e) {
            self::$error = $e->getMessage();
            return null;
        }

        if ($reserveItems) {
            WarehouseRepository::makeReservationOnItems(
                $record->warehouse_id,
                $record->source_doc_id,
                $record->transaction_id,
                $record->amount
            );
        }

        return $record;
    }

    public static function RemoveWarehouseDocItemModel(WarehouseLog $record)
    {
        if (null !== $record->source_doc_id) {
            WarehouseRepository::removeReservationOnItems(
                $record->warehouse_id,
                $record->source_doc_id,
                $record->transaction_id
            );
        }
        return $record->delete();
    }

    protected static function hydrateFilamentLogModel($data, WarehouseDoc $doc, $context = 'filament'): array
    {
        $data['installation'] = $doc->installation;
        $data['warehouse_id'] = $doc->warehouse_id;
        $data['type'] = $doc->type->value;
        $data['unit'] = 'szt';
        $data['transaction_id'] = self::CreateDocItemTransactionId($doc);
        $data['document_id'] = $doc->id;
        $data['user_id'] = auth()->user()->id;
        $data['created_at'] = now();

        switch ($doc->type) {
            case DocumentTypes::PZ:
            case DocumentTypes::PW:
            case DocumentTypes::ZW:
                $data['gtin'] = Products::find($data['product_id'])?->gtin;
                break;
            case DocumentTypes::WZ:
            case DocumentTypes::RW:
                $product = WarehouseItem::where('transaction_id', $data['source_doc_id'])
                    ->where('warehouse_id', $doc->warehouse_id)->first();
                $data['gtin'] = $product->gtin;
                $data['product_id'] = $product->product_id;
                if ($context === 'filament:employee') {
                    $data['series_no'] = $product->series_no;
                    $data['exp_date'] = $product->exp_date;
                    $data['price'] = $product->price;
                }
                break;
            case DocumentTypes::MW:
                $product = WarehouseItem::where('transaction_id', $data['source_doc_id'])
                    ->where('warehouse_id', $doc->warehouse_id)->first();
                $data['gtin'] = $product->gtin;
                $data['series_no'] = $product->series_no;
                $data['exp_date'] = $product->exp_date;
                $data['product_id'] = $product->product_id;
                $data['price'] = $product->price;
        }

        return $data;
    }

    public static function UpdateWarehouseDocItem(array $data, WarehouseLog $record)
    {
        switch ($record->type) {
            case DocumentTypes::WZ:
            case DocumentTypes::MW:
            case DocumentTypes::RW:
            case DocumentTypes::MP:
                if ($data['source_doc_id'] ?? null !== $record->source_doc_id || $data['amount'] !== $record->amount) {
                    WarehouseRepository::removeReservationOnItems(
                        $record->warehouse_id,
                        $record->source_doc_id,
                        $record->transaction_id
                    );
                    if ($data['source_doc_id'] ?? null !== $record->source_doc_id) {
                        $product = $record
                            ->warehouse
                            ->items()
                            ->where('transaction_id', $data['source_doc_id'])
                            ->first()
                            ->product;
                        $data['product_id'] = $product->id;
                    }
                }
                unset($data['max_value']);
                $record->update($data);
                WarehouseRepository::removeReservationOnItems(
                    $record->warehouse_id,
                    $record->source_doc_id,
                    $record->transaction_id
                );
                WarehouseRepository::makeReservationOnItems(
                    $record->warehouse_id,
                    $record->source_doc_id,
                    $record->transaction_id,
                    $record->amount
                );
                break;
            default:
                $record->update($data);
                break;
        }

        return $record;
    }

    public static function GetDocByTransactionId($transaction_id): ?WarehouseDoc
    {
        return WarehouseDoc::where('transaction_id', $transaction_id)
            ->with(['log', 'log.product', 'user'])->first();
    }

    public static function CreateDocItemTransactionId(WarehouseDoc $doc): string
    {
        return Identifiers::createTransactionId($doc->type->name . '-');
    }

    public static function CreateDocTransactionId(array $doc): string
    {
        return Identifiers::createTransactionId(DocumentTypes::tryFrom($doc['type'])->name . '-');
    }

    public static function CreateDocNumber(array &$data): void
    {
        $dsp = DocumentSeriesRepository::getDocNumber($data['document_series_id'], new \DateTime($data['doc_date']) ?? now('Europe/Warsaw'));
        $data['doc_number'] = $dsp->getConcreteNumber();
        $data['full_doc_number'] = $dsp->getFullNumber();
    }

    public static function CreateDocNumberOnModel(WarehouseDoc $record): void
    {
        $dsp = DocumentSeriesRepository::getDocNumber($record->document_series_id, new \DateTime($record->doc_date ?? now('Europe/Warsaw')));
        $record->doc_number = $dsp->getConcreteNumber();
        $record->full_doc_number = $dsp->getFullNumber();
    }

    public static function makeMPonMWDoc(WarehouseDoc $mwdoc): ?WarehouseDoc
    {
        $source = $mwdoc->toArray();
        unset($source['id'], $source['created_at'], $source['updated_at'], $source['accepted_at'], $source['log']);
        $source_target_warehouse = $source['target_warehouse_id'];
        $source_warehouse = $source['warehouse_id'];
        $source_signatory = $source['signatory_id'];
        $source['type'] = DocumentTypes::MP;
        $source['document_series_id'] = DocumentSeriesRepository::getDefaultSeriesForDocType(
            DocumentTypes::MP
        )->id;
        self::CreateDocNumber($source);
        $target_transaction_id = self::swapTRIDMWMP($source['transaction_id']);
        $source['warehouse_id'] = $source_target_warehouse;
        $source['target_warehouse_id'] = $source_warehouse;
        $source['user_id'] = $source_signatory;
        $source['transaction_id'] = $target_transaction_id;
        $source['items_issue_date'] = null;
        $source['accepted'] = false;
        $target_doc = self::createWarehouseDocModel($source);
        if (null === $target_doc) {
            return null;
        }

        /**
         * @var WarehouseLog $log
         */
        foreach ($mwdoc->log as $log) {
            $source_log = $log->toArray();
            $new_log = new WarehouseLog();
            unset($source_log['id'], $source_log['created_at']);
            $source_log['type'] = DocumentTypes::MP;
            $source_log['warehouse_id'] = $target_doc->warehouse_id;
            $source_log['transaction_id'] = self::swapTRIDMWMP($source_log['transaction_id']);
            $source_log['document_id'] = $target_doc->id;
            $new_log->fill($source_log);
            $new_log->save();
            WarehouseRepository::switchReservationOnItems($log->transaction_id, $new_log->transaction_id);
        }

        return $target_doc;
    }

    public static function swapTRIDMWMP($transaction_id): string
    {
        return str_replace('MW-', DocumentTypes::MP->name . '-', $transaction_id);
    }

    public static function swapTRIDMPMW($transaction_id): string
    {
        return str_replace('MP-', DocumentTypes::MW->name . '-', $transaction_id);
    }
}
