<?php

namespace App\Repositories;

use App\Enums\BarcodeTypes;
use App\Helpers\Barcodes;
use App\Helpers\Identifiers;
use App\Models\DrugSource;
use App\Models\Products;
use Illuminate\Database\Eloquent\Builder;

class ProductsRepository
{

    public static string $errors = '';

    public static array $dm_ais_map = [
        '01' => 'gtin',
        '10' => 'series_no',
        '17' => 'exp_date',
        '21' => 'series_no',
    ];


    public static function createFromModalForm($data): Products
    {

        $data = self::mutateFormDataBeforeCreate($data);
        $product = new Products();
        $product->fill($data);
        $product->save();
        return $product;
    }

    public static function createFromDrugSource($id): Products
    {
        $drug = DrugSource::find($id);
        $data['name'] = $drug->long_name;
        $data['grace_period'] = $drug->json['karencja'];
        $data['gtin'] = $drug->gtin;
        $data['ext_link'] = $drug->json['link']['charakterystyka'];
        $data['data_source'] = DrugSource::class;
        $data['data_source_identifier'] = $drug->system_id;
        $data = self::mutateFormDataBeforeCreate($data);
        $product = new Products();
        $product->fill($data);
        $product->save();
        return $product;
    }

    public static function mutateFormDataBeforeCreate(array $data): array
    {
        $data['installation'] = auth()->user()?->installation() ?? 0;
        $data['hash'] = Identifiers::getRandomHash();
        $data['is_active'] = true;
        return $data;
    }


    public static function modifyQueryByBarcode(Builder $query, $barcode): Builder
    {
        $barcode = trim($barcode);
        switch (Barcodes::identify($barcode)) {
            default:
                $query->where('gtin', $barcode);
                break;
            case BarcodeTypes::DATAMATRIX:
                $bc = new Barcodes();
                $ais = $bc->parseDataMatrix($barcode, Barcodes::$data_matrix_fnck);
                if (null === $ais) {
                    self::$errors = $bc->errors;
                    break;
                }
                if (!empty($ais['01'])) {
                    $query->where(self::$dm_ais_map['01'], ltrim($ais['01'], '0'));
                }
                if (!empty($ais['10'])) {
                    $query->where(self::$dm_ais_map['10'], $ais['10']);
                }
                if (!empty($ais['17'])) {
                    try {
                        $date = Barcodes::parseDate($ais['17']);
                    } catch (\Exception) {
                        break;
                    }
                    $query->where(self::$dm_ais_map['17'], $date->format('Y-m-d'));
                }
                break;
        }

        return $query;
    }
}
