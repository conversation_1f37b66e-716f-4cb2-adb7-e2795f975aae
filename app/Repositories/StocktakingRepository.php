<?php

namespace App\Repositories;

use App\Filament\Filters\WHSelectFilter;
use App\Helpers\Identifiers;
use App\Interface\StocktakingInterface;
use App\Models\Stocktaking;
use App\Models\StocktakingItems;
use App\Models\WarehouseItem;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;

//use MongoDB\Laravel\Eloquent\Model;

class StocktakingRepository
{
    protected $hash = null;
    protected $date = null;
    protected int $warehouse;

    protected $expected_value = 0;

    protected array $data = [];

    //model::class
    protected ?StocktakingInterface $model;

    protected array $items = [];

    protected ?Stocktaking $stocktaking_model;

    /*
     * FIELDS
     * accepted
     * real_amount
     *
     */

    public function __construct()
    {
    }

    protected function getData(Stocktaking $stocktaking)
    {
        return WarehouseItem::where('warehouse_id', $stocktaking->warehouse_id)
            ->groupBy('warehouse_id')
            ->groupBy('product_id')
            ->groupBy('series_no')
            ->groupBy('transaction_id')
            ->selectRaw('*, SUM(amount) as total, SUM(price) as expected')
            ->get();
    }


    protected function createModel(array $formatted)
    {
        $item = new StocktakingItems($formatted);
        $item->save();
        return $item->fresh();
    }

    public function createItems(Stocktaking $stocktaking)
    {
        $data = $this->getData($stocktaking);
        foreach ($data as $entry) {
            $formatted = [
                'hash' => Identifiers::createTransactionId(null, true),
                'stocktaking_id' => $stocktaking->id,
                'product_id' => $entry->product_id,
                'gtin' => $entry->gtin,
                'series_no' => $entry->series_no,
                'base_amount' => $entry->total,
                'real_amount' => 0,
                'difference_amount' => $entry->total * -1,
                'price' => $entry->price,
                'expected_value' => $entry->expected
            ];

            $this->items[] = $this->createModel($formatted);
            $this->expected_value += $formatted['expected_value'];
        }

        return $this->items;
    }

    public function update($id, $value)
    {
        $this->model->updateEntry($id, $value);
    }

    public function createStockTaking(array $data): Stocktaking
    {
        $model = new Stocktaking();
        $model->warehouse_id = WHSelectFilter::deconstructCompoundIndex($data['warehouse'])['id'];
        $model->user_id = $data['user'] ?? null;
        $model->hash = Identifiers::createTransactionId();
        $model->creator_id = auth()->user()->id;
        $model->installation = auth()->user()->installation();
        $model->for_date = now();
        $model->save();
        $model->refresh();
        return $model;
    }

    public function createStockTakingWithItems(array $data): Stocktaking
    {
        $st_model = $this->createStockTaking($data);
        $items = $this->createItems($st_model);
        $st_model->setRelation('items', $items);
        $st_model->expected_value = $this->expected_value;
        $st_model->save();
        return $st_model;
    }

    public function finishStockTaking(int|Stocktaking $record): bool
    {
        if (is_int($record)) {
            $record = \App\Models\Stocktaking::where('hash', $record)->first();
            if (empty($record)) {
                return false;
            }
        }

        $real_value = 0;

        /**
         * @var StocktakingItems $item
         */
        foreach ($record->items as $item) {
            $item->acceptStockItem();
            $real_value += $item->real_value;
        }

        $record->accept($real_value);
        return $record->save();
    }

    public function removeStockTaking(int|Stocktaking $record): bool
    {
        if (is_int($record)) {
            $record = \App\Models\Stocktaking::where('id', $record)->first();
            if (empty($record)) {
                return false;
            }
        }

        $record->items()->delete();
        return $record->delete();
    }
}
