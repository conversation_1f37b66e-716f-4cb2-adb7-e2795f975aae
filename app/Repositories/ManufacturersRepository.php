<?php

namespace App\Repositories;

use App\Helpers\Identifiers;
use App\Models\Manufacturer;

class ManufacturersRepository
{
    public static $error;

    public static function CreateFromModalForm($data): null|Manufacturer
    {
        $data = self::mutateFormDataBeforeCreate($data);
        $manufacturer = new Manufacturer($data);
        try{
            $manufacturer->save();
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return null;
        }
        return $manufacturer;
    }

    public static function mutateFormDataBeforeCreate(array $data): array
    {
        $data['installation'] = auth()->user()?->installation() ?? 0;
        $data['hash'] = Identifiers::getRandomHash();
        $data['is_active'] = true;
        return $data;
    }
}
