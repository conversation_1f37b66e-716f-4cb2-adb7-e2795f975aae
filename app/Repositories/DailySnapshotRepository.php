<?php


namespace App\Repositories;

use App\Models\DailySnapshots;
use App\Models\WarehouseItem;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DailySnapshotRepository
{
    protected null|Model $model = null;
    protected string $storage_path = 'snapshots/';

    /**
     * @var string $storage_dirs tenant_id || flat
     */
    protected string $storage_dirs = 'tenant_id';

    protected string $storage = 'file';

    protected string $dateFormat = 'Ymd';

    public function __construct(null|Model|DailySnapshots $model = null)
    {
        $this->model = $model;
    }

    public function setDirsByTenant(): static
    {
        $this->storage_dirs = 'tenant_id';
        return $this;
    }

    public function setDirsFlat(): static
    {
        $this->storage_dirs = 'flat';
        return $this;
    }

    public function getFormattedDate(null|\DateTimeInterface $date): string
    {
        return match ($date) {
            null => Carbon::today()->format($this->dateFormat),
            default => $date?->format($this->dateFormat)
        };
    }

    public function save(): bool
    {
        return $this->storage()->put();
    }

    public function prepareData(int $tenant_id): array
    {
        $currents = WarehouseItem::with(['product'])
            ->selectRaw('*, SUM(amount) AS total')
            ->where('installation', $tenant_id)
            ->groupBy('product_id')
            ->groupBy('warehouse_id')
            ->groupBy('series_no')
            ->get();
        $documentArray = [];
        foreach ($currents as $result) {
            $documentArray['products'][] = [
                'product_id' => $result->product_id,
                'product_hash' => $result->product->hash,
                'product_name' => $result->product->name,
                'amount' => $result->total,
                'gtin' => $result->gtin,
                'series_no' => $result->series_no,
                'warehouse' => $result->warehouse_id
            ];
        }
        return $documentArray;
    }

    /**
     * @throws \JsonException
     */
    public function getSnapshot(\DateTimeInterface $date, int $tenant_id): ?DailySnapshots
    {
        $data = $this->storage()->get($date, $tenant_id);
        if (null === $data) {
            return null;
        }
        try {
            $data = json_decode($data, true, 512, JSON_THROW_ON_ERROR);
        } catch (\JsonException $e) {
            return null;
        }

        return new DailySnapshots($data);
    }

    public function checkSnapshot(string|\DateTimeInterface $for_date, int $tenant_id): bool
    {
        return $this->storage()->exists($for_date, $tenant_id);
    }

    public function prepareModel(array $data, \DateTimeInterface $date, int $tenant_id): self
    {
        $this->model ?? $this->model = new DailySnapshots();
        $this->model->for_date = $date;
        $this->model->tenant = $tenant_id;
        $this->model->data = $data;

        return $this;
    }

    public function getModel(): null|Model|DailySnapshots
    {
        return $this->model;
    }

    protected function storage(): static
    {
        return $this;
    }


    protected function get(\DateTimeInterface $for_date, int $tenant_id): null|string
    {
        return match ($this->exists($for_date, $tenant_id)) {
            false => null,
            default => Storage::disk('local')
                ->get($this->getFilePath($this->getRecordName($for_date, $tenant_id), $tenant_id))
        };
    }

    /**
     * @throws \Exception
     */
    protected function put(?Model $record = null): bool
    {
        $model = $record ?? $this->model;

        if (null === $model) {
            throw new \Exception('No model provided');
        }

        return Storage::disk('local')
            ->put(
                $this->getFilePath(
                    $this->getRecordName($model->for_date, $model->tenant),
                    $model->tenant
                ),
                $model->toJson()
            );
    }


    protected function exists(\DateTimeInterface $for_date, int $tenant_id): bool
    {
        return Storage::disk('local')
            ->exists($this->getFilePath($this->getRecordName($for_date, $tenant_id), $tenant_id));
    }

    protected function getFilePath(string $name, int $tenant_id): string
    {
        return match ($this->storage_dirs) {
            default => Str::finish($this->storage_path, '/') . $name,
            'tenant_id' => Str::finish($this->storage_path, '/') . $tenant_id . '/' . $name
        };
    }


    protected function getStorageFilePath(string $name, ?int $tenant_id = null): string
    {
        return Storage::disk('local')->path($this->getFilePath($name, $tenant_id));
    }

    public function getRecordName(\DateTimeInterface $for_date, int $tenant_id): string
    {
        return match ($this->storage_dirs) {
            'tenant_id' => $for_date->format($this->dateFormat) . '.json',
            default => $tenant_id . '_' . $for_date->format($this->dateFormat) . '.json'
        };
    }
}
