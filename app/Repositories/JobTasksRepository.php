<?php

namespace App\Repositories;

use App\Enums\DocumentTypes;
use App\Events\JobTaskFinished;
use App\Models\JobTask;
use App\Models\JobTaskItem;
use App\Models\WarehouseDoc;
use App\Models\WarehouseItem;
use App\Models\WarehouseLog;
use Illuminate\Database\Eloquent\Model;

class JobTasksRepository
{

    public static $error = '';

    public static function removeJobTask(JobTask $record): bool
    {
        $record->items->each(function (JobTaskItem $item) {
            self::removeJobTaskItem($item);
        });
        return $record->delete();
    }

    public static function removeJobTaskItem(JobTaskItem $record): void
    {
        $record->removeDemand();
        $record->delete();
    }

    public static function removeProductDemandForJobTaskItems(JobTask $record): void
    {
        $record->items->each(function (JobTaskItem $item) {
            $item->removeDemand();
        });
    }

    public static function finishJobTask(JobTask|Model $record, array $data): JobTask
    {
        $createRWDoc = $data['create_rw_doc'] ?? false;
        if ($createRWDoc) {
            self::createRWDocument($record, $data);
        }
        unset($data['create_rw_doc']);
        $record->finishJobTask($data['comment'] ?? null);
        self::removeProductDemandForJobTaskItems($record);
        event(new JobTaskFinished($record));
        return $record;
    }

    public static function createRWDocument(JobTask $record, array $data): bool
    {
        $data = self::mutateFilamentDataOnRWDocCreate($record, $data);
        WarehouseDocsRepository::CreateDocNumber($data);
        $rwdoc = WarehouseDocsRepository::createWarehouseDocModel($data);
        if (null === $rwdoc) {
            return false;
        }
        self::createRWDocumentLogs($record, $rwdoc);
        WarehouseDocsRepository::acceptDocProcess($rwdoc);
        return true;
    }

    public static function mutateFilamentDataOnRWDocCreate(JobTask $record, array $data): array
    {
        $rwdata['installation'] = $record->installation;
        $rwdata['user_id'] = $record->executed_by;
        $rwdata['type'] = DocumentTypes::RW->value;
        $rwdata['issuer_data'] = auth()->user()->getTenant()->getAddressText();
        $rwdata['transaction_id'] = WarehouseDocsRepository::CreateDocTransactionId($rwdata);
        $rwdata['warehouse_id'] = (int)$record->executor->warehouse->id;
        $rwdata['document_series_id'] = DocumentSeriesRepository::getDefaultSeriesForDocType(
            DocumentTypes::RW
        )->id;
        $rwdata['doc_date'] = now('Europe/Warsaw')->format('Y-m-d');
        $rwdata['partner_id'] = 0;
        $rwdata['partner_data'] = $rwdata['issuer_data'];
        $rwdata['notes'] = $data['doc_notes'];
        $rwdata['related_doc'] = $data['doc_related_doc'];
        return $rwdata;
    }

    public static function createRWDocumentLogs(JobTask $jobTask, WarehouseDoc $rwDoc): bool
    {
        $items = $jobTask->items;
        $warehouse = $jobTask->executor->warehouse;
        foreach ($items as $item) {
            $products = WarehouseItem::where('warehouse_id', $warehouse->id)
                ->where('product_id', $item->product_id)
                ->isReserved(false)
                ->get();
            if ($products->count() < $item->quantity) {
                self::$error = 'Niewystarczająca ilość produktu: ' . $item->product_id;
                return false;
            }
            $product = $products->first();
            $data['type'] = $rwDoc->type->value;
            $data['warehouse_id'] = $rwDoc->warehouse_id;
            $data['product_id'] = $product->product_id;
            $data['amount'] = $item->quantity;
            $data['unit'] = $item->quantity_unit;
            $data['price'] = $product->price;
            $data['gtin'] = $product->gtin;
            $data['series_no'] = $product->series_no;
            $data['transaction_id'] = WarehouseDocsRepository::CreateDocItemTransactionId($rwDoc);
            $data['document_id'] = $rwDoc->id;
            $data['source_doc_id'] = $product->transaction_id;
            $data['exp_date'] = $product->exp_date;
            $data['user_id'] = $rwDoc->id;
            $data['created_at'] = now();
            $data['installation'] = $rwDoc->installation;
            $record = new WarehouseLog($data);
            $record->saveOrFail();
            WarehouseRepository::makeReservationOnItems(
                $record->warehouse_id,
                $record->source_doc_id,
                $record->transaction_id,
                $record->amount
            );
        }

        return true;
    }
}
