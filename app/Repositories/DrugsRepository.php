<?php

namespace App\Repositories;

use App\Models\DrugSource;
use Filament\Actions\StaticAction;
use Filament\Forms\ComponentContainer;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Set;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Livewire\Form;

class DrugsRepository
{

    protected $storage_path = 'drugs/';

    /**
     * @var string $storage 'db' || 'file'
     */
    public string $storage = 'db';

    public bool $storage_file_replica = true;

    public string $import_type = 'overall';
    protected array $imported_drugs = [];
    protected string $downloaded_file_path = '';

    protected Http $remoteHandler;

    public function __construct(Http $remoteHandler)
    {
        $this->remoteHandler = $remoteHandler;
    }

    public array $info = [
        'processed' => 0,
        'saved' => 0,
    ];

    public string $error = '';

    public function fetchRemoteFile(): null|string
    {
        $url = match ($this->import_type) {
            'incremental' => config('app.drug.import.url.incremental'),
            default => config('app.drug.import.url.overall'),
        };
        $response = $this->remoteHandler::get($url);
        if ($response->failed()) {
            $this->error = $response->status() . ': ' . $response->reason();
            return null;
        }
        [$date, $version] = Str::of($response->header("Content-Disposition"))
            ->match("/\d{8}_\d\.\d\.\d/")->split('/_/');
        $name = $this->getFetchedFileName($date, $version);
        $this->downloaded_file_path = $this->storage_path . $name;
        return match (Storage::disk('local')->put($this->downloaded_file_path, $response->body())) {
            true => $this->downloaded_file_path,
            default => null,
        };
    }


    /**
     * @throws \Exception
     */
    public function processXMLFile($filename): array
    {
        $path = Storage::disk('local')->path($filename);
        if (!file_exists($path)) {
            throw new \Exception('File not found');
        }
        $h = file($path, FILE_SKIP_EMPTY_LINES);
        $product = '';
        $capture = false;
        $items = 0;
        foreach ($h as $idx => $line) {
            $ll = ltrim($line);

            if (!$capture) {
                if (!str_contains($ll, '<produktLeczniczy')) {
                    continue;
                }

                if (str_contains($ll, 'rodzajPreparatu="weterynaryjny"')) {
                    $capture = true;
                    $product .= $ll;
                    $items++;
                    continue;
                }
                continue;
            }

            if (str_contains($ll, '</produktLeczniczy>')) {
                $capture = false;
                $this->parseProduct($product . $ll);
                $product = '';
                continue;
            }

            $product .= $ll;
        }

        $this->info['processed'] = $items;
        $this->info['saved'] = count($this->imported_drugs);
        return $this->imported_drugs;
    }

    protected function parseProduct($line): void
    {
        $start = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>' . PHP_EOL;
        //phpcs:ignore
        $start .= '<produktyLecznicze stanNaDzien="2024-04-25" xmlns="http://rejestry.ezdrowie.gov.pl/rpl/eksport-danych-v4.0.0">' . PHP_EOL;
        $stop = '</produktyLecznicze>';
        $dom = new \DOMDocument(encoding: 'utf8');
        $dom->loadXML($start . $line . $stop);
        $product = $dom->getElementsByTagName('produktLeczniczy')[0];
        $dsc = [];
        $dsc['nazwa'] = $product->getAttribute('nazwaProduktu');
        $dsc['link']['ulotka'] = $product->getAttribute('ulotka');
        $dsc['link']['charakterystyka'] = $product->getAttribute('charakterystyka');
        $dsc['system_id'] = $product->getAttribute('id');
        $dsc['podmiotOdpowiedzialny'] = $product->getAttribute('podmiotOdpowiedzialny');
        $kody_atc = $product->getElementsByTagName('kodyATC')[0];
        foreach ($kody_atc->getElementsByTagName('kodATC') as $kod) {
            $dsc['atc'] = $kod->nodeValue;
        }
        $drogi_podania = $product->getElementsByTagName('drogiPodania')[0];
        $opakowania = $product->getElementsByTagName('opakowania')[0];
        $dp_line = '';
        foreach ($drogi_podania->getElementsByTagName('drogaPodania') as $droga_podania) {
            $dp_str = $droga_podania->getAttribute('drogaPodaniaNazwa') . ':' . PHP_EOL;
            foreach ($droga_podania->getElementsByTagName('gatunki') as $gatunki) {
                foreach ($gatunki->getElementsByTagName('gatunek') as $gatunek) {
                    $gat_str = '-' . $gatunek->getAttribute('nazwaGatunku') . ':' . PHP_EOL;
                    foreach ($gatunek->getElementsByTagName('okresyKarencji') as $okresy_kar) {
                        foreach ($okresy_kar->getElementsByTagName('okresKarencji') as $okres_kar) {
                            $tk_string = '--' . $okres_kar->getAttribute('nazwaTkanki') .
                                ': ' . $okres_kar->getAttribute('wartoscMiary') . ' ' .
                                $okres_kar->getAttribute('jednostkaMiary') . PHP_EOL;
                            $gat_str .= $tk_string;
                        }
                    }
                    $dp_str .= $gat_str;
                }
            }
            $dp_line .= $dp_str;
        }
        $subst_czynne = $product->getElementsByTagName('substancjeCzynne')[0] ?? [];
        $dsc['substancje_czynne'] = [];
        foreach ($subst_czynne->getElementsByTagName('substancjaCzynna') ?? [] as $subst) {
            $inny = $subst->getAttribute('innyOpisIlosci');
            if (empty($inny)) {
                $inny = $subst->getAttribute('iloscSubstancji') . ' '
                    . $subst->getAttribute('jednostkaMiaryIlosciSubstancji');
                $ilprep = $subst->getAttribute('iloscPreparatu');
                if (!empty($ilprep)) {
                    $inny .= ' / ' . $ilprep . ' ' . $subst->getAttribute('jednostkaMiaryIlosciPreparatu');
                }
            }

            $dsc['substancje_czynne'][$subst->getAttribute('nazwaSubstancji')] = $inny;
        }
        $dsc['karencja'] = $dp_line;
        $dsc['opakowania'] = [];
        foreach ($opakowania->getElementsByTagName('opakowanie') as $opakowanie) {
            if ($opakowanie->getAttribute('skasowane') !== 'NIE') {
                continue;
            }
            $gtin = ltrim($opakowanie->getAttribute('kodGTIN'), '0');
            foreach ($opakowanie->getElementsByTagName('jednostkiOpakowania') as $jki_opakowania) {
                foreach ($jki_opakowania->getElementsByTagName('jednostkaOpakowania') as $jka_opakowania) {
                    $dsc['opakowania'][$gtin] = [
                        'pojemnosc' => $jka_opakowania->getAttribute('pojemnosc'),
                        'jednostka_poj' => $jka_opakowania->getAttribute('jednostkaPojemnosci'),
                        'rodzaj_op' => $jka_opakowania->getAttribute('rodzajOpakowania'),
                        'liczba_op' => $jka_opakowania->getAttribute('liczbaOpakowan'),
                    ];
                }
            }
        }
        $this->imported_drugs[] = $dsc;
    }

    /**
     * @throws \Exception
     */
    public function importFromJson(): void
    {
        $file = $this->getResultFileName();
        if (!Storage::exists($this->storage_path . $file)) {
            throw new \Exception('File not found ' . $file);
        }

        $this->imported_drugs = json_decode(Storage::disk('local')->get($this->storage_path . $file), true);
        $this->info['processed'] = $this->info['saved'] = count($this->imported_drugs);
    }

    public function putToStorage(): void
    {
        if ($this->storage === 'file' || $this->storage_file_replica) {
            $name = $this->getResultFileName();
            Storage::disk('local')
                ->put($this->storage_path . $name, json_encode($this->imported_drugs, JSON_PRETTY_PRINT));
        }

        if ($this->storage === 'db') {
            if ($this->import_type === 'incremental') {
                $this->putToDbIncrementalRecords();
            } else {
                $this->putToDbOverallRecords();
            }
        }
    }

    protected function putToDbOverallRecords(): void
    {
        DrugSource::truncate();
        foreach ($this->imported_drugs as $drug) {
            foreach ($drug['opakowania'] as $gtin => $data) {
                $this->createFromImported($gtin, $drug, $data);
            }
        }
    }

    protected function putToDbIncrementalRecords(): void
    {
        foreach ($this->imported_drugs as $drug) {
            DrugSource::where('system_id', $drug['system_id'])->delete();

            foreach ($drug['opakowania'] as $gtin => $data) {
                $this->createFromImported($gtin, $drug, $data);
            }
        }
    }

    protected function createFromImported($gtin, $drug, $longNameExtraData): DrugSource
    {
        $record = new DrugSource();
        $record->name = $drug['nazwa'];
        $record->system_id = $drug['system_id'];
        $record->gtin = $gtin;
        $record->json = $drug;
        $record->setLongName(...$longNameExtraData);
        $record->save();
        return $record;
    }

    public function clearTempFiles(): null|bool
    {
        return match ($this->downloaded_file_path === "") {
            true => null,
            default => Storage::disk('local')->delete($this->downloaded_file_path),
        };
    }

    public function getInfoString()
    {
        return implode(
            ", ",
            array_map(
                fn($key, $value): string => ucfirst($key) . ": {$value}",
                array_keys($this->info),
                $this->info
            )
        );
    }


    public function getResultFileName(): string
    {
        return 'drugs_' . $this->import_type . '.json';
    }

    public function getStorageFilePath(): string
    {
        return $this->storage_path;
    }


    public function getFetchedFileName($date, $version): string
    {
        return $this->import_type . "_{$date}_{$version}.xml";
    }


    public static function getImportAction($type = 'suffix'): StaticAction
    {

        switch ($type) {
            default:
            case 'suffix':
                $action = Action::make('import')
                    ->action(static function (
                        Action $action,
                        array $arguments,
                        Select $component,
                        array $data,
                        ComponentContainer $form
                    ) {
                        if (!filled($data['drugs'])) {
                            return;
                        }
                        $product = ProductsRepository::createFromDrugSource($data['drugs']);
                        $component->state($product->id);
                        $component->callAfterStateUpdated();
                    })
                    ->hiddenLabel();
                break;
            case 'header':
                $action = \Filament\Actions\Action::make('import')
                    ->action(static function (\Filament\Actions\Action $action, array $arguments, array $data) {
                        if (!filled($data['drugs'])) {
                            return;
                        }
                        ProductsRepository::createFromDrugSource($data['drugs']);
                    })
                    ->label('Import z katalogu leków');
                break;
        }

        $action->icon('heroicon-o-arrow-down-tray')
            ->form([
                Select::make('drugs')
                    ->label('Wyszukaj preparat')
                    ->options(
                        fn() => DrugSource::selectRaw('id, CONCAT(long_name, \', \', gtin) as long_name_gtin')
                            ->pluck('long_name_gtin', 'id')
                    )
                    ->searchable()
                    ->required()
                    ->searchingMessage('Szukam....')
                    ->getSearchResultsUsing(function (string $search) {
                        return DrugSource::where('name', 'LIKE', "%{$search}%")
                            ->orWhere('gtin', 'LIKE', "%{$search}%")
                            ->selectRaw('id, CONCAT(long_name, \', \', gtin) as long_name_gtin')
                            ->limit(50)
                            ->pluck('long_name_gtin', 'id')
                            ->toArray();
                    })
                    ->getOptionLabelUsing(
                        fn($value) => DrugSource::selectRaw('id, CONCAT(long_name, \', \', gtin) as long_name_gtin')
                            ->find($value)->long_name_gtin
                    )
                    ->live()
                    ->afterStateUpdated(function (Set $set, $state) {
                        if (!filled($state)) {
                            return null;
                        }
                        $drug = DrugSource::find($state);
                        $set('karencja', $drug->json['karencja']);
                        $set('gtin', $drug->gtin);
                        $set(
                            'sub_czynne',
                            implode(
                                "\n",
                                array_map(
                                    static fn($n, $d) => $n . ': ' . $d,
                                    array_keys($drug->json['substancje_czynne'] ?? []),
                                    $drug->json['substancje_czynne'] ?? []
                                )
                            )
                        );
                    })
                    ->preload(),
                TextInput::make('gtin')
                    ->readOnly(true)
                    ->dehydrated(false)
                    ->label('GTIN'),
                Textarea::make('karencja')
                    ->rows(6)
                    ->readOnly(true)
                    ->dehydrated(false)
                    ->label('Karencja'),
                Textarea::make('sub_czynne')
                    ->readOnly(true)
                    ->rows(6)
                    ->dehydrated(false)
                    ->label('Substancje czynne')
            ]);

        return $action;
    }
}
