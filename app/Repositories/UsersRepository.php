<?php

namespace App\Repositories;

use App\Helpers\Identifiers;
use App\Models\User;

class UsersRepository
{
    public static function deleteUserWithRelations(User|int $user): bool
    {
        if (is_numeric($user) && null === $user = User::find($user)) {
            return false;
        }

        return $user->deleteQuietly();
    }

    public static function deleteTenantUser(User|int $user): bool
    {
        if (is_numeric($user) && null === $user = User::find($user)) {
            return false;
        }
        $fake = Identifiers::getRandomHash(8);
        $profile = [
            'name' => $fake,
            'surname' => $fake,
            'adress' => $fake,
            'number' => $fake
        ];
        $user->profile()->update($profile);
        return $user->deleteQuietly();
    }
}
