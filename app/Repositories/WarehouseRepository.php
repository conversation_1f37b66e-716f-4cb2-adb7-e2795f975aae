<?php

namespace App\Repositories;

use App\Enums\DocumentTypes;
use App\Enums\WarehouseTypes;
use App\Models\Products;
use App\Models\User;
use App\Models\Warehouse;
use App\Models\WarehouseDoc;
use App\Models\WarehouseItem;
use App\Models\WarehouseLog;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class WarehouseRepository
{

    public static $error = '';

    protected static $createdItems = [];

    public static function canDocBeRemoved(WarehouseDoc $doc): bool
    {
        return WarehouseDoc::where('type', $doc->type->value)
                ->where('document_series_id', $doc->document_series_id)
                ->whereGt('doc_number', $doc->doc_number)
                ->count() === 0;
    }

    public static function updateWarehouseItemsByDoc(WarehouseDoc $doc, int $target_warehouse = null): bool
    {
        switch ($doc->type) {
            case DocumentTypes::PZ:
            case DocumentTypes::PW:
            case DocumentTypes::ZW:
                return self::updateWarehouseItemsByPZ($doc);
            case DocumentTypes::WZ:
            case DocumentTypes::RW:
                return self::updateWarehouseItemsByWZ($doc);
            case DocumentTypes::MW:
                return self::updateWarehouseItemsByMW($doc);
            case DocumentTypes::MP:
                return self::updateWarehouseItemsByMP($doc);
        }

        return false;
    }


    public static function updateWarehouseItemsByPZ(WarehouseDoc $doc): bool
    {

        try {
            DB::transaction(static function () use ($doc) {
                foreach ($doc->log as $log) {
                    /**
                     * @var WarehouseLog $log
                     */
                    $data = $log->toArray();
                    unset($data['id'], $data['type'], $data['created_at'], $data['user_id'], $data['unit']);
                    $data['amount'] = 1;
                    $data['minimum_stock'] = $log->product->minimum_stock;
                    $data['minimum_exp_date'] = $log->product->minimum_exp_date;
                    for ($a = 0; $a < $log->amount; $a++) {
                        $data['hash'] = \App\Helpers\Identifiers::getRandomHash();
                        $item = new WarehouseItem();
                        $item->fill($data);
                        $item->save();
                        self::$createdItems[] = $item;
                    }
                }
            });
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            self::$createdItems = [];
            return false;
        }
        return true;
    }

    public static function updateWarehouseItemsByWZ(WarehouseDoc $doc): bool
    {
        /**
         * @var WarehouseLog $log
         */
        try {
            DB::transaction(static function () use ($doc) {
                foreach ($doc->log as $log) {
                    $log->warehouse->items()
                        ->where('transaction_id', $log->source_doc_id)
                        ->where('reserved', true)
                        ->where('reserved_by', $log->transaction_id)->delete();
                }
            });
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
        return true;
    }

    public static function updateWarehouseItemsWarnings(
        Products|Model $product,
        bool $expu = false,
        ?int $expv = null,
        bool $lstocku = false,
        ?int $lstockv = null
    ) {
        if (!$expu && !$lstocku) {
            return;
        }

        if ($expu) {
            $update['minimum_exp_date'] = $expv;
        }

        if ($lstocku) {
            $update['minimum_stock'] = $lstockv;
        }

        WarehouseItem::where('product_id', $product->id)->update($update);
    }

    public static function updateWarehouseItemsByMW(WarehouseDoc $doc): bool
    {
        return true;
    }

    public static function updateWarehouseItemsByMP(WarehouseDoc $doc): bool
    {
        /**
         * @var WarehouseLog $log
         */
        DB::enableQueryLog();
        try {
            DB::transaction(static function () use ($doc) {
                foreach ($doc->log as $log) {
                    WarehouseItem::query()
                        ->where('transaction_id', $log->source_doc_id)
                        ->where('reserved', true)
                        ->where('reserved_by', $log->transaction_id)
                        ->where('warehouse_id', $doc->target_warehouse_id)
                        ->update(['warehouse_id' => $doc->warehouse_id]);
                    self::removeReservationOnItems($doc->warehouse_id, $log->source_doc_id, $log->transaction_id);
                }
            });
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
        return true;
    }


    public static function switchReservationOnItems(
        string $old_reserved_by,
        string $reserved_by,
        ?int $limit = null
    ): int {
        $qry = WarehouseItem::query()
            ->where('reserved_by', $old_reserved_by);

        $limit === null ?: $qry->limit($limit);

        return $qry->update([
            'reserved_by' => $reserved_by,
        ]);
    }

    public static function makeReservationOnItems(
        int $warehouse_id,
        $item_transaction_id,
        $reserved_by,
        $quantity = 1
    ): int {
        return WarehouseItem::query()
            ->where('transaction_id', $item_transaction_id)
            ->where('warehouse_id', $warehouse_id)
            ->where('reserved', false)
            ->limit($quantity)
            ->update([
                'reserved' => true,
                'reserved_by' => $reserved_by,
            ]);
    }


    public static function removeReservationOnItems(
        int $warehouse_id,
        string $item_transaction_id,
        string $reserved_by,
        ?int $limit = null
    ): int {
        $qry = WarehouseItem::query()
            ->where('transaction_id', $item_transaction_id)
            ->where('reserved_by', $reserved_by)
            ->where('warehouse_id', $warehouse_id);

        $limit === null ?: $qry->limit($limit);

        return $qry->update([
            'reserved' => false,
            'reserved_by' => null,
        ]);
    }

    public static function createUserWarehouse(User|Model $user, $data): ?Warehouse
    {
        try {
            $wh = new Warehouse();
            $wh->name = $data['name'];
            $wh->owner_type = WarehouseTypes::USER->value;
            $wh->owner_identifier = $user->id;
            $wh->is_active = true;
            $wh->installation = auth()->user()->installation();
            $wh->save();
            return $wh;
        } catch (\Throwable $e) {
            self::$error = $e->getMessage();
            return null;
        }
    }


    public static function removeWarehouse(Model|Warehouse $warehouse): bool
    {
        return match ($warehouse->owner_type) {
            WarehouseTypes::COMPANY => self::removeTenantWarehouse($warehouse),
            WarehouseTypes::USER => self::removeUserWarehouse($warehouse),
            default => false,
        };
    }

    public static function removeUserWarehouse(Warehouse $warehouse): bool
    {
        try {
            $warehouse->items()->delete();
            if ($warehouse->docs()->count() > 0 || $warehouse->docsWhenTargetWh()->count() > 0) {
                $warehouse->delete();
            } else {
                $warehouse->forceDelete();
            }
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }

        return true;
    }

    public static function removeTenantWarehouse(Warehouse $warehouse): bool
    {
        try {
            $warehouse->items()->delete();
            if ($warehouse->docs()->count() > 0 || $warehouse->docsWhenTargetWh()->count() > 0) {
                $warehouse->delete();
            } else {
                $warehouse->forceDelete();
            }
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }

        return true;
    }
}
