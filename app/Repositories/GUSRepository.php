<?php

namespace App\Repositories;

use <PERSON><PERSON><PERSON>\Exception\InvalidReportTypeException;
use <PERSON><PERSON><PERSON>\Exception\InvalidUserKeyException;
use <PERSON><PERSON><PERSON>\Exception\NotFoundException;
use <PERSON><PERSON><PERSON>\GusApi;
use <PERSON><PERSON><PERSON>\ReportTypes;
use <PERSON><PERSON><PERSON>\SearchReport;
use <PERSON><PERSON><PERSON>\Type\Response\SearchResponseCompanyData;
use Illuminate\Support\Facades\Log;

class GUSRepository
{
    protected GusApi $gus;
    public string $error = '';
    public string $env;
    public string $userKey;

    public function __construct(string $userKey = null, string $env = null)
    {
        $this->userKey = $userKey ?? config('gus.user_key');
        $this->env = $env ?? config('gus.env');
        $this->gus = new GusApi($this->userKey, $this->env);
    }


    public static function findByNip($nipToCheck): array
    {
        $gus = new self();
        return $gus->podmiotByNIP($nipToCheck) ?? [];
    }

    /*
     * @param string $nipToCheck
     * @param string returnType = Enum('array', 'object')
     */
    public function podmiotByNIP(string $nipToCheck, $returnType = 'array'): null|array|SearchReport
    {

        try {
            if (!$this->gus->isLogged()) {
                $this->gus->login();
            }

            $gusReports = $this->gus->getByNip($nipToCheck);
            return $returnType === 'object' ? $gusReports[0] ?? null : $gusReports[0]?->jsonSerialize() ?? [];
        } catch (InvalidUserKeyException $e) {
            $this->error = 'Bad user key';
            Log::error('GUS error: ' . $this->error);
            return null;
        } catch (NotFoundException $e) {
            $this->error = sprintf(
                "StatusSesji:%s\nKomunikatKod:%s\nKomunikatTresc:%s\n",
                $this->gus->getSessionStatus(),
                $this->gus->getMessageCode(),
                $this->gus->getMessage()
            );
            Log::error('GUS error: ' . $this->error);
            return null;
        }
    }

    /**
     * @param string $regon
     * @param string $reportType ReportTypes enum
     * @return array|null
     */
    public function pelnyRaportByRegon(string $regon, string $reportType): null|array
    {

        try {
            if (!$this->gus->isLogged()) {
                $this->gus->login();
            }

            $structure = new SearchResponseCompanyData();
            $structure->Regon = $regon;
            $rep = new SearchReport($structure);
            return $this->gus->getFullReport($rep, $reportType);
        } catch (InvalidUserKeyException $e) {
            $this->error = 'Bad user key';
            Log::error('GUS error: ' . $this->error);
            return null;
        } catch (InvalidReportTypeException $e) {
            $this->error = sprintf(
                "StatusSesji:%s\nKomunikatKod:%s\nKomunikatTresc:%s\n",
                $this->gus->getSessionStatus(),
                $this->gus->getMessageCode(),
                $this->gus->getMessage()
            );
            Log::error('GUS error: ' . $this->error);
            return null;
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            Log::error('GUS error: ' . $this->error);
            return null;
        }
    }

    public static function resolveReportType(SearchReport $report): ?string
    {
        return match (strtoupper($report->getType())) {
            'F' => match ((int)$report->getSilo()) {
                1 => ReportTypes::REPORT_PERSON_CEIDG,
                2 => ReportTypes::REPORT_PERSON_AGRO,
                3 => ReportTypes::REPORT_PERSON_OTHER,
                4 => ReportTypes::REPORT_PERSON_DELETED_BEFORE_20141108
            },
            'P' => ReportTypes::REPORT_ORGANIZATION,
            default => null
        };
    }
}
