<?php

namespace App\Repositories;

use App\Models\JobTaskTemplate;
use App\Models\Tenant;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class TenantRepository
{

    public function getTenantForExport(Tenant $tenant): \Illuminate\Support\Collection
    {
        $array = $tenant->makeHidden(['id', 'config'])->toArray();
        $array['vat_type'] = $tenant->vat_type->name;
        $array['tax_residency_country'] = $tenant->tax_residency_country->name;
        $array['tax_type'] = $tenant->tax_type->name;
        $array['accounting_type'] = $tenant->accounting_type->name;
        $array['business_type'] = $tenant->business_type->name;
        $tenant->loadMissing(['meta']);
        $array['meta'] = $tenant->meta->setHidden(['id', 'tenant_id'])->toArray();
        return collect($array);
    }

    public function getTenantPurchaseDocsForExport(Tenant $tenant): \Illuminate\Support\Collection
    {
        $years = DB::query()->selectRaw('year(issued_at) as year FROM purchase_docs')
            ->where('installation', $tenant->id)
            ->groupBy('year')->get();
        $response = [];
        foreach ($years as $year) {
            $start = Carbon::createFromFormat('Y', $year->year)->startOfYear()->format('Y-m-d');
            $stop = Carbon::createFromFormat('Y', $year->year)->endOfYear()->format('Y-m-d');

            /** @var Collection $purdocs */
            $purdocs = $tenant->purchaseDocs()
                ->with(['meta','items'])
                ->where('issued_at', '>=', $start)
                ->where('issued_at', '<=', $stop)
                ->get();
            $response[$year->year] = \App\Http\Resources\PurchaseDocRes::collection($purdocs);
        }
        return collect($response);
    }

    public function getTenantTradeDocsForExport(Tenant $tenant): \Illuminate\Support\Collection
    {
        $years = DB::query()->selectRaw('year(issued_at) as year FROM trade_docs')
            ->where('installation', $tenant->id)
            ->groupBy('year')->get();
        $response = [];
        foreach ($years as $year) {
            $start = Carbon::createFromFormat('Y', $year->year)->startOfYear()->format('Y-m-d');
            $stop = Carbon::createFromFormat('Y', $year->year)->endOfYear()->format('Y-m-d');

            /** @var Collection $purdocs */
            $purdocs = $tenant->tradeDocs()
                ->with(['meta','items'])
                ->where('issued_at', '>=', $start)
                ->where('issued_at', '<=', $stop)
                ->get();
            $response[$year->year] = \App\Http\Resources\TradeDocRes::collection($purdocs);
        }
        return collect($response);
    }

    public function getTenantWarehouseDocsForExport(Tenant $tenant): \Illuminate\Support\Collection
    {
        $years = DB::query()->selectRaw('year(doc_date) as year FROM warehouse_docs')
            ->where('installation', $tenant->id)
            ->groupBy('year')->get();
        $response = [];
        foreach ($years as $year) {
            $start = Carbon::createFromFormat('Y', $year->year)->startOfYear()->format('Y-m-d');
            $stop = Carbon::createFromFormat('Y', $year->year)->endOfYear()->format('Y-m-d');

            /** @var Collection $purdocs */
            $purdocs = $tenant->warehouseDocs()
                ->with(['log', 'warehouse', 'target_warehouse', 'user', 'signatory'])
                ->where('doc_date', '>=', $start)
                ->where('doc_date', '<=', $stop)
                ->get();
            $response[$year->year] = \App\Http\Resources\WarehouseDocRes::collection($purdocs);
        }
        return collect($response);
    }


    public function removeTenant(Tenant $tenant): true
    {
        $this->removeTenantPurchaseDocs($tenant);
        $this->removeTenantTradeDocs($tenant);
        $this->removeTenantWarehouseDocs($tenant);
        $this->removeTenantJobTasks($tenant);
        $this->removeTenantWarehouses($tenant);
        $this->removeTenantProductDemands($tenant);
        $this->removeTenantProducts($tenant);
        $this->removeTenantManufactures($tenant);
        $this->removeTenantPartners($tenant);
        $this->disconnectTenantUsers($tenant);
        $this->removeTenantDocSeries($tenant);
        Log::debug('Removing meta for tenant ' . $tenant->name);
        $tenant->meta()->forceDelete();
        Log::debug('Removed meta for tenant ' . $tenant->name);
        Log::debug('Removing tenant ' . $tenant->name);
        $tenant->forceDelete();
        Log::debug('Removed tenant ' . $tenant->name);
        return true;
    }

    protected function removeTenantPurchaseDocs(Tenant $tenant): void
    {
        Log::debug('Removing purchase docs for tenant ' . $tenant->name);
        $tenant->purchaseDocs()->forceDelete();
        Log::debug('Removed purchase docs for tenant ' . $tenant->name);
    }

    protected function removeTenantTradeDocs(Tenant $tenant): void
    {
        Log::debug('Removing trade docs for tenant ' . $tenant->name);
        $tenant->tradeDocs()->forceDelete();
        Log::debug('Removed trade docs for tenant ' . $tenant->name);
    }

    protected function removeTenantDocSeries(Tenant $tenant): void
    {
        Log::debug('Removing doc series for tenant ' . $tenant->name);
        $series = $tenant->docSeries()->get();
        foreach ($series as $item) {
            $item->seriesCounters()->forceDelete();
            $item->forceDelete();
        }
        Log::debug('Removed doc series for tenant ' . $tenant->name);
    }

    protected function removeTenantWarehouseDocs(Tenant $tenant): void
    {
        Log::debug('Removing warehouse docs for tenant ' . $tenant->name);
        $whdocs = $tenant->warehouseDocs()->get();
        foreach ($whdocs as $item) {
            $item->log()->forceDelete();
            $item->forceDelete();
        }
        Log::debug('Removed warehouse docs for tenant ' . $tenant->name);
    }

    protected function removeTenantJobTasks(Tenant $tenant): void
    {
        Log::debug('Removing job tasks for tenant ' . $tenant->name);
        $jtasks = $tenant->jobTasks()->get();
        foreach ($jtasks as $item) {
            $item->items()->forceDelete();
            $item->forceDelete();
        }
        Log::debug('Removed job tasks for tenant ' . $tenant->name);
        Log::debug('Removing job task templates for tenant ' . $tenant->name);
        JobTaskTemplate::where('installation', $tenant->id)->forceDelete();
        Log::debug('Removed job task templates for tenant ' . $tenant->name);
    }

    protected function removeTenantWarehouses(Tenant $tenant): void
    {
        Log::debug('Removing warehouses for tenant ' . $tenant->name);
        $wh = $tenant->warehouse()->get();
        foreach ($wh as $item) {
            $item->items()->forceDelete();
            $item->stocktaking()->forceDelete();
            $item->forceDelete();
        }
        Log::debug('Removed warehouses for tenant ' . $tenant->name);
    }

    protected function removeTenantProductDemands(Tenant $tenant): void
    {
        Log::debug('Removing product demands for tenant ' . $tenant->name);
        $tenant->productDemands()->forceDelete();
        Log::debug('Removed product demands for tenant ' . $tenant->name);
    }

    protected function removeTenantProducts(Tenant $tenant): void
    {
        Log::debug('Removing products for tenant ' . $tenant->name);
        $tenant->products()->forceDelete();
        Log::debug('Removed products for tenant ' . $tenant->name);
    }

    protected function removeTenantManufactures(Tenant $tenant): void
    {
        Log::debug('Removing manufactures for tenant ' . $tenant->name);
        $tenant->manufacturers()->forceDelete();
        Log::debug('Removed manufactures for tenant ' . $tenant->name);
    }

    protected function removeTenantPartners(Tenant $tenant): void
    {
        Log::debug('Removing partners for tenant ' . $tenant->name);
        $tenant->partners()->forceDelete();
        Log::debug('Removed partners for tenant ' . $tenant->name);
    }

    protected function disconnectTenantUsers(Tenant $tenant): void
    {
        Log::debug('Disconnecting users for tenant ' . $tenant->name);
        $users = $tenant->user;
        $tenant->user()->detach();
        foreach ($users as $user) {
            $user = $user->fresh();
            if ($user->tenant()->count() < 1) {
                $user->forceDelete();
            }
        }
        Log::debug('Disconnected users for tenant ' . $tenant->name);
    }
}
