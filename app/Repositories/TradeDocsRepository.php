<?php

namespace App\Repositories;

use App\Enums\DocumentTypes;
use App\Helpers\Identifiers;
use App\Models\DocumentSeriesPattern;
use App\Models\DTOTradeDocMeta;
use App\Models\TradeDoc;
use App\Models\TradeDocItem;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use PhpParser\Comment\Doc;

class TradeDocsRepository
{

    public static $error = '';
    public static $error_code = 0;

    public static function createTradeDoc($data)
    {
    }

    public static function createDocNumberOnModel(TradeDoc $record): void
    {
        $dsp = DocumentSeriesRepository::getDocNumber(
            $record->document_series_id,
            new \DateTime($record->issued_at ?? now('Europe/Warsaw'))
        );
        $record->doc_number = $dsp->getConcreteNumber();
        $record->full_doc_number = $dsp->getFullNumber();
    }

    public static function createTransactionOnModel(TradeDoc $record): void
    {
        $record->transaction_id = Identifiers::createTransactionId(strtolower($record->type->name) . '-');
    }

    public static function createMetaOnModel(TradeDoc $record, array $meta = [], array $data = []): void
    {
        $recordMeta = DTOTradeDocMeta::createTradeDoc($record, $meta, $data);
        $record->meta()->create(['meta' => $recordMeta]);
    }

    public static function createDocItem(Model|TradeDoc $tradeDoc, array $data): Model
    {
        $data = self::modifyItemDataBeforeCreate($tradeDoc, $data);
        $model = new TradeDocItem();
        $model->fill($data);
        $model->installation = $tradeDoc->installation;
        $tradeDoc->items()->save($model);
        self::updateDocSummary($tradeDoc);
        return $model;
    }


    public static function createFVKDocItem(Model|TradeDoc $tradeDoc, array $data): Model
    {

        $modifiedData = self::modifyItemDataBeforeCreate($tradeDoc, $data);
        $model = new TradeDocItem();
        $model->fill($modifiedData);
        $model->installation = $tradeDoc->installation;
        $tradeDoc->items()->save($model);
        $meta = $tradeDoc->getMeta();
        $meta->addItem($model->uuid, $data);
        $tradeDoc->meta->update(['meta' => $meta]);
        self::updateDocSummary($tradeDoc);
        return $model;
    }


    /**
     * @param Model|TradeDoc $tradeDoc
     * @param array $data
     * @return array
     * @throws \Exception
     */
    public static function modifyItemDataBeforeCreate(Model|TradeDoc $tradeDoc, array $data): array
    {
        if ($tradeDoc->type === DocumentTypes::FVK) {
            return self::modifyFVKItemDataBeforeCreate($tradeDoc, $data);
        }

        if ($tradeDoc->type === DocumentTypes::FAUP) {
            return self::modifyFAUPItemDataBeforeCreate($tradeDoc, $data);
        }

        return $data;
    }

    public static function modifyFAUPItemDataBeforeCreate(Model|TradeDoc $tradeDoc, array $data): array
    {
        $data['net_unit_price'] = $data['gross_unit_price'];
        $data['net_value'] = $data['gross_value'];
        $data['vat_value'] = 0;
        $data['vat_rate'] = 0;
        return $data;
    }

    public static function modifyFVKItemDataBeforeCreate(Model|TradeDoc $tradeDoc, array $data): array
    {
        if (blank($data['source_id'] ?? null)) {
            return $data;
        }

        $newData = $data;

        $sourceDoc = $tradeDoc->getSourceDoc() ?? throw new \Exception('Source document not found');
        $sourceItem = $sourceDoc->items()->find($data['source_id']) ?? throw new \Exception('Source item not found');

        $newData['net_unit_price'] = $data['net_unit_price'] - $sourceItem->net_unit_price;
        $newData['gross_unit_price'] = $data['gross_unit_price'] - $sourceItem->gross_unit_price;
        $newData['discounted_unit_price'] = $data['discounted_unit_price'] - $sourceItem->discounted_unit_price;
        $newData['amount'] = $data['amount'] - $sourceItem->amount;
        $newData['net_value'] = $data['net_value'] - $sourceItem->net_value;
        $newData['vat_value'] = $data['vat_value'] - $sourceItem->vat_value;
        $newData['gross_value'] = $data['gross_value'] - $sourceItem->gross_value;
        return $newData;
    }

    public static function updateFVKDocItem(Model|TradeDoc $tradeDoc, Model|TradeDocItem $item, array $data): void
    {
        $newData = self::modifyItemDataBeforeCreate($tradeDoc, $data);
        $item->fill($newData);
        $item->save();
        $meta = $tradeDoc->getMeta();
        $meta->addItem($item->uuid, $data);
        $tradeDoc->meta->update(['meta' => $meta]);
        self::updateDocSummary($tradeDoc);
    }


    public static function updateDocItem(Model|TradeDoc $tradeDoc, Model|TradeDocItem $item, array $data): void
    {
        $data = self::modifyItemDataBeforeCreate($tradeDoc, $data);
        $item->fill($data);
        $item->save();
        self::updateDocSummary($tradeDoc);
    }

    public static function updateDocSummary(Model|TradeDoc $tradeDoc): void
    {
        $tradeDoc->gross = 0;
        $tradeDoc->net = 0;
        $tradeDoc->vat_amount = 0;
        $vatByRate = [];
        $summary = [
            'vat_amount' => 0,
            'net_amount' => 0,
            'gross_amount' => 0,
        ];
        $tradeDoc->items()->each(function (TradeDocItem $item) use ($tradeDoc, &$vatByRate) {
            $tradeDoc->gross += $item->gross_value;
            $tradeDoc->net += $item->net_value;
            $tradeDoc->vat_amount += $item->vat_value;
            $vatByRate[$item->vat_label] = [
                'vat_rate' => $item->vat_rate,
                'vat_amount' => round(($vatByRate[$item->vat_label]['vat_amount'] ?? 0) + $item->vat_value, 2),
                'net_amount' => round(($vatByRate[$item->vat_label]['net_amount'] ?? 0) + $item->net_value, 2),
                'gross_amount' => round(($vatByRate[$item->vat_label]['gross_amount'] ?? 0) + $item->gross_value, 2),
            ];
        });
        $summary['vat_amount'] = array_sum(array_column($vatByRate, 'vat_amount'));
        $summary['net_amount'] = array_sum(array_column($vatByRate, 'net_amount'));
        $summary['gross_amount'] = array_sum(array_column($vatByRate, 'gross_amount'));
        $vatByRate['summary'] = $summary;
        $tradeDoc->save();
        self::updateVATMeta($tradeDoc, $vatByRate);
    }

    public static function updateVATMeta(TradeDoc $record, array $vatByRate): void
    {
        $meta = $record->getMeta();
        $meta->setUpVat($vatByRate);
        if ($record->type === DocumentTypes::FVK) {
            $vat_final = $vatByRate;
            foreach ($meta->vat_source as $label => $vat) {
                $parts = [];
                if ($label !== 'summary') {
                    $parts['vat_rate'] = $vat['vat_rate'];
                }
                $parts['vat_amount'] = $vat['vat_amount'] + ($vatByRate[$label]['vat_amount'] ?? 0);
                $parts['net_amount'] = $vat['net_amount'] + ($vatByRate[$label]['net_amount'] ?? 0);
                $parts['gross_amount'] = $vat['gross_amount'] + ($vatByRate[$label]['gross_amount'] ?? 0);
                $vat_final[$label] = $parts;
            }
            $meta->setUpVatFinal($vat_final);
        }
        $record->meta->update(['meta' => $meta]);
    }

    public static function getDocByTransaction(string $transaction): ?TradeDoc
    {
        return TradeDoc::where('transaction_id', $transaction)->first();
    }

    public static function createInvoiceCorrection(TradeDoc $tradeDoc, array $data): TradeDoc
    {
        $correction = $tradeDoc->replicate();
        $correction->type = DocumentTypes::FVK;
        $correction->document_series_id = $data['document_series_id'];
        $correction->issued_at = $data['issued_at'];
        $correction->notes = $data['reason'];
        $correction->source_id = $tradeDoc->uuid;
        $correction->is_final_invoice = false;
        $correction->is_paid = false;
        $correction->payment_date = null;
        $correction->transaction_id = Identifiers::createTransactionId(DocumentTypes::FVK->name);
        $correction->status = 2;
        $number = DocumentSeriesRepository::getDocNumber(
            $data['document_series_id'],
            Carbon::make($data['issued_at'])
        );
        $correction->full_doc_number = $number->getFullNumber();
        $correction->doc_number = $number->getConcreteNumber();
        $correction->save();
        $meta = $tradeDoc->meta->meta;
        $meta['vat_source'] = $meta['vat'];
        $meta['vat'] = [];
        $correction->meta()->create(['meta' => $meta]);
        $tradeDoc->has_correction = true;
        $tradeDoc->save();
        return $correction;
    }

    public static function finalInvoiceProcess(TradeDoc $doc, array $prepaidInvoicesIds, $operation = 'create'): bool
    {
        if (!$doc->is_final_invoice && $operation !== 'edit') {
            return false;
        }

        if ($operation === 'edit') {
            $doc->prepaidInvoices()->update(['final_invoice_trade_doc_id' => null]);
            TradeDoc::whereIn('uuid', $prepaidInvoicesIds)->update(['final_invoice_trade_doc_id' => $doc->uuid]);
        } else {
            TradeDoc::whereIn('uuid', $prepaidInvoicesIds)->update(['final_invoice_trade_doc_id' => $doc->uuid]);
            self::resolvePrepaidInvoicesInFinalInvoice($doc);
        }
        return true;
    }

    protected static function resolvePrepaidInvoicesInFinalInvoice(TradeDoc $doc): TradeDoc
    {
        /**
         * @var TradeDoc $invoice
         */
        foreach ($doc->prepaidInvoices as $invoice) {
            $vatRates = $invoice->getMeta()->vat;
            foreach ($vatRates as $rate => $vat) {
                if ($rate === 'summary') {
                    continue;
                }
                $item = new TradeDocItem();
                $item->installation = $doc->installation;
                $label = 'Rozliczenie faktury zaliczkowej %s z dnia %s. Stawka %s';
                $item->label .= sprintf($label, $invoice->full_doc_number, $invoice->issued_at->format('Y-m-d'), $rate);
                $item->net_unit_price = -1 * $vat['net_amount'];
                $item->discounted_unit_price = -1 * $vat['net_amount'];
                $item->gross_unit_price = -1 * $vat['gross_amount'];
                $item->amount = 1;
                $item->net_value = -1 * $vat['net_amount'];
                $item->vat_rate = $vat['vat_rate'];
                $item->vat_label = $rate;
                $item->vat_value = -1 * $vat['vat_amount'];
                $item->gross_value = -1 * $vat['gross_amount'];
                $doc->items()->save($item);
            }

            self::updateDocSummary($doc);
        }

        return $doc;
    }

    public static function canDocBeRemoved(TradeDoc $doc): bool
    {
        return
            DocumentSeriesRepository::isLastInNumberInSeries(
                $doc->document_series_id,
                $doc->doc_number,
                $doc->issued_at
            ) &&
            !$doc->has_correction &&
            !$doc->hasFinalInvoice() &&
            !$doc->isAccepted();
    }

    public static function deleteTradeDoc(TradeDoc $doc): bool
    {
        if (false === self::canDocBeRemoved($doc)) {
            self::$error = 'Istnieje nowszy dokument w serii lub ten został już zaakceptowany';
            return false;
        }
        /**
         * @var TradeDoc $doc
         */
        DB::enableQueryLog();
        try {
            DB::transaction(static function () use ($doc) {
                /**
                 * @var DocumentSeriesPattern $dsp
                 */
                $dsp = $doc->documentSeries;
                if (!$dsp->rollbackNumber($doc->issued_at, $doc->doc_number)) {
                    throw new \Exception('Błąd serii dokumentu!', 1088);
                }

                switch ($doc->type) {
                    case DocumentTypes::FVK:
                        TradeDoc::where('uuid', $doc->source_id)->update(['has_correction' => false]);
                        $doc->correctedDocument()->update(['has_correction' => false]);
                        break;
                    case DocumentTypes::FVS:
                        if ($doc->is_final_invoice) {
                            $doc->prepaidInvoices()->update(['final_invoice_trade_doc_id' => null]);
                        }
                }

                $doc->items()->delete();
                $doc->meta()->delete();
                $doc->forceDelete();
            });

            return true;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    public static function resetErrors(): void
    {
        self::$error = '';
        self::$error_code = 0;
    }
}
