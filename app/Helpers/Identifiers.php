<?php

namespace App\Helpers;

use Illuminate\Database\Eloquent\Model;

class Identifiers
{
    public static function getRandomHash(int $length = 16): string
    {
        return bin2hex(random_bytes($length));
    }

    public static function getHash(string $string): string
    {
        return md5($string);
    }

    /** @noinspection NonSecureUniqidUsageInspection */
    public static function createTransactionId(?string $prefix = null, bool $more_entropy = false): string
    {
        return uniqid($prefix ?? '', $more_entropy);
    }

    public static function buildCompoundIndex(Model $row, string $mode = 'simple', string $glue = '.'): string
    {
        return match ($mode) {
            default => $row['hash'] . $glue . $row['id'],
            'reverse' => $row['hash'] . $glue . 10000 - $row['id'],
        };
    }

    /**
     * @param string $index
     * @param string $mode
     * @param string $glue
     * @return Array<hash,id>|null
     *
     */
    public static function deconstructCompoundIndex(string $index, string $mode = 'simple', string $glue = '.'): ?array
    {

        $raw = explode('.', $index);

        if (count($raw) !== 2) {
            return null;
        }

        return match ($mode) {
            default => [
                'hash' => $raw[0],
                'id' => $raw[1],
            ],
            'reverse' => [
                'hash' => $raw[0],
                'id' => 10000 - $raw[1],
            ]
        };
    }
}
