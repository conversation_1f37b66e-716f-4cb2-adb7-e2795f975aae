<?php
namespace App\Helpers;

use App\Models\Installation;
use App\Models\ProfileData;
use App\Models\User;

class UserHelper
{
    public static function synchronizeInstallationByModel(User $modelToSynchronize): void
    {
        $tenant = auth()->user()->tenant()->first()->id;
        Installation::updateOrCreate([
            'user_id' => $modelToSynchronize->id,
        ], [
            'tenant_id' => $tenant
        ]);
    }

    public static function editUserAdditionalData(User $user, array $editData): ?ProfileData
    {
        return ProfileData::updateOrCreate([
            'user_id' => $user->id
        ], [
            'name' => $editData['name'] ?? null,
            'surname' => $editData['surname'] ?? null,
            'adress' => $editData['adress'] ?? null,
            'lang' => $editData['lang'] ?? null,
            'number' => $editData['number'] ?? null
        ]);
    }
}
