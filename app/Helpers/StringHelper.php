<?php

namespace App\Helpers;

class StringHelper
{
    /**
     * Extract only digits from a string
     *
     * @param string $input The input string containing digits and other characters
     * @return string A string containing only the digits from the input
     *
     * @example
     * StringHelper::extractDigits('739-29-55-802') // returns '73929558021'
     */
    public static function extractDigits(string $input): string
    {
        // Use preg_replace to remove all non-digit characters
        return preg_replace('/[^0-9]/', '', $input);
    }
}
