<?php

if (! function_exists('tenant')) {
    function tenant(bool $refresh = false): ?\App\Models\Tenant
    {
        if (null === auth()->user()) {
            return null;
        }

        if ($refresh || !session()->has(auth()->user()->id . '_tenant')) {
            $tenant = auth()->user()->getTenant();
            if (null === $tenant) {
                return null;
            }

            session()->put(auth()->user()->id . '_tenant', $tenant);
        }

        return session()->get(auth()->user()->id . '_tenant', null);
    }
}

if (! function_exists('tenantFlush')) {
    function tenantFlush(): void
    {
        if (null === auth()->getUser()) {
            return;
        }

        session()->forget(auth()->user()->id . '_tenant');
    }
}

if (! function_exists('appVersion')) {
    function appVersion(bool $refresh = false): array
    {
        [$major, $minor, $patch] = explode('.', config('app.version'));
        return ['major' => $major, 'minor' => $minor, 'patch' => $patch];
    }
}


function imageFileToBase64(string $path): string
{

    $imageData = \Illuminate\Support\Facades\File::get($path);
    if (empty($imageData)) {
        return '';
    }

    $mimeType = \Illuminate\Support\Facades\File::mimeType($path);

    if (empty($mimeType)) {
        trigger_error("Nie można rozpoznać typu MIME dla pliku", E_USER_WARNING);
        return '';
    }

    $base64 = base64_encode($imageData);
    if ($base64 === false) {
        trigger_error("Nie można zakodować danych obrazu w Base64.", E_USER_WARNING);
        return '';
    }

    return 'data:' . $mimeType . ';base64,' . $base64;
}
