<?php

namespace App\Helpers;

class NumberToWords
{
    public static string $result = ''; //zmienna przechowuje slownie
    private static array $triplets = []; //tablica z trojkami
    private static array $d1 = []; //tablica z cyframi
    private static array $dot = []; //tablica calkowite,ulamki
    private static array $current_suffix = []; //nazwa aktualnej tablicy z suffixem
    private static int $last_digit = 0; //ostatnio obrabiana cyfra
    private static array $digits = [
        0 => [1 => 'zero', 2 => 'zero', 3 => 'zero'],
        1 => [1 => 'jeden', 2 => 'dziesi<PERSON><PERSON>', 3 => 'sto'],
        2 => [1 => 'dwa', 2 => 'dwadzieścia', 3 => 'dwieście'],
        3 => [1 => 'trzy', 2 => 'trzydzieści', 3 => 'trzysta'],
        4 => [1 => 'cztery', 2 => 'czterdzieści', 3 => 'czterysta'],
        5 => [1 => 'pięć', 2 => 'pięćdziesiąt', 3 => 'pięćset'],
        6 => [1 => 'sześć', 2 => 'sześćdziesiąt', 3 => 'sześćset'],
        7 => [1 => 'siedem', 2 => 'siedemdziesiąt', 3 => 'siedemset'],
        8 => [1 => 'osiem', 2 => 'osiemdziesiąt', 3 => 'osiemset'],
        9 => [1 => 'dziewięć', 2 => 'dziewięćdziesiąt', 3 => 'dziewięćset']
    ];
    private static array $numbers = [
        0 => [1 => 'dziesięć'],
        1 => [1 => 'jedenaście'],
        2 => [1 => 'dwanaście'],
        3 => [1 => 'trzynaście'],
        4 => [1 => 'czternaście'],
        5 => [1 => 'piętnaście'],
        6 => [1 => 'szesnaście'],
        7 => [1 => 'siedemnaście'],
        8 => [1 => 'osiemnaście'],
        9 => [1 => 'dziewiętnaście']
    ];
    private static array $suffix = [
        0 => [
            1 => 'złoty(ch)',
            2 => 'złote',
            3 => 'złote',
            4 => 'złote',
            5 => 'złotych',
            6 => 'złotych',
            7 => 'złotych',
            8 => 'złotych',
            9 => 'złotych',
            0 => 'złotych'
        ],
        1 => [
            1 => 'tysiąc',
            2 => 'tysiące',
            3 => 'tysiące',
            4 => 'tysiące',
            5 => 'tysięcy',
            6 => 'tysięcy',
            7 => 'tysięcy',
            8 => 'tysięcy',
            9 => 'tysięcy',
            0 => 'tysięcy'
        ],
        2 => [
            1 => 'milion',
            2 => 'miliony',
            3 => 'miliony',
            4 => 'miliony',
            5 => 'milionów',
            6 => 'milionów',
            7 => 'milionów',
            8 => 'milionów',
            9 => 'milionów',
            0 => 'milionów'
        ]
    ];
    private static $suffix1 = [
        0 => [
            1 => 'złotych',
            2 => 'złotych',
            3 => 'złotych',
            4 => 'złotych',
            5 => 'złotych',
            6 => 'złotych',
            7 => 'złotych',
            8 => 'złotych',
            9 => 'złotych',
            0 => 'złotych'
        ],
        1 => [
            1 => 'tysięcy',
            2 => 'tysięcy',
            3 => 'tysięcy',
            4 => 'tysięcy',
            5 => 'tysięcy',
            6 => 'tysięcy',
            7 => 'tysięcy',
            8 => 'tysięcy',
            9 => 'tysięcy',
            0 => 'tysięcy'
        ],
        2 => [
            1 => 'milionów',
            2 => 'milionów',
            3 => 'milionów',
            4 => 'milionów',
            5 => 'milionów',
            6 => 'milionów',
            7 => 'milionów',
            8 => 'milionów',
            9 => 'milionów',
            0 => 'milionów'
        ]
    ];

    /**
     * Zamienia na slownie
     * @param float $src Liczba do zamiany
     * @param string $sep Separator dziesiętnych
     */
    public static function get(float $number = 1, string $sep = ','): string
    {
        self::reset();
        $number = number_format($number, 2, $sep, '');
        self::fdot($number, $sep);
        self::splitByThree(self::$dot[0]);
        $result = '';
        foreach (self::$triplets as $suf => $src) {
            self::fd1($src);
            $result .= self::convert();
            $result .= self::$current_suffix[$suf][self::$last_digit] . " ";
        }
        if (!empty(self::$dot[1])) {
            self::fd1(self::$dot[1]);
            $result .= self::convert();
            if ((int)self::$d1[1] === 0 && (int)self::$d1[2] === 0) {
                $result .= 'zero groszy';
            } else {
                $result .= 'groszy';
            }
        }
//        self::$result = $result;

        return self::$result = $result;
    }

    private static function reset(): void
    {
        self::$result = '';
        self::$triplets = [];
        self::$d1 = [];
        self::$dot = [];
        self::$current_suffix = [];
        self::$last_digit = 0;
    }


    private static function fdot($src = '', $sep = '.')
    {
        self::$dot = explode($sep, $src);
    }

    /**
     * Split number by 3 digits
     * @param string $src
     */
    private static function splitByThree(int $src): void
    {
        if ($src < 1000) {
            self::$triplets[0] = $src;
            return;
        }

        do {
            self::$triplets[] = (int)fmod($src, 1000);
            $src = (int)($src / 1000);
        } while ($src > 1000);

        self::$triplets[] = $src;
        self::$triplets = array_reverse(self::$triplets, true);
    }

    /**
     * Rozbija trójki na pojedyncze cyfry w kolejności odwrotnej
     * @param string $src
     */
    private static function fd1($src): void
    {
        $ln_src = strlen($src);
        $d1 = array(); // cyfry w trojkach
        for ($i = $ln_src; $i > 0; $i--) {
            $d1[$i] = substr($src, 0, 1);
            $src = substr($src, 1);
        }
        self::$d1 = $d1;
    }

    /**
     * Convert number to words
     * @return string
     */
    private static function convert(): string
    {
        $stop = false;
        $cyfra = self::$digits;
        $c_suffix = self::$suffix;
        $result = '';

        foreach (self::$d1 as $poz => $num) {
            if ($cyfra[$num][$poz] === 'zero') {
                continue;
            }

            if ($poz == 2 && $num == 1) {
                $cyfra = self::$numbers;
                $c_suffix = self::$suffix1;
                $stop = true;
                continue;
            }

            $result .= $cyfra[$num][$poz] . " ";

            if ($stop) {
                break;
            }
        }

        self::$last_digit = $num;
        self::$current_suffix = $c_suffix;
        return $result;
    }
}
