<?php

namespace App\Helpers;

use App\Enums\BarcodeTypes;
use Carbon\Carbon;
use Lamoda\GS1Parser\Barcode;
use Lamoda\GS1Parser\Exception\InvalidBarcodeException;
use Lamoda\GS1Parser\Parser\Parser;
use Lamoda\GS1Parser\Parser\ParserConfig;
use Lamoda\GS1Parser\Validator\Validator;
use Lamoda\GS1Parser\Validator\ValidatorConfig;

class Barcodes
{
    public string $errors = '';
    public static string $data_matrix_fnck = '';

    protected ?ParserConfig $dataMatrixConfig = null;

    protected array $dm_known_ais = ['01', '15', '10', '17'];


    public static function identify($code): BarcodeTypes
    {
        $len = strlen($code);

        if (str_starts_with($code, "\x1D")) {
            self::$data_matrix_fnck = "\x1D";
            return BarcodeTypes::DATAMATRIX;
        }


        if (str_starts_with($code, "]d2")) {
            self::$data_matrix_fnck = "]d2";
            return BarcodeTypes::DATAMATRIX;
        }

        if (($len === 13 || $len === 8) && 1 === preg_match('/\d+/', $code)) {
            return match ($len) {
                13 => BarcodeTypes::EAN13,
                8 => BarcodeTypes::EAN8,
            };
        }

        if ($len >= 24 && str_starts_with($code, '010')) {
            return BarcodeTypes::DATAMATRIX;
        }

        if (str_starts_with($code, 'ntv:')) {
            return BarcodeTypes::NTV;
        }

        return BarcodeTypes::QRCODE;
    }


    public function validate($barcode, ?BarcodeTypes $barcode_type = null, mixed $config = null)
    {
        switch ($barcode_type) {
            case BarcodeTypes::EAN13:
            case BarcodeTypes::EAN8:
                break;
            case BarcodeTypes::DATAMATRIX:
                break;
        }
    }

    public function parseDataMatrix(string $barcode, string $fnc_gs1_start_seq = ']d2', string $gs = "\u{001d}"): ?array
    {
        $this->dataMatrixConfig()
            ->setGroupSeparator($gs)
            ->setKnownAIs($this->dm_known_ais);
        if ($fnc_gs1_start_seq === "") {
            $this->dataMatrixConfig()
                ->setFnc1SequenceRequired(false);
        } else {
            $this->dataMatrixConfig()->setFnc1PrefixMap([$fnc_gs1_start_seq => Barcode::TYPE_GS1_DATAMATRIX]);
        }
        $parser = new Parser($this->dataMatrixConfig());

        try {
            $parsed = $parser->parse($barcode);
        } catch (InvalidBarcodeException $exception) {
            $this->errors = $exception->getMessage();
            return null;
        }

        return $parsed->ais();
    }


    public function dataMatrixConfig(): ParserConfig
    {
        if (null === $this->dataMatrixConfig) {
            $this->setDataMatrixConfig();
        }
        return $this->dataMatrixConfig;
    }

    public function setDataMatrixConfig(): void
    {
        $this->dataMatrixConfig = new ParserConfig();
    }

    public static function parseDate(string $date)
    {
        $format = 'ymd';
        $update_day = false;

        [$year, $month, $day] = str_split($date, 2);

        if ((int) $month > 12) {
            $format = 'Ym';
            $update_day = true;
        }

        if ((int) $day === 0) {
            $update_day = true;
            $format = 'ym';
            $date = $year.$month;
        }

        $object = Carbon::createFromFormat($format, $date, new \DateTimeZone('Europe/Warsaw'));

        if ($update_day) {
            $object->lastOfMonth();
        }

        return $object;
    }
}
