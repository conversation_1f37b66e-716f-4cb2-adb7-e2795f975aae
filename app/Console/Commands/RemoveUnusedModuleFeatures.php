<?php

namespace App\Console\Commands;

use App\Enums\DocumentGeneralTypes;
use App\Enums\DocumentTypes;
use App\Models\DocumentSeriesPattern;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class RemoveUnusedModuleFeatures extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wh:remove-unused-module-features {--yes2all : no questions asked}
    {--force : hard remove (no soft deletes). Removes also previous soft deleted records}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Removes unused features when system modules are removed';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Removing unused DocumentSeriesPatterns');

        if ($this->option('yes2all')) {
            $this->removeSellsDocsPatterns();
            $this->info('Removed unused SellsDocs patterns');
            $this->removeWarehouseDocsPatterns();
            $this->info('Removed unused WarehouseDocs patterns');
            exit();
        }

        $tdd =  $this->getSellsDocsBuilder()->count();
        $this->info('Found unused SellsDocs: ' . $tdd);
        if ($tdd > 0 && $this->confirm('Do you want to remove unused SellsDocs?')) {
            $this->removeSellsDocsPatterns();
            $this->info('Removed unused SellsDocs patterns');
        }

        $whd = $this->getWarehouseDocsBuilder()->count();
        $this->info('Found unused WarehouseDocs ' . $whd);
        if ($whd > 0 && $this->confirm('Do you want to remove unused WarehouseDocs?')) {
            $this->removeWarehouseDocsPatterns();
            $this->info('Removed unused WarehouseDocs patterns');
        }

        $this->info('Done');
    }

    private function removeSellsDocsPatterns(): void
    {
        match ($this->option('force')) {
            true => $this->getSellsDocsBuilder()->forceDelete(),
            default => $this->getSellsDocsBuilder()->delete()
        };
    }

    private function removeWarehouseDocsPatterns(): void
    {
        match ($this->option('force')) {
            true => $this->getWarehouseDocsBuilder()->forceDelete(),
            default => $this->getWarehouseDocsBuilder()->delete()
        };
    }

    private function getSellsDocsBuilder(): Builder
    {
        $qry = DocumentSeriesPattern::query()
            ->whereIn('doc_type', DocumentTypes::getSellsTypeDocs())
            ->doesntHave('tradeDocuments')
            ->blocked();

        return $this->parseForce($qry);
    }

    private function getWarehouseDocsBuilder(): Builder
    {
        $qry = DocumentSeriesPattern::query()
            ->whereIn('doc_type', DocumentTypes::getDocTypesOf(DocumentGeneralTypes::WAREHOUSE))
            ->blocked()
            ->doesntHave('warehouseDocuments');

        return $this->parseForce($qry);
    }

    private function parseForce(Builder $builder)
    {
        return match ($this->option('force')) {
            true => $builder->withTrashed(),
            default => $builder
        };
    }
}
