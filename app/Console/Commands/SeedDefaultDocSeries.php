<?php

namespace App\Console\Commands;

use App\Models\DocumentSeriesPattern;
use App\Models\Tenant;
use App\Models\WarehouseDoc;
use App\Repositories\DocumentSeriesRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SeedDefaultDocSeries extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wh:seed-default-doc-series';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed default document series and update numbers.
    Should used once after Document series pattern installation..
    Use very carefully!';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        $this->info('Command blocked. Should used once after Document series pattern installation.');
        exit(0);

        $tenants = Tenant::all();
        foreach ($tenants as $tenant) {
            DocumentSeriesRepository::seedDefaultSeries($tenant);
        }

        $series = [];
        foreach ($tenants as $tenant) {
            $docs = WarehouseDoc::where('installation', $tenant->id)->get();
            foreach ($docs as $doc) {
                /**
                 * @var DocumentSeriesPattern $dsp
                 */
                $dsp = DocumentSeriesPattern::default()
                    ->docType($doc->type)
                    ->where('installation', $tenant->id)
                    ->first();
                $full = $dsp->generateFullNumber($doc->doc_number, $doc->created_at);
                $doc->full_doc_number = $full;
                $doc->document_series_id = $dsp->id;
                $doc->save();
                $ternary = ($series[$dsp->id] ?? 0) <=> (int) $doc->doc_number;
                if ($ternary < 0) {
                    $series[$dsp->id] = $doc->doc_number;
                }
            }
        }

        foreach ($series as $docId => $docNumber) {
            DB::table('document_series_numbers')->where('id', $docId)->update([
                'counter' => $docNumber,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
    }
}
