<?php

namespace App\Console\Commands;

use App\Filament\App\Resources\ProductDemandResource;
use App\Models\ProductDemand;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CleanProductDemands extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wh:clean-product-demands {--no-confirm : no questions}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Czyści przeterminowane zapotrzebowania';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        $completed = ProductDemand::query()->completed()->count();
        $inProgress = ProductDemand::query()->inProgress()->count();
        $active = ProductDemand::query()->active()->count();
        $overdue = ProductDemand::query()->overdue()->count();

        $this->info('Completed: ' . $completed);
        $this->info('In progress: ' . $inProgress);
        $this->info('Active: ' . $active);
        $this->info('Overdue: ' . $overdue);
        if ($this->option('no-confirm') === false && false === $this->confirm('Continue?')) {
            $this->info('Exit');
            exit();
        }

        ProductDemand::query()->overdue()->delete();
        ProductDemand::query()->completed()->delete();
        Log::info('Clean products demands command run');
    }
}
