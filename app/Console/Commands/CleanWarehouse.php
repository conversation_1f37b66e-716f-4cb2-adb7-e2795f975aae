<?php

namespace App\Console\Commands;

use App\Models\WarehouseItem;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CleanWarehouse extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wh:clean-warehouse
    {--soft-deleted : Clean soft deleted}
    {--release-reserved : Remove reservation when source doc doesn\'t exist}
    {--all : Clean soft deleted and remove reservation when source doc doesn\'t exist}
    {--no-confirm : don\'t ask confirmation}
    ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean warehouse of deleted products (soft-deleted) ' .
    'or/and clean reservation when source doc doesn\'t exist';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('soft-deleted') === false &&
            $this->option('release-reserved') === false &&
            $this->option('all') === false) {
            $this->error('You have to choose one option');
            $this->info($this->getSynopsis());
        }

        if ($this->option('soft-deleted') || $this->option('all')) {
            $this->cleanSoftDelete();
        }

        if ($this->option('release-reserved') || $this->option('all')) {
            $this->releaseReserved();
        }

        Log::info('Clean warehouse command run');
    }

    protected function cleanSoftDelete(): void
    {
        $this->info('Cleaning soft deleted items...');
        $found = WarehouseItem::withTrashed()->whereNotNull('deleted_at')->count();
        if ($this->option('no-confirm') === false &&
            !$this->confirm('Found ' . $found . ' warehouse items to delete. Continue')) {
            $this->info('Skipping');
            return;
        }
        $this->info('Deleting...');
        WarehouseItem::withTrashed()->whereNotNull('deleted_at')->forceDelete();
        $found = WarehouseItem::withTrashed()->whereNotNull('deleted_at')->count();
        $this->info('Found ' . $found . ' warehouse items to delete.');
    }

    protected function releaseReserved()
    {
        $this->info('Releasing reservation...');
        $found = WarehouseItem::whereNotNull('reserved_by')->doesntHave('booker')->count();
        if ($found < 1) {
            $this->info('Not found orphans...');
            return;
        }
        if ($this->option('no-confirm') === false &&
            !$this->confirm('Found ' . $found . ' warehouse items to remove reservation. Continue')) {
            $this->info('Bye bye');
            return;
        }
        $this->info('Removing reservation...');

        /**
         * @var WarehouseItem[] $all
         */
        $all = WarehouseItem::whereNotNull('reserved_by')->doesntHave('booker')->get();
        foreach ($all as $item) {
            $item->removeReservation();
        }
        $this->info('Reservations released.');
    }
}
