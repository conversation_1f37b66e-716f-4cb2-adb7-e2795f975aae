<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Permission;

class SynchronizeRoles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    // phpcs:ignore
    protected $signature = 'wh:sync-roles-permissions {--dump : To save current state to file} {--force : no confirmation}';

    protected string $fileName;
    protected string $path;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronize roles and permissions';

    public function __construct()
    {
        $this->fileName = 'roles_permission_dump.json';
        $this->path = resource_path() . '/dumps/';
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        if ($this->option('dump')) {
            $this->firstDrop();
            $this->info('Roles and permission saved to ' . $this->path . $this->fileName . ' file');
            exit();
        }

        if (!$this->option('force') && !$this->confirm('Are you sure?')) {
            $this->warn('Cancelled');
            exit();
        }

        $rolesAndPermissions = $this->read(true);
        $this->install($rolesAndPermissions);
    }

    protected function firstDrop()
    {
        $allPermissions = Permission::get();
        $rolePermissions = DB::table('role_has_permissions')->get();
        if (!$this->confirm('Roles and permissions synchronization. This command will overwrite existing json file')) {
            $this->warn('Cancelled');
            exit() ;
        }
        return $this->toFile(json_encode([
            'permissions' => $allPermissions,
            'role_has_permissions' => $rolePermissions,
        ]));
    }

    protected function toFile(string $jsonContent)
    {
        return file_put_contents($this->path . $this->fileName, $jsonContent);
    }

    protected function read(bool $asArray = true)
    {
        if (file_exists($this->path . $this->fileName)) {
            try {
                return json_decode(
                    file_get_contents($this->path . $this->fileName),
                    $asArray,
                    512,
                    JSON_THROW_ON_ERROR
                );
            } catch (\Exception $e) {
                $this->error($e->getMessage());
                exit();
            }
        }

        $this->error('FILE NOT EXIST! DROP FIRST');
        exit();
    }

    protected function checkFile($content)
    {
        if (!isset($content['permissions'], $content['role_has_permissions'])) {
            $this->warn('"Permissions" or "role_has_permissions" key not set, aborting');
            return false;
        }
        return true;
    }

    protected function install($rolesAndPermissions): void
    {
        if ($this->checkFile($rolesAndPermissions)) {
            Schema::disableForeignKeyConstraints();
            $this->info('Disable foreign key checking');
            $this->info('Start truncating tables');
            DB::table('permissions')->truncate();
            DB::table('role_has_permissions')->truncate();

            $this->info('Start inserting records');
            app()->make(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();
            $this->warn(PHP_EOL . 'Table: permissions');
            $progressBar = $this->output->createProgressBar(count($rolesAndPermissions['permissions']));
            $progressBar->start();
            foreach ($rolesAndPermissions['permissions'] as $perm) {
                DB::table('permissions')->insert($perm);
                $progressBar->advance();
            }
            $progressBar->finish();

            $this->warn(PHP_EOL . 'Table: role_has_permissions');
            $progressBar = $this->output->createProgressBar(count($rolesAndPermissions['role_has_permissions']));
            $progressBar->start();
            foreach ($rolesAndPermissions['role_has_permissions'] as $rhp) {
                DB::table('role_has_permissions')->insert($rhp);
                $progressBar->advance();
            }
            $progressBar->finish();

            Schema::enableForeignKeyConstraints();
            $this->info(PHP_EOL . 'Enable foreign key checking');
        }
    }
}
