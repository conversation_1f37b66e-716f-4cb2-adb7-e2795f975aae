<?php

namespace App\Console\Commands;

use App\Models\Tenant;
use App\Repositories\DailySnapshotRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class DailySnapshot extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wh:daily-snapshot
            {tenant? : tenant id (not used if --all option is used)}
            {--all : process all active tenants}';


    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a daily snapshot of products states for tenant(s)';

    /**
     * Execute the console command.
     */

    public function handle()
    {
        if ($this->option('all')) {
            foreach ($this->getTenantsIds() as $tenantId) {
                $this->info("Processing tenant $tenantId");
                $this->doTenant($tenantId);
            }
            Log::info('Daily snapshot run');
            exit(0);
        }

        $tenantId = $this->argument('tenant');
        if (false === $tenantId) {
            $this->error('Provide tenant id! Aborting.');
            exit(1);
        }

        if (false === $this->checkTenant((int)$tenantId)) {
            $this->error('Tenant not found! Aborting.');
            exit(1);
        }
        $this->doTenant((int)$tenantId);
        Log::info('Daily snapshot run');
    }


    public function doTenant(int $tenantId): void
    {
        $snapClass = new DailySnapshotRepository();

        $date = Carbon::today();

        if ($snapClass->checkSnapshot($date, $tenantId)) {
            $this->warn('Snapshot exist!');
            return;
        }

        $data = $snapClass->prepareData($tenantId);
        $snapClass->prepareModel(data: $data, date: $date, tenant_id: $tenantId);
        $snapClass->save();
        $this->info('Snapshot prepared');
    }

    protected function checkTenant(int $tenant)
    {
        return Tenant::where('id', $tenant)->exists();
    }

    protected function getTenantsIds(): array
    {
        return Tenant::active()->pluck('id')->toArray();
    }
}
