<?php

namespace App\Console\Commands;

use App\Models\WarehouseDoc;
use App\Models\WarehouseItem;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CleanWarehouseDocs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wh:clean-warehouse-docs
    {--soft-deleted : Clean soft deleted}
    {--all : do all cleaning}
    {--no-confirm : don\'t ask confirmation}
    ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('soft-deleted') || $this->option('all')) {
            $this->cleanSoftDelete();
        } else {
            $this->error('You have to choose one option');
            $this->info($this->getSynopsis());
        }
    }

    protected function cleanSoftDelete()
    {
        $this->info('Cleaning soft deleted docs...');
        $found = WarehouseDoc::withTrashed()->whereNotNull('deleted_at')->count();
        if ($this->option('no-confirm') === false &&
            !$this->confirm('Found ' . $found . ' warehouse docs to delete. Continue')) {
            $this->info('Skipping');
            return;
        }
        $this->info('Deleting...');
        $all = WarehouseDoc::withTrashed()->whereNotNull('deleted_at')->get();
        foreach ($all as $doc) {
            $doc->log()->forceDelete();
            $doc->forceDelete();
        }
        $found = WarehouseDoc::withTrashed()->whereNotNull('deleted_at')->count();
        $this->info('Found ' . $found . ' warehouse docs to delete.');
        Log::info('Clean warehouse docs command run');
    }
}
