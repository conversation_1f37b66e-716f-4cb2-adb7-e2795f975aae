<?php

namespace App\Policies;

use App\Models\ProductDemand;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ProductDemandPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any ProductDemand');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ProductDemand $productDemand): bool
    {
        return $user->checkPermissionTo('view ProductDemand');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create ProductDemand');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ProductDemand $productDemand): bool
    {
        return $user->checkPermissionTo('update ProductDemand');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ProductDemand $productDemand): bool
    {
        return $user->checkPermissionTo('delete ProductDemand');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, ProductDemand $productDemand): bool
    {
        return $user->checkPermissionTo('restore ProductDemand');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, ProductDemand $productDemand): bool
    {
        return $user->checkPermissionTo('force-delete ProductDemand');
    }
}
