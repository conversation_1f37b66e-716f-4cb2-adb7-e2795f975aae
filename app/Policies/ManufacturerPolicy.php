<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\Manufacturer;
use App\Models\User;

class ManufacturerPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any Manufacturer');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Manufacturer $manufacturer): bool
    {
        return $user->checkPermissionTo('view Manufacturer');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create Manufacturer');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Manufacturer $manufacturer): bool
    {
        return $user->checkPermissionTo('update Manufacturer');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Manufacturer $manufacturer): bool
    {
        return $user->checkPermissionTo('delete Manufacturer');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Manufacturer $manufacturer): bool
    {
        return $user->checkPermissionTo('restore Manufacturer');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Manufacturer $manufacturer): bool
    {
        return $user->checkPermissionTo('force-delete Manufacturer');
    }
}
