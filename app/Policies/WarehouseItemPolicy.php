<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\WarehouseItem;
use App\Models\User;

class WarehouseItemPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any WarehouseItem');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, WarehouseItem $warehouseitem): bool
    {
        return $user->checkPermissionTo('view WarehouseItem');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create WarehouseItem');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, WarehouseItem $warehouseitem): bool
    {
        return $user->checkPermissionTo('update WarehouseItem');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, WarehouseItem $warehouseitem): bool
    {
        return $user->checkPermissionTo('delete WarehouseItem');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, WarehouseItem $warehouseitem): bool
    {
        return $user->checkPermissionTo('restore WarehouseItem');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, WarehouseItem $warehouseitem): bool
    {
        return $user->checkPermissionTo('force-delete WarehouseItem');
    }
}
