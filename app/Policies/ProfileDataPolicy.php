<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\ProfileData;
use App\Models\User;

class ProfileDataPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any ProfileData');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ProfileData $profiledata): bool
    {
        return $user->checkPermissionTo('view ProfileData');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create ProfileData');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ProfileData $profiledata): bool
    {
        return $user->checkPermissionTo('update ProfileData');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ProfileData $profiledata): bool
    {
        return $user->checkPermissionTo('delete ProfileData');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, ProfileData $profiledata): bool
    {
        return $user->checkPermissionTo('restore ProfileData');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, ProfileData $profiledata): bool
    {
        return $user->checkPermissionTo('force-delete ProfileData');
    }
}
