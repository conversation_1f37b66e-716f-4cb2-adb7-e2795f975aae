<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\WarehouseDoc;
use App\Models\User;

class WarehouseDocPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any WarehouseDoc');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, WarehouseDoc $warehousedoc): bool
    {
        return $user->checkPermissionTo('view WarehouseDoc');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create WarehouseDoc');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, WarehouseDoc $warehousedoc): bool
    {
        return $user->checkPermissionTo('update WarehouseDoc');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, WarehouseDoc $warehousedoc): bool
    {
        return $user->checkPermissionTo('delete WarehouseDoc');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, WarehouseDoc $warehousedoc): bool
    {
        return $user->checkPermissionTo('restore WarehouseDoc');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, WarehouseDoc $warehousedoc): bool
    {
        return $user->checkPermissionTo('force-delete WarehouseDoc');
    }
}
