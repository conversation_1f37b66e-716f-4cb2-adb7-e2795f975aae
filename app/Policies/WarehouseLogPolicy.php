<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\WarehouseLog;
use App\Models\User;

class WarehouseLogPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any WarehouseLog');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, WarehouseLog $warehouselog): bool
    {
        return $user->checkPermissionTo('view WarehouseLog');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create WarehouseLog');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, WarehouseLog $warehouselog): bool
    {
        return $user->checkPermissionTo('update WarehouseLog');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, WarehouseLog $warehouselog): bool
    {
        return $user->checkPermissionTo('delete WarehouseLog');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, WarehouseLog $warehouselog): bool
    {
        return $user->checkPermissionTo('restore WarehouseLog');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, WarehouseLog $warehouselog): bool
    {
        return $user->checkPermissionTo('force-delete WarehouseLog');
    }
}
