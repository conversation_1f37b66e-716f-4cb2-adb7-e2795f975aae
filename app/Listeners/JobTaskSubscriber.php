<?php

namespace App\Listeners;

use App\Events\JobTaskFinished;
use App\Jobs\NotifyTenantAdmins;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Events\Dispatcher;
use Illuminate\Queue\InteractsWithQueue;

class JobTaskSubscriber
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    public function handleJobTaskFinished(JobTaskFinished $event): void
    {
        $message = 'Zlecenie ' . $event->jobTask->name . ' numer: ' . $event->jobTask->number . ' zostało zakończone';
        $title = 'Zlecenie zostało zakończone';
        dispatch(new NotifyTenantAdmins($message, $title, $event->jobTask->installation));
    }

    /**
     * Handle the event.
     */
    public function subscribe(Dispatcher $events): array
    {
        return [
            JobTaskFinished::class => 'handleJobTaskFinished',
        ];
    }
}
