<?php

namespace App\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class OnlyTenantUsers implements Scope
{

    public function __construct()
    {
    }


    /**
     * Apply the scope to a given Eloquent query builder.
     */
    public function apply(Builder $builder, Model $model): void
    {
        if (defined('INSTALLATION')) {
            $builder->whereHas('tenant', function ($q) {
                $q->whereIn('tenants.id', [INSTALLATION]);
            });
        }
    }
}
