<?php

namespace App\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class NoSuperAdminInList implements Scope
{

    /**
     * @inheritDoc
     */
    public function apply(Builder $builder, Model $model): void
    {
            $builder->whereDoesntHave('roles', function (Builder $query) {
                return $query->where('name', '=', 'Super Admin');
            });
    }
}
