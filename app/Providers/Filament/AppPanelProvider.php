<?php

namespace App\Providers\Filament;

use App\Filament\App\Navigation\NavigationManager;
use App\Filament\App\Pages\Auth\Register;
use App\Filament\App\Pages\Auth\RequestPasswordReset;
use App\Http\Middleware\DomainIsCorrect;
use App\Http\Middleware\IsUserActive;
use App\Http\Middleware\UserAllowedToUse;
use App\Models\Tenant;
use Filament\FontProviders\GoogleFontProvider;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\NavigationBuilder;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Assets\Js;
use Filament\Support\Colors\Color;
use Filament\Support\Enums\MaxWidth;
use Filament\Support\Facades\FilamentAsset;
use Filament\Support\Facades\FilamentView;
use Filament\View\PanelsRenderHook;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Http\Request;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class AppPanelProvider extends PanelProvider
{

    public array $tenants = [];

    public function boot(): void
    {
        FilamentAsset::register([
            Js::make('field-filters', 'resources/js/filters.js'),
        ]);

        if (request()?->getHost() !== env('BASE_ADMIN_DOMAIN')) {
            FilamentView::registerRenderHook(
                PanelsRenderHook::STYLES_AFTER,
                static fn(): string => '<link rel="stylesheet" href="/build/assets/theme.css?v=' .
                    config('app.assets_version') .
                    '" data-navigate-track="reload">',
            );
            FilamentView::registerRenderHook(
                PanelsRenderHook::USER_MENU_BEFORE,
                static fn(): string => '<div class="text-sm order-first">'. tenant()?->name . '</div>',
            );
        }
    }

    public function panel(Panel $panel): Panel
    {

        return $panel
            ->default()
            ->domains($this->getSystemDomains())
            ->id('app')
            ->path('app')
            ->colors([
                'primary' => Color::Slate,
            ])
            ->darkMode(false)
            ->brandName(fn() => $this->getBrandName(\request()))
            ->brandLogo(asset('images/tf-toned.svg'))
            ->login()
            ->registration(
                config('app.allow_register') ? Register::class : null
            )
            ->homeUrl(function (Panel $panel) {
                return NavigationManager::getHomeLink($panel);
            })
            ->font(family: 'Lato', provider: GoogleFontProvider::class)
            ->maxContentWidth(MaxWidth::ScreenTwoExtraLarge)
            ->passwordReset(
                requestAction: RequestPasswordReset::class
            )
            ->topNavigation(true)
            ->topbar(true)
            ->breadcrumbs(false)
            ->sidebarCollapsibleOnDesktop(true)
            ->sidebarWidth('280px')
            ->discoverResources(in: app_path('Filament/App/Resources'), for: 'App\\Filament\\App\\Resources')
            ->discoverPages(in: app_path('Filament/App/Pages'), for: 'App\\Filament\\App\\Pages')
            ->userMenuItems(NavigationManager::getUserMenuNavigation())
            ->pages([])
            ->navigation(function (NavigationBuilder $builder): NavigationBuilder {
                return NavigationManager::getNavigation($builder);
            })
            ->discoverWidgets(in: app_path('Filament/App/Widgets'), for: 'App\\Filament\\App\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
//                Widgets\FilamentInfoWidget::class,
            ])
//            ->viteTheme('resources/css/filament/app/theme.css')
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->databaseNotifications()
            ->authMiddleware([
                Authenticate::class,
                DomainIsCorrect::class,
                IsUserActive::class,
                UserAllowedToUse::class,
            ], true);
    }

    public function getBrandName(Request $request): string
    {

        return $this->tenants[$request->getHost()] ?? config('app.name', "TwojeFaktury");
    }


    public function getSystemDomains(): array
    {
        if (app()->runningInConsole()) {
            return [];
        }
        $other = Tenant::query()->whereNotNull('system_domain')
            ->select(['system_domain', 'name'])
            ->get()->pluck('name', 'system_domain')->toArray();
        $this->tenants = $other;
        return [
            config('app.domain.app', '127.0.0.1'),
            ...array_keys($this->tenants)
        ];
    }
}
