<?php

namespace App\Providers;

use App\Repositories\DrugsRepository;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\ServiceProvider;

class DrugImportServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind('DrugImport', function ($app) {
            return new DrugsRepository($this->app->make(Http::class));
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
