<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * TradeDocMetas Table
 *
 * This class represents the structure of the `trade_doc_metas` table.
 *
 * Columns:
 * @property int       $id              Primary key, auto-incrementing identifier (BIGINT, unsigned).
 * @property string    $trade_doc_uuid  Foreign key referencing `trade_docs.uuid` (CHAR(36)).
 * @property array     $meta            Metadata stored as a JSON object (LONGTEXT, validated as JSON).
 * @property string|null $created_at    Timestamp of creation (TIMESTAMP, nullable).
 * @property string|null $updated_at    Timestamp of last update (TIMESTAMP, nullable).
 * @property string|null $deleted_at    Timestamp of soft deletion (TIMESTAMP, nullable).
 *
 * Indexes:
 * - `trade_doc_metas_trade_doc_uuid_index`: On `trade_doc_uuid`.
 *
 * Foreign Keys:
 * - `trade_doc_uuid` references `trade_docs.uuid` (on delete: CASCADE).
 */

class PurchaseDocMeta extends Model
{
    use HasFactory;

    protected $casts = [
       'meta' => 'array'
    ];

    protected $fillable = [
        'meta',
    ];

    public function purchaseDoc(): BelongsTo
    {
        return $this->belongsTo(PurchaseDoc::class, 'trade_doc_uuid');
    }

    public function getBankData(): DTOBankData
    {
        return DTOBankData::make($this->meta['bank_data'] ?? []);
    }

    public function getBankDataItem(string $field): string
    {
        return DTOBankData::make($this->meta['bank_data'] ?? [])->{$field};
    }


    public function getBuyerData($format = 'text'): string
    {
        $buyer = $this->meta['buyer_address'];
        $lines = [];
        $lines[] = $buyer['name'];
        $lines[] = $buyer['address'];
        $lines[] = $buyer['postcode'] . ' ' . $buyer['city'];
        $lines[] = 'VAT: ' . $buyer['vat_id'];
        filled($buyer['phone']) ? $lines[] = $buyer['phone'] : true;
        filled($buyer['email']) ? $lines[] = $buyer['email'] : true;
        return match ($format) {
            'html' => implode('<br>', $lines),
            default => implode(PHP_EOL, $lines),
        };
    }

    public function getSellerData($format = 'text'): string
    {
        $seller = filled($this->meta['seller_address']) ? $this->meta['seller_address'] : $this->meta['issuer_address'];
        $lines = [];
        $lines[] = $seller['name'];
        $lines[] = $seller['address'];
        $lines[] = $seller['postcode'] . ' ' . $seller['city'];
        $lines[] = 'VAT: ' . $seller['vat_id'];
        filled($seller['phone']) ? $lines[] = $seller['phone'] : true;
        filled($seller['email']) ? $lines[] = $seller['email'] : true;
        return match ($format) {
            'html' => implode('<br>', $lines),
            default => implode(PHP_EOL, $lines),
        };
    }
}
