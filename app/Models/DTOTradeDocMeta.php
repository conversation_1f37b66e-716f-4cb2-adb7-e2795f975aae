<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use JsonSerializable;

class DTOTradeDocMeta implements JsonSerializable
{

    public function __construct(
        public array $issuer_address = [],
        public array $seller_address = [],
        public array $buyer_address = [],
        public array $bank_data = [],
        public array $options = [],
        public ?string $note = null,
        public array $vat = [],
        public array $duplicates = [],
        public array $vat_source = [],
        public array $vat_final = [],
        public array $items = [],
        public array $corrected_doc = [],
    ) {
    }

    public static function make(TradeDocMeta|PurchaseDocMeta $tradeDocMeta)
    {
        return new self(...$tradeDocMeta->meta);
    }

    public static function createTradeDoc(TradeDoc $tradeDoc, array $meta, array $data): self
    {
        $buyer_address = Arr::except($tradeDoc->buyer->getAttributes() ?? [], ['id', 'installation']);
        $buyer_address = array_merge($buyer_address, $data['buyerdata']);
        $issuer_address = Arr::except(auth()->user()->getTenant()->getAttributes() ?? [], ['id', 'config']);
        return new self(
            issuer_address: $issuer_address,
            seller_address: $issuer_address,
            buyer_address: $buyer_address,
            bank_data: tenant()->getBankAccounts()->where('bank_account', $meta['bank_account'])->first() ?? [],
            options: [
                'reverse_charge' =>  $meta['options']['reverse_charge'] ?? false,
                'different_seller' => $meta['options']['different_seller'] ?? false,
                'mpp' => $meta['options']['mpp'] ?? false,
            ],
            note: $meta['note'] ?? null,
            vat: $meta['vat'] ?? [],
            duplicates: $meta['duplicates'] ?? [],
            vat_source: $meta['vat_source'] ?? [],
            vat_final: $meta['vat_final'] ?? [],
            items: $meta['items'] ?? [],
        );
    }

    public static function createPurchaseDoc(PurchaseDoc $purchaseDoc, array $meta, array $data): self
    {
        $seller_address = Arr::except($purchaseDoc->seller->getAttributes() ?? [], ['id', 'installation']);
        $seller_address = array_merge($seller_address, $data['sellerdata']);

        return new self(
            issuer_address: $seller_address,
            seller_address: $seller_address,
            buyer_address: Arr::except(tenant()?->getAttributes() ?? [], ['id', 'config']),
            bank_data: DTOBankData::make($meta)->toArray(),
            options: [
                'reverse_charge' =>  $meta['options']['reverse_charge'] ?? false,
                'different_seller' => $meta['options']['different_seller'] ?? false,
                'mpp' => $meta['options']['mpp'] ?? false,
            ],
            note: $meta['note'] ?? null,
            vat: $meta['vat'] ?? [],
            duplicates: $meta['duplicates'] ?? [],
            vat_source: $meta['vat_source'] ?? [],
            vat_final: $meta['vat_final'] ?? [],
            items: $meta['items'] ?? [],
            corrected_doc: $meta['corrected_doc'] ?? [],
        );
    }

    public function addItem($key, $data): void
    {
        $this->items[$key] = $data;
    }

    public function getItem($key): ?array
    {
        return $this->items[$key] ?? null;
    }

    public function removeItem($key): void
    {
        unset($this->items[$key]);
    }

    public function setUpBankAccount(string $bank_account): void
    {
        $this->bank_data = tenant()?->getBankAccounts()->where('bank_account', $bank_account)->first() ?? [];
    }

    public function setUpIssuer(array $basedata, array $issuerdata = []): void
    {
        $this->issuer_address = array_merge($basedata, $issuerdata);
    }

    public function setUpSeller(array $basedata, array $sellerdata = []): void
    {
        $this->seller_address = array_merge($basedata, $sellerdata);
    }


    public function setUpBuyer(array $basedata, array $buyerdata = []): void
    {
        $this->buyer_address = array_merge($basedata, $buyerdata);
    }

    public function setUpVat(array $vat): void
    {
        $this->vat = $vat;
    }

    public function setUpVatFinal(array $vat): void
    {
        $this->vat_final = $vat;
    }

    public function setOption(string $option, mixed $value): void
    {
        $this->options[$option] = $value;
    }

    public function getOption(string $option, mixed $default = ''): mixed
    {
        return $this->options[$option] ?? $default;
    }

    public function setNote(?string $note = null): void
    {
        $this->note = $note;
    }

    public function getNote(mixed $default = ''): mixed
    {
        return $this->note ?? $default;
    }

    public function getVatSummary(): array
    {
        return $this->vat['summary'] ?? [];
    }

    public function toArray(): array
    {
        return [
            'issuer_address' => $this->issuer_address,
            'seller_address' => $this->seller_address,
            'buyer_address' => $this->buyer_address,
            'bank_data' => $this->bank_data,
            'options' => $this->options,
            'note' => $this->note,
            'vat' => $this->vat,
            'vat_source' => $this->vat_source,
            'vat_final' => $this->vat_final,
            'duplicates' => $this->duplicates,
            'items' => $this->items,
            'corrected_doc' => $this->corrected_doc,
        ];
    }

    public function jsonSerialize(): mixed
    {
        return $this->toArray();
    }
}
