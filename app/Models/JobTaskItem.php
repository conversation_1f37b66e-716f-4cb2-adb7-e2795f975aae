<?php

namespace App\Models;

use App\Enums\ProductDemandsStatuses;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property integer id
 * @property integer job_task_id
 * @property integer product_id
 * @property integer installation
 * @property string product_name
 * @property float expected_quantity
 * @property float quantity
 * @property string quantity_unit
 * @property boolean make_demand
 * @property \DateTime created_at
 * @property \DateTime updated_at
 */
class JobTaskItem extends Model
{
    use HasFactory;

    protected static $unguarded = true;

    protected static function booted()
    {
        if (auth()->hasUser()) {
            if ((auth()->user()?->isSuperAdmin() ?? false) !== true) {
                static::addGlobalScope(new \App\Scopes\Installation());
            }
        }
    }

    public function jobTask(): BelongsTo
    {
        return $this->belongsTo(JobTask::class, 'job_task_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Products::class);
    }

    public function demand(): HasOne
    {
        return $this->hasOne(ProductDemand::class, 'context_identifier')
            ->where('context', self::class)
            ->where('product_id', $this->product_id);
    }

    public function createDemand(): ProductDemand
    {
        $pd = new ProductDemand();
        $pd->user_id = $this->jobTask->created_by;
        $pd->installation = $this->installation;
        $pd->context = self::class;
        $pd->context_identifier = $this->id;
        $pd->product_id = $this->product_id;
        $pd->amount = $this->expected_quantity;
        $pd->demand_at = $this->jobTask->planned_date_at ?? Carbon::tomorrow();
        $pd->status = ProductDemandsStatuses::ACTIVE;
        $pd->save();
        return $pd;
    }


    public function removeDemand(): bool
    {
        return $this->demand()->delete();
    }
}
