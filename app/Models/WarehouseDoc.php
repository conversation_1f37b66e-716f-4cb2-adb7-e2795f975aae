<?php

namespace App\Models;

use App\Enums\DocumentGeneralTypes;
use App\Enums\DocumentTypes;
use App\Enums\WarehouseTypes;
use App\Repositories\WarehouseDocsRepository;
use App\Scopes\Installation;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int installation
 * @property int warehouse_id
 * @property int target_warehouse_id
 * @property int user_id
 * @property int signatory_id
 * @property int|DocumentTypes type
 * @property string signatory_name
 * @property string signatory_last_name
 * @property string issuer_data
 * @property int partner_id
 * @property string partner_data
 * @property \DateTime items_issue_date
 * @property int doc_number
 * @property string full_doc_number
 * @property \DateTime doc_date
 * @property int document_series_id
 * @property string transaction_id
 * @property string notes
 * @property string related_doc
 * @property bool accepted
 * @property \DateTime accepted_at
 * @property \DateTime created_at
 * @property \DateTime updated_at
 * @property \DateTime deleted_at
 * @property bool cancelled
 * @property int cancelled_by
 * @property \DateTime cancelled_at
 */
class WarehouseDoc extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'warehouse_docs';
    protected static $unguarded = true;

    protected static function booted()
    {
        if (false === auth()->user()?->isSuperAdmin()) {
            static::addGlobalScope(new Installation());
        }
    }


    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class, 'warehouse_id', 'id');
    }

    public function scopeAccepted(Builder $query): Builder
    {
        return $query->where('accepted', 1);
    }


    public function scopeNotAccepted(Builder $query): Builder
    {
        return $query->where('accepted', 0);
    }


    public function target_warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class, 'target_warehouse_id', 'id');
    }


    public function partner(): BelongsTo
    {
        return $this->belongsTo(Partner::class, 'partner_id', 'id');
    }


    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function signatory(): BelongsTo
    {
        return $this->belongsTo(User::class, 'signatory_id', 'id');
    }

    public function cancelledby(): BelongsTo
    {
        return $this->belongsTo(User::class, 'cancelled_by', 'id');
    }


    public function installation(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'installation', 'id');
    }

    public function log(): HasMany
    {
        return $this->hasMany(WarehouseLog::class, 'document_id');
    }

    public function documentSeries(): BelongsTo
    {
        return $this->belongsTo(DocumentSeriesPattern::class, 'document_series_id', 'id')
            ->whereIn(
                'doc_type',
                collect(DocumentTypes::getDocTypesOf(DocumentGeneralTypes::WAREHOUSE))->pluck('value')->toArray()
            );
    }

    public function getCounterpart(): null|Model|WarehouseDoc
    {
        $qry = $this->newQuery();
        return match ($this->type) {
            DocumentTypes::MW => $qry
                ->where('transaction_id', WarehouseDocsRepository::swapTRIDMWMP($this->transaction_id))
                ->first(),
            DocumentTypes::MP => $qry
                ->where('transaction_id', WarehouseDocsRepository::swapTRIDMPMW($this->transaction_id))
                ->first(),
            default => null
        };
    }

//    public static function getLastDocNumber(WarehouseDocsTypes $type)
//    {
//        return self::where('installation', auth()->user()?->installation())
//            ->where('type', $type->value)->max('doc_number') ?? 0;
//    }

    public function accept(): self
    {
        $this->accepted = true;
        $this->accepted_at = now();
        return $this;
    }

    public function cancel(?string $reason = null): self
    {
        $this->cancelled = true;
        $this->cancelled_by = auth()->user()->id;
        $this->cancelled_at = now();
        $this->notes .= $reason ?? '';
        return $this;
    }

    public function createdAt(): ?\DateTime
    {
        return match ($this->created_at) {
            null => null,
            default => \DateTime::createFromInterface($this->created_at)
                ->setTimezone(new \DateTimeZone('Europe/Warsaw'))
        };
    }

    public function acceptedAt(): ?\DateTime
    {
        return match ($this->accepted_at) {
            null => null,
            default => \DateTime::createFromInterface($this->accepted_at)
                ->setTimezone(new \DateTimeZone('Europe/Warsaw'))
        };
    }

    public function isAccepted(): bool
    {
        return $this->accepted;
    }

    public function isCancelled(): bool
    {
        return $this->cancelled;
    }

    public function itemsIssuedAt(): ?\DateTime
    {
        return match ($this->items_issue_date) {
            null => null,
            default => \DateTime::createFromInterface($this->items_issue_date)
                ->setTimezone(new \DateTimeZone('Europe/Warsaw'))
        };
    }


    protected $casts = [
        'type' => DocumentTypes::class,
        'items_issue_date' => 'datetime:Y-m-d H:i:s',
        'accepted_at' => 'datetime:Y-m-d H:i:s',
        'cancelled_at' => 'datetime:Y-m-d H:i:s',
        'doc_date' => 'date:Y-m-d',
    ];
}
