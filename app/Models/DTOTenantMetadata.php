<?php

namespace App\Models;

use Illuminate\Support\Collection;

class DTOTenantMetadata implements \JsonSerializable
{
    /**
     * @param DTOAccountingData $accounting
     * @param Collection<DTOBankData> $bank_accounts
     */
    public function __construct(
        public readonly DTOAccountingData $accounting,
        public readonly Collection $bank_accounts,
    ) {
    }

    /**
     * Create instance from array data
     */
    public static function make(array $data): self
    {
        $accounting = DTOAccountingData::make($data['accounting'] ?? []);

        $bankAccounts = collect($data['bank_accounts'] ?? [])
            ->map(fn(array $bankData) => DTOBankData::make($bankData));

        return new self(
            accounting: $accounting,
            bank_accounts: $bankAccounts,
        );
    }

    /**
     * Create instance from JSON string
     */
    public static function fromJson(string $json): self
    {
        $data = json_decode($json, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \InvalidArgumentException('Invalid JSON provided: ' . json_last_error_msg());
        }

        return self::make($data);
    }

    /**
     * Create instance from array (alias for make)
     */
    public static function fromArray(array $data): self
    {
        return self::make($data);
    }

    /**
     * Convert to array format
     */
    public function toArray(): array
    {
        return [
            'accounting' => $this->accounting->toArray(),
            'bank_accounts' => $this->bank_accounts->map(fn(DTOBankData $bankData) => $bankData->toArray())->toArray(),
        ];
    }

    /**
     * Convert to JSON string
     */
    public function toJson(int $options = 0): string
    {
        return json_encode($this->toArray(), $options);
    }

    /**
     * JsonSerializable implementation
     */
    public function jsonSerialize(): mixed
    {
        return $this->toArray();
    }

    /**
     * Get accounting data
     */
    public function getAccounting(): DTOAccountingData
    {
        return $this->accounting;
    }

    /**
     * Get bank accounts collection
     */
    public function getBankAccounts(): Collection
    {
        return $this->bank_accounts;
    }

    /**
     * Get bank account by account name
     */
    public function getBankAccountByName(string $accountName): ?DTOBankData
    {
        return $this->bank_accounts->first(fn(DTOBankData $bankData) => $bankData->account_name === $accountName);
    }

    /**
     * Get bank account by bank account number
     */
    public function getBankAccountByNumber(string $accountNumber): ?DTOBankData
    {
        return $this->bank_accounts->first(fn(DTOBankData $bankData) => $bankData->bank_account === $accountNumber);
    }

    /**
     * Get bank accounts by currency
     */
    public function getBankAccountsByCurrency(string $currency): Collection
    {
        return $this->bank_accounts->filter(fn(DTOBankData $bankData) => $bankData->bank_currency === $currency);
    }

    /**
     * Add bank account
     */
    public function addBankAccount(DTOBankData $bankData): self
    {
        $newBankAccounts = $this->bank_accounts->push($bankData);

        return new self(
            accounting: $this->accounting,
            bank_accounts: $newBankAccounts,
        );
    }

    /**
     * Remove bank account by account name
     */
    public function removeBankAccountByName(string $accountName): self
    {
        $newBankAccounts = $this->bank_accounts->reject(fn(DTOBankData $bankData) => $bankData->account_name === $accountName);

        return new self(
            accounting: $this->accounting,
            bank_accounts: $newBankAccounts,
        );
    }

    /**
     * Update accounting data
     */
    public function updateAccounting(DTOAccountingData $accounting): self
    {
        return new self(
            accounting: $accounting,
            bank_accounts: $this->bank_accounts,
        );
    }

    /**
     * Validate the DTO structure
     */
    public function validate(): array
    {
        $errors = [];

//        // Validate accounting data
//        if (empty($this->accounting->regon)) {
//            $errors[] = 'Accounting REGON is required';
//        }
//
//        if (empty($this->accounting->bdo)) {
//            $errors[] = 'Accounting BDO is required';
//        }
//
//        // Validate bank accounts
//        if ($this->bank_accounts->isEmpty()) {
//            $errors[] = 'At least one bank account is required';
//        }
//
//        $this->bank_accounts->each(function (DTOBankData $bankData, int $index) use (&$errors) {
//            if (empty($bankData->account_name)) {
//                $errors[] = "Bank account #{$index}: account_name is required";
//            }
//
//            if (empty($bankData->bank_name)) {
//                $errors[] = "Bank account #{$index}: bank_name is required";
//            }
//
//            if (empty($bankData->bank_account)) {
//                $errors[] = "Bank account #{$index}: bank_account is required";
//            }
//
//            if (empty($bankData->bank_currency)) {
//                $errors[] = "Bank account #{$index}: bank_currency is required";
//            }
//        });

        return $errors;
    }

    /**
     * Check if the DTO is valid
     */
    public function isValid(): bool
    {
        return empty($this->validate());
    }
}
