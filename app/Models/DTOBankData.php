<?php

namespace App\Models;

class DTOBankData implements \JsonSerializable
{
    public function __construct(
        public readonly string $account_name,
        public readonly string $bank_name,
        public readonly string $bank_account,
        public readonly string $bank_swift,
        public readonly ?string $bank_iban,
        public readonly string $bank_currency,
    ) {
    }

    public static function make(array $data): self
    {
        return new self(
            account_name: $data['account_name'] ?? '',
            bank_name: $data['bank_name'] ?? '',
            bank_account: $data['bank_account'] ?? '',
            bank_swift: $data['bank_swift'] ?? '',
            bank_iban: $data['bank_iban'] ?? '',
            bank_currency: $data['bank_currency'] ?? '',
        );
    }

    public function toArray(): array
    {
        return [
            'account_name' => $this->account_name,
            'bank_name' => $this->bank_name,
            'bank_account' => $this->bank_account,
            'bank_swift' => $this->bank_swift,
            'bank_iban' => $this->bank_iban,
            'bank_currency' => $this->bank_currency,
        ];
    }

    public function jsonSerialize(): mixed
    {
        return $this->toArray();
    }
}
