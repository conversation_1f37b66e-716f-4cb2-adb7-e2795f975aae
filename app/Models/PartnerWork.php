<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PartnerWork extends Model
{
    use HasFactory;

    protected $fillable = ['partner_id', 'name', 'short_name', 'address', 'metadata'];

    protected $casts = [
        'metadata' => 'array'
    ];

    public function partner(): BelongsTo
    {
        return $this->belongsTo(Partner::class);
    }

    public function objects(): HasMany
    {
        return $this->hasMany(PartnerObject::class);
    }
}
