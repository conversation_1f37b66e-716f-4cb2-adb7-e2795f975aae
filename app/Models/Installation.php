<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Installation extends Model
{
    use HasFactory;

    protected $table = 'installations';

    protected $fillable = ['tenant_id', 'user_id'];

    public function tenant()
    {
        $this->hasMany(Tenant::class);
    }

    public function user()
    {
        $this->hasMany(User::class);
    }
}
