<?php

namespace App\Models;

use App\Enums\DocumentTypes;
use App\Scopes\Installation;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property DocumentTypes $type
 * @property int $warehouse_id
 * @property int $product_id
 * @property int $amount
 * @property string $unit
 * @property float $price
 * @property bool $is_net
 * @property string $gtin
 * @property string $series_no
 * @property string $transaction_id
 * @property int $document_id
 * @property string $source_doc_id
 * @property \DateTime $exp_date
 * @property int $user_id
 * @property \DateTime $created_at
 * @property int $installation
 */

class WarehouseLog extends Model
{
    use HasFactory;

    public $timestamps = false;

//    protected static $unguarded=true;

    protected $guarded = [
        'updated_at'
    ];

    protected $casts = [
        'type' => DocumentTypes::class,
    ];

    protected static function booted()
    {
        if (false === auth()->user()?->isSuperAdmin()) {
            static::addGlobalScope(new Installation());
        }
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class, 'warehouse_id', 'id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Products::class, 'product_id', 'id');
    }

    public function warehouse_doc(): BelongsTo
    {
        return $this->belongsTo(WarehouseDoc::class, 'document_id', 'id');
    }

    public function items() : HasMany
    {
        return $this->hasMany(WarehouseItem::class, 'transaction_id', 'transaction_id');
    }


    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }


    public function save(array $options = [])
    {
        return parent::save($options); // TODO: Change the autogenerated stub
    }
}
