<?php

namespace App\Models;

use App\Enums\WarehouseTypes;
use App\Helpers\Identifiers;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $hash
 * @property string $name
 * @property string $address
 * @property WarehouseTypes $owner_type
 * @property int $owner_identifier
 * @property int $installation
 * @property bool $is_active
 * @property \DateTime $created_at
 * @property \DateTime $updated_at
 */
class Warehouse extends Model
{
    use HasFactory, SoftDeletes;

    protected static $unguarded = true;

    protected $casts = [
       'owner_type' => WarehouseTypes::class,
       'is_active' => 'bool',
    ];

    protected static function booted(): void
    {
        if (auth()->user() !== null) {
            if (auth()->user()->isSuperAdmin() !== true) {
                static::addGlobalScope(new \App\Scopes\Installation());
            }
        }
    }

    public function scopeActive(Builder $builder): Builder
    {
        return $builder->where('is_active', true);
    }

    public function log(): HasMany
    {
        return $this->hasMany(WarehouseLog::class, 'warehouse_id', 'id');
    }


    public function items(): HasMany
    {
        return $this->hasMany(WarehouseItem::class, 'warehouse_id', 'id');
    }


    public function docs(): HasMany
    {
        return $this->hasMany(WarehouseDoc::class, 'warehouse_id', 'id');
    }

    public function docsWhenTargetWh(): HasMany
    {
        return $this->hasMany(WarehouseDoc::class, 'target_warehouse_id', 'id');
    }

    protected function _user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_identifier', 'id');
    }


    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'installation', 'id');
    }

    public function stocktaking(): HasMany
    {
        return $this->hasMany(Stocktaking::class, 'warehouse_id', 'id');
    }


    public function owner(): BelongsTo
    {
        if ($this->owner_type === WarehouseTypes::COMPANY) {
            return $this->tenant();
        }
        return $this->_user();
    }

    public function save(array $options = [])
    {
        if ($this->hash === null) {
            $this->hash = Identifiers::getRandomHash();
        }
        return parent::save($options); // TODO: Change the autogenerated stub
    }

}
