<?php

namespace App\Models;

use App\Scopes\Installation;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Manufacturer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'hash',
        'phone',
        'email',
        'contact_name',
        'website',
        'address',
        'is_active',
        'installation',
    ];

    protected static function booted()
    {
        if (false === auth()->user()?->isSuperAdmin()) {
            static::addGlobalScope(new Installation());
        }
    }

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'installation', 'id');
    }

    public function scopeHash(Builder $query, $hash)
    {
        return $query->where('hash', $hash);
    }
}
