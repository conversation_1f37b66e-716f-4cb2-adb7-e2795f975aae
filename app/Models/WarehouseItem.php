<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $warehouse_id
 * @property int $product_id
 * @property int $amount
 * @property float $price
 * @property bool $is_net
 * @property string $hash
 * @property string $gtin
 * @property string $series_no
 * @property string $transaction_id
 * @property bool $reserved
 * @property string $reserved_by
 * @property \DateTime $exp_date
 * @property int $minimum_exp_date
 * @property int $minimum_stock
 * @property \DateTime $created_at
 * @property \DateTime $updated_at
 * @property int $installation
 * @property \DateTime $deleted_at
 */
class WarehouseItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'warehouse_items';

    protected $guarded = [
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'exp_date' => 'datetime:Y-m-d'
    ];

    protected static function booted(): void
    {
        if (auth()->hasUser()) {
            if ((auth()->user()?->isSuperAdmin() ?? false) !== true) {
                static::addGlobalScope(new \App\Scopes\Installation());
            }
        }
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function product()
    {
        return $this->belongsTo(Products::class);
    }

    public function transaction(): BelongsTo
    {
        return $this->belongsTo(WarehouseLog::class, 'transaction_id', 'transaction_id');
    }

    public function booker(): BelongsTo
    {
        return $this->belongsTo(WarehouseLog::class, 'reserved_by', 'transaction_id');
    }

    public function scopeIsReserved(Builder $query, bool $isReserved)
    {
        return $query->where('reserved', (int) $isReserved);
    }

    public function removeReservation(): self
    {
        $this->reserved = false;
        $this->reserved_by = null;
        $this->save();
        return $this;
    }
}
