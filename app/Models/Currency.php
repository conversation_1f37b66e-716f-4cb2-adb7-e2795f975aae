<?php

namespace App\Models;

use App\Repositories\CurrenciesRepository;

class Currency
{
    public function __construct(
        public readonly string $alphabeticCode,
        public readonly string $currency,
        public readonly int $minorUnit,
        public readonly int $numericCode
    ) {
    }

    public static function make(string $currencyCode): null|self
    {
        return CurrenciesRepository::getCurrencyModel($currencyCode);
    }

    public function getLabel(): string
    {
        return $this->currency . ' (' . $this->alphabeticCode . ')';
    }
}
