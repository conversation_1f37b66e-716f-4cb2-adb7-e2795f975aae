<?php

namespace App\Models;

use App\Enums\JobTaskStatuses;
use App\Enums\WarehouseTypes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class JobTask extends Model
{
    use HasFactory;

    protected static $unguarded = true;

    protected $casts = [
        'configuration' => 'array',
        'objects' => 'array',
        'planned_date_at' => 'date',
        'done_at' => 'date',
        'status' => 'bool'
    ];

    protected static function booted()
    {
        if (auth()->hasUser()) {
            if ((auth()->user()?->isSuperAdmin() ?? false) !== true) {
                static::addGlobalScope(new \App\Scopes\Installation());
            }
        }
    }


    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'installation', 'id');
    }


    public function partner(): BelongsTo
    {
        return $this->belongsTo(Partner::class);
    }


    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function object(): HasMany
    {
        return $this->hasMany(PartnerObject::class, 'id', 'objects');
    }

    public function objects(): Builder
    {
        return PartnerObject::query()->whereIn('id', $this->objects);
    }

    public function executor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'executed_by')
            ->whereHas('warehouse', function (Builder $query) {
                $query->where('owner_type', WarehouseTypes::USER->value);
            });
    }

    public function items(): HasMany
    {
        return $this->hasMany(JobTaskItem::class);
    }

    public function isActive(): bool
    {
        return $this->status;
    }

    public function finishJobTask(?string $comment = null): self
    {
        $this->done_at = now();
        $this->comment .= $comment;
        $this->status = JobTaskStatuses::COMPLETED->value;
        $this->save();
        return $this;
    }

    public function getPreProductsConfig(): array
    {
        $result = [];
        foreach ($this->configuration as $item) {
            if ($item['is_products_list']) {
                break;
            }

            $result[] = $item;
        }
        return $result;
    }

    public function getProductsConfig(): bool|array
    {
        $result = false;
        foreach ($this->configuration as $item) {
            if ($item['is_products_list']) {
                return $item;
            }
        }
        return $result;
    }

    public function getPostProductsConfig(): array
    {
        $result = [];
        if (blank(collect($this->configuration)->filter(fn($item) => $item['is_products_list'] === true)->toArray())) {
            return $result;
        }
        foreach (array_reverse($this->configuration) as $item) {
            if ($item['is_products_list']) {
                break;
            }

            $result[] = $item;
        }
        return array_reverse($result);
    }

    public function canIssueWHDocument(): bool
    {
        if (blank($this->executor)) {
            return false;
        }

        if ($this->items->count() === 0) {
            return false;
        }

        foreach ($this->items as $item) {
            if (($item->quantity ?? 0) === 0) {
                return false;
            }
            if ($this->executor
                    ->warehouse
                    ->items()
                    ->where('product_id', $item->product_id)
                    ->sum('amount') < $item->quantity) {
                return false;
            }
        }

        return true;
    }
}
