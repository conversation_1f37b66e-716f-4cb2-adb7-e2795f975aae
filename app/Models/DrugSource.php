<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DrugSource extends Model
{
    use HasFactory;
    use HasUlids;

    protected $table = 'drug_source';

    protected $fillable = [
        'name',
        'long_name',
        'gtin',
        'system_id',
        'removed',
        'json'
    ];

    protected $casts =[
        'json' => 'array',
        'removed' => 'bool'
    ];

    public function setLongName($pojemnosc = "", $jednostka_poj = "", $rodzaj_op = "", $liczba_op = "")
    {

        $a = array_filter([$pojemnosc, $jednostka_poj], fn($el) => !empty($el));
        $b = array_filter([$liczba_op, $rodzaj_op], fn($el) => !empty($el));


        $f = match (!empty($a)) {
            true => implode(" ", $a),
            false => "",
        };

        $s = match (!empty($b)) {
            true => implode(" ", $b),
            false => "",
        };

        $c = array_filter([$f, $s], fn($el) => !empty($el));
        $suffix = implode(", ", $c);

        $this->long_name = $this->name . " " . $suffix;
    }
}
