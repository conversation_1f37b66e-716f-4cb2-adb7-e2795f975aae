<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Database\Eloquent\Model;

/**
 * @property \DateTime for_date
 * @property int tenant
 * @property Json data
 * @property \DateTime created_at
 * @property \DateTime updated_at
 */
class DailySnapshots extends Model
{


    protected $fillable = [
        'data',
        'tenant',
        'for_date'
    ];


    protected $casts = [
        'data' => 'array',
        'created_at'=>'datetime:',
        'updated_at'=>'datetime',
        'for_date'=>'datetime',
    ];
}
