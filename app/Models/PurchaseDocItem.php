<?php

namespace App\Models;

use App\Enums\MoneyVOCast;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * PurchaseDocItems Table
 *
 * This class represents the structure of the `purchase_doc_items` table.
 *
 * Columns:
 * @property string $uuid               Primary key, unique identifier (CHAR(36)).
 * @property string $purchase_doc_uuid     Foreign key referencing `trade_docs.uuid` (CHAR(36)).
 * @property string $source_id          Source identifier (CHAR(36)) - used when in Correction mode.
 * @property int    $installation       Installation identifier (BIGINT, unsigned).
 * @property string $label              Description label for the item (TEXT).
 * @property int    $net_unit_price     Net unit price of the item (INT).
 * @property int    $gross_unit_price   Gross unit price of the item (INT).
 * @property int    $discount_type      Type of discount applied (INT).
 * @property int    $discount_value     Value of the discount applied (INT).
 * @property int    $discounted_unit_price Discounted unit price of the item (INT).
 * @property float  $amount             Quantity of the item (DECIMAL(16,6)).
 * @property string $unit_type          Unit type (e.g., "szt.") (VARCHAR(255), default: 'szt.').
 * @property int|null $net_value        Net value of the item (INT, nullable).
 * @property int|null $vat_rate         VAT rate applied (INT, nullable).
 * @property string|null $vat_label     Label for the VAT rate (VARCHAR(20), nullable).
 * @property int|null $vat_value        VAT value calculated (INT, nullable).
 * @property int    $gross_value        Gross value of the item (INT).
 * @property string|null $created_at    Timestamp of creation (TIMESTAMP, nullable).
 * @property string|null $updated_at    Timestamp of last update (TIMESTAMP, nullable).
 * @property array|null $tags    itemtags.
 *
 * Indexes:
 * - `trade_doc_items_installation_index`: On `installation`.
 * - `trade_doc_items_installation_trade_doc_uuid_index`: On `installation`, `trade_doc_uuid`.
 * - `trade_doc_items_trade_doc_uuid_foreign`: On `trade_doc_uuid`.
 *
 * Foreign Keys:
 * - `trade_doc_uuid` references `trade_docs.uuid` (on delete: CASCADE).
 */

class PurchaseDocItem extends Model
{
    use HasFactory, HasUuids;

    protected $primaryKey = 'uuid';
    protected $keyType = 'string';

    public static $unguarded = true;

    protected $casts = [
        'net_unit_price' => MoneyVOCast::class,
        'gross_unit_price' => MoneyVOCast::class,
        'discount_value' => MoneyVOCast::class,
        'discounted_unit_price' => MoneyVOCast::class,
        'net_value' => MoneyVOCast::class,
        'vat_value' => MoneyVOCast::class,
        'gross_value' => MoneyVOCast::class,
        'tags' => 'array',
    ];

    public function purchaseDoc(): BelongsTo
    {
        return $this->belongsTo(PurchaseDoc::class, 'purchase_doc_uuid');
    }
}
