<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class Stocktaking
 *
 * Represents the `stocktaking` table in the database.
 *
 * @property int $id Primary key, auto-incrementing.
 * @property string $hash Unique hash identifier for the stocktaking entry (24 characters max).
 * @property string $for_date Date of the stocktaking in `YYYY-MM-DD` format.
 * @property int $warehouse_id ID of the associated warehouse (unsigned).
 * @property int $creator_id ID of the user who created the stocktaking entry (unsigned).
 * @property int|null $user_id ID of the user associated with the stocktaking entry (nullable, unsigned).
 * @property float $expected_value Expected stock value (decimal with precision 10, scale 2).
 * @property float $real_value Real stock value (decimal with precision 10, scale 2).
 * @property float $difference_value Difference between expected and real value (decimal with precision 10, scale 2).
 * @property string|null $accepted_at Timestamp of when the stocktaking was accepted (nullable).
 * @property int $installation Installation ID (unsigned).
 * @property string|null $created_at Timestamp of when the record was created (nullable).
 * @property string|null $updated_at Timestamp of when the record was last updated (nullable).
 */

class Stocktaking extends Model
{
    use HasFactory;

    protected $table = 'stocktaking';

    protected $fillable = [
        'hash',
        'for_date',
        'warehouse_id' ,
        'creator_id',
        'user_id',
        'expected_value' ,
        'real_value' ,
        'difference_value'
    ];

    protected $casts = [
        'accepted_at' => 'datetime:Y-m-d H:i:s',
        'for_date' => 'date:Y-m-d',
    ];

    protected static function booted(): void
    {
        if (auth()->user() !== null) {
            if (auth()->user()->isSuperAdmin() !== true) {
                static::addGlobalScope(new \App\Scopes\Installation());
            }
        }
    }

    public function scopeHash(Builder $query, string $hash)
    {
        $query->where('hash', $hash);
    }

    public function scopeActive(Builder $query)
    {
        $query->whereNull('accepted_at');
    }

    public function scopeClosed(Builder $query)
    {
        $query->whereNotNull('accepted_at');
    }

    public function accept(?float $real_value = null)
    {
        $this->accepted_at = now();
        $this->real_value = match ($real_value) {
            null => $this->real_value,
            default => $real_value
        };
    }


    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class, 'warehouse_id');
    }


    public function items(): HasMany
    {
        return $this->hasMany(StocktakingItems::class);
    }

    public function save(array $options = [])
    {
        $this->attributes['installation'] ??= tenant()->id;
        return parent::save($options);
    }
}
