<?php

namespace App\Models;

use App\Enums\DocumentPatternResetSwitch;
use App\Enums\DocumentTypes;
use App\Scopes\Installation;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @property int $id
 * @property int $installation
 * @property int|DocumentTypes $doc_type
 * @property string $name
 * @property string $pattern
 * @property bool $is_active
 * @property bool $is_default
 * @property bool $is_blocked
 * @property string|DocumentPatternResetSwitch $switch
 * @property string $counter_key
 * @property \DateTime $created_at
 * @property \DateTime $updated_at
 */
class DocumentSeriesPattern extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'document_series';
    protected $numbers_table = 'document_series_numbers';

    protected static $unguarded = true;

    protected $attributes = [
        'switch' => 'cy'
    ];

    protected $casts = [
        'doc_type' => DocumentTypes::class,
        'switch' => DocumentPatternResetSwitch::class,
        'is_active' => 'bool',
        'is_default' => 'bool',
        'is_blocked' => 'bool',
    ];

    protected ?int $concrete_number = null;
    protected ?string $full_number = null;

    protected static function booted()
    {
        if (false === auth()->user()?->isSuperAdmin()) {
            static::addGlobalScope(new Installation());
        }
    }

    public function warehouseDocuments(): HasMany
    {
        return $this->hasMany(WarehouseDoc::class, 'document_series_id', 'id');
    }

    public function tradeDocuments(): HasMany
    {
        return $this->hasMany(TradeDoc::class, 'document_series_id', 'id');
    }

    public function seriesCounters(): HasMany
    {
        return $this->hasMany(DocumentSeriesNumbers::class, 'document_series_id');
    }


    public function lastSeriesCounter(): HasOne
    {
        return $this->hasOne(DocumentSeriesNumbers::class, 'document_series_id')
            ->orderByDesc('year')->orderByDesc('month')->limit(1);
    }

    public function lastSeriesCounterForDate(\DateTimeInterface $date): HasOne
    {
        return $this->hasOne(DocumentSeriesNumbers::class, 'document_series_id')
            ->where('year', $date->format('Y'))
            ->when(
                $this->switch === DocumentPatternResetSwitch::SWITCH_YEARLY,
                function (Builder $builder) {
                    return $builder->where('month', 0);
                },
                function (Builder $builder) use ($date) {
                    return $builder->where('month', $date->format('n'));
                }
            );
    }

    public function getLastNumberInSeries(\DateTimeInterface $date): int
    {
        return $this->lastSeriesCounterForDate($date)->first()?->counter ?? 0;
    }

    public function isEnabledToEdit(): bool
    {
        $amount = $this->seriesCounters()->count();
        return $this->is_blocked === false &&
            ($amount === 0 || ($amount === 1 and $this->seriesCounters->first()->counter === 0));
    }

    public function isEnabledToRemove(): bool
    {
        $amount = $this->seriesCounters()->count();
        return ($amount === 0 || ($amount === 1 and $this->seriesCounters->first()->counter === 0)) &&
            $this->is_default === false;
    }


    public function save(array $options = [])
    {
        parent::save($options);
        if (blank($this->counter_key)) {
            $this->counter_key = $this->counterKeyGenerator(now());
            parent::save($options);
        }
    }


    public function counterKeyGenerator(\DateTime $date): string
    {
        if ($this->switch === 'cy') {
            return implode('-', [$this->installation, $this->id, $date->format('Y')]);
        }

        return implode('-', [$this->installation, $this->id, $date->format('Y'), $date->format('m')]);
    }

    public function scopeDefault(Builder $query): Builder
    {
        return $query->where('is_default', true);
    }


    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    public function scopeBlocked(Builder $query): Builder
    {
        return $query->where('is_blocked', true);
    }

    public function scopeActiveToEdit(Builder $query): Builder
    {
        return $query->where('is_active', true)
            ->where('is_blocked', false);
    }


    public function scopeDocType(Builder $query, DocumentTypes|int $docType): Builder
    {
        $type = match (is_int($docType)) {
            false => $docType->value,
            default => $docType
        };
        return $query->where('doc_type', $type);
    }

    public static function getPatternMarkers(): array
    {
        return [
            '%L%' => 'Licznik',
            '%M%' => 'Miesiąc (1 cyfra)',
            '%MM%' => 'Miesiąc (2 cyfry) ',
            '%YY%' => 'Rok (2 cyfry)',
            '%YYYY%' => 'Rok (4 cyfry)'
        ];
    }

    public static function getPatternSwitch(): array
    {
        return [
            'cy' => 'Resetuj co rok',
            'cm' => 'Resetuj co miesiąc'
        ];
    }

    public function generateNextDocNumber(\DateTime $date): self
    {
        $number = null;
        DB::transaction(function () use (&$number, $date) {
            $qry = DB::table($this->numbers_table)
                ->where('document_series_id', $this->id)
                ->where('year', $date->format('Y'));
            if ($this->switch === DocumentPatternResetSwitch::SWITCH_MONTHLY) {
                $qry->where('month', $date->format('n'));
            } else {
                $qry->where('month', 0);
            }
            $row = $qry->lockForUpdate()->first();
            if (empty($row)) {
                $this->createSeriesNumberRow($date, 1);
                $number = 1;
            } else {
                $number = $row->counter + 1;
                $qry->update(['counter' => $number]);
            }
        });
        $this->concrete_number = $number;
        $this->full_number = $this->generateFullNumber($this->concrete_number, $date);
        return $this;
    }

    public function generateFullNumber(int $counter, \DateTime $date): string
    {
        $markers = array_keys(self::getPatternMarkers());
        return str_replace(
            $markers,
            [$counter, $date->format('n'), $date->format('m'), $date->format('y'), $date->format('Y')],
            $this->pattern
        );
    }

    public static function generateFullNumberStatic(string $pattern, int $counter, \DateTime $date): string
    {
        $markers = array_keys(self::getPatternMarkers());
        return str_replace(
            $markers,
            [$counter, $date->format('n'), $date->format('m'), $date->format('y'), $date->format('Y')],
            $pattern
        );
    }

    public function getFullNumber(): ?string
    {
        return $this->full_number;
    }

    public function getConcreteNumber(): ?int
    {
        return $this->concrete_number;
    }

    public function rollbackNumber($date, $counter): bool
    {
        $success = false;

        DB::transaction(function () use ($date, $counter, &$success) {
            $row = $this->lastSeriesCounterForDate($date)
                ->where('counter', $counter)
                ->lockForUpdate()
                ->first();
            if (filled($row)) {
                $success = (bool)$row->decrement('counter');
            }
        });

        return (bool)$success;
    }


    public function createSeriesNumberRow(\DateTime $date, $counter = 0): void
    {
        DB::table($this->numbers_table)
            ->insert([
                'document_series_id' => $this->id,
                'year' => $date->format('Y'),
                'month' => $this->switch->value === 'cy' ? 0 : $date->format('n'),
                'counter' => $counter,
                'created_at' => now(),
            ]);
    }
}
