<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Althinect\FilamentSpatieRolesPermissions\Concerns\HasSuperAdmin;
use App\Enums\Roles;
use App\Enums\WarehouseTypes;
use App\Helpers\Identifiers;
use App\Scopes\NoSuperAdminInList;
use App\Scopes\OnlyTenantUsers;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements FilamentUser
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, HasSuperAdmin, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'active'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'active' => 'boolean',
    ];

    protected static function booted(): void
    {

//        if (auth()->hasUser()) {
//            if (auth()->user()->isGod() !== true) {
//                static::addGlobalScope(new NoSuperAdminInList());
//                static::addGlobalScope(new OnlyTenantUsers(auth()->user()->installation()));
//            }
//        }
    }


    public function canAccessPanel(Panel $panel): bool
    {

        if (!$this->isActive()) {
            return false;
        }

        if ($panel->getId() === 'app' && blank($this->installation())) {
            return false;
        }

        if ($panel->getId() === 'admin' && !$this->isGod()) {
            return false;
        }

        return true;
    }

    public function tenant(): BelongsToMany
    {
        return $this->belongsToMany(
            related: Tenant::class,
            table: 'installations',
            foreignPivotKey: 'user_id',
            relatedPivotKey: 'tenant_id'
        );
    }

    public function getTenant(): ?Model
    {
        return $this->tenant->first();
    }

    public function installation()
    {
        return $this->getTenant()?->id;
    }

    public function profile(): HasOne
    {
        return $this->hasOne(ProfileData::class);
    }

    public function fullName(): string
    {
        return $this->profile?->fullName() ?? '';
    }

    public function isTenantAdmin(): bool
    {
        return $this->hasRole(Roles::TENANT_ADMIN->value);
    }

    public function isGod(): bool
    {
        return $this->hasRole(Roles::SUPER_ADMIN->value);
    }

    public function isActive()
    {
        return $this->active;
    }

    public function warehouse(): HasOne
    {
        return $this->hasOne(Warehouse::class, 'owner_identifier')
            ->where('owner_type', WarehouseTypes::USER->value);
    }

    public function hasWarehouse(): bool
    {
        return $this->warehouse()->count() > 0;
    }

    /**
     * @return HasManyThrough
     * @link https://laravel.com/docs/10.x/eloquent-relationships#has-many-through
     */
    public function partners(): HasManyThrough
    {
        return $this->hasManyThrough(
            Partner::class,
            Installation::class,
            'user_id',
            'installation',
            'id',
            'tenant_id'
        );
    }

    public function scopeForTenant(Builder $builder)
    {
        $builder->whereHas('tenant', function ($q) {
            $q->whereIn('tenants.id', [INSTALLATION]);
        });
    }

    public function scopeNotGod(Builder $builder)
    {
        $builder->whereDoesntHave('roles', function (Builder $query) {
            return $query->where('id', '=', Roles::SUPER_ADMIN->value);
        });
    }

    public function scopeActive(Builder $builder)
    {
        $builder->where('active', 1);
    }

    public function save(array $options = []): bool
    {
        if (blank($this->attributes['hash'] ?? null)) {
            $this->attributes['hash'] = Identifiers::getRandomHash(16);
        }
        return parent::save($options);
    }
}
