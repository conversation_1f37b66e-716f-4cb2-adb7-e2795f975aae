<?php

namespace App\Models;

use App\Enums\PartnerBusinessTypes;
use App\Enums\PartnerVATTypes;
use App\Enums\TaxResidencyCountries;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class Partners
 *
 * @property int $id Primary key, auto-incremented
 * @property int $installation Installation identifier (bigint unsigned)
 * @property string $hash Unique hash identifier (64 characters)
 * @property string $name Name of the partner (255 characters)
 * @property string $short_name Name of the partner (127 characters)
 * @property string|null $address Address of the partner (TEXT)
 * @property string|null $postcode Postal code (100 characters)
 * @property string|null $city City name (100 characters)
 * @property string|null $country_id Country identifier (4 characters)
 * @property string|null $phone Phone number (100 characters)
 * @property string|null $email Email address (60 characters)
 * @property string|null $contact_name Contact person's name (255 characters)
 * @property string|null $website Website URL (255 characters)
 * @property string|null $bank_name Name of the bank (255 characters)
 * @property string|null $bank_iban IBAN of the bank account (255 characters)
 * @property string|null $bank_swift SWIFT/BIC code of the bank (255 characters)
 * @property string|null $bank_account Bank account number (255 characters)
 * @property string|null $vat_id VAT identification number (255 characters)
 * @property int $vat_type Type of VAT (smallint unsigned, default 1)
 * @property int $business_type Business type identifier (smallint unsigned, default 1)
 * @property string|null $tax_residency_country Tax residency country code (4 characters)
 * @property bool $is_active Whether the partner is active (0 = inactive, 1 = active, default 1)
 * @property string|null $created_at Timestamp of creation
 * @property string|null $updated_at Timestamp of last update
 * @property string|null $deleted_at Timestamp of deletion (nullable, soft delete)
 */

class Partner extends Model
{
    use HasFactory, SoftDeletes;

    protected static $unguarded = true;

    protected $casts = [
        'vat_type' => PartnerVATTypes::class,
        'business_type' => PartnerBusinessTypes::class,
        'tax_residency_country' => TaxResidencyCountries::class,
    ];

    protected static function booted(): void
    {
        if (auth()->user() !== null) {
            if (auth()->user()->isSuperAdmin() !== true) {
                static::addGlobalScope(new \App\Scopes\Installation());
            }
        }
    }


    public function objects(): HasMany
    {
        return $this->hasMany(PartnerObject::class);
    }

    public function meta(): HasMany
    {
        return $this->hasMany(PartnerMeta::class);
    }

    public function workshops(): HasMany
    {
        return $this->hasMany(PartnerWork::class);
    }


    public function warehouse_docs():HasMany
    {
        return $this->hasMany(WarehouseDoc::class, 'partner_id', 'id');
    }

    public function save(array $options = [])
    {
        if ($this->hash === null) {
            $this->hash = bin2hex(random_bytes(16));
        }
        return parent::save($options);
    }

    public function getAddressText($format = 'text')
    {
        $lines = [];
        $lines[] = $this->name;
        $lines[] = $this->address;
        $lines[] = $this->postcode . ' ' . $this->city;
        $lines[] = $this->phone;
        $lines[] = $this->website . ' ' . $this->email;
        return match ($format) {
            'html' => implode('<br>', $lines),
            default => implode(PHP_EOL, $lines),
        };
    }

    public function getInvoiceText($format = 'text')
    {
//        $lines = [];
        $lines['name'] = $this->name;
        $lines['address'] = $this->address;
        $lines['city'] = $this->postcode . ' ' . $this->city;
        $this->phone ? $lines['phone'] = $this->phone : true;
        $lines['tax_id'] = 'VAT: ' . $this->vat_id;
        return match ($format) {
            'html' => implode('<br>', $lines),
            'array' => $lines,
            default => implode(PHP_EOL, $lines),
        };
    }

    public function displayName(): string
    {
        return $this->short_name ?? $this->name;
    }
}
