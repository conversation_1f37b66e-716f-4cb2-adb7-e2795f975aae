<?php

namespace App\Models;

use App\Enums\WarehouseItemTypes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Products Table
 *
 * @property int $id Unique identifier for the product
 * @property string $name Product name
 * @property string $hash Unique hash for the product
 * @property int|null $manufacturer_id Reference to the manufacturer
 * @property float|null $price_per_unit Price per unit (decimal 8,2)
 * @property bool $is_net Indicates if the price is net (1) or gross (0)
 * @property int|null $vat_rate VAT rate percentage
 * @property string|null $vat_label VAT description label
 * @property string|null $basic_unit Basic unit of measurement
 * @property int|null $volume_ml Volume in milliliters
 * @property int|null $weight_gr Weight in grams
 * @property bool $limited_stock Whether stock is limited (1) or not (0)
 * @property bool $below_stock Can be issued from storage when stock is zero
 * @property int $item_type Type of item (default: 1)
 * @property string|null $description Product description
 * @property string|null $grace_period Grace period details
 * @property int $minimum_stock Minimum stock level (default: 5)
 * @property int|null $minimum_exp_date Minimum expiration date (in days)
 * @property string|null $gtin Global Trade Item Number (GTIN)
 * @property string|null $extra_code Additional code (length: 225)
 * @property string $ext_link External link (default: empty string)
 * @property bool $is_active Whether the product is active (1) or inactive (0)
 * @property int $installation Installation reference ID
 * @property string|null $data_source Data source name
 * @property string|null $data_source_identifier Data source identifier
 * @property string|null $created_at Timestamp when the product was created
 * @property string|null $updated_at Timestamp when the product was last updated
 * @property string|null $deleted_at Timestamp when the product was deleted (soft delete)
 */

class Products extends Model
{
    use HasFactory, SoftDeletes;


    protected static $unguarded = true;

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'installation');
    }

    public function manufacturer(): BelongsTo
    {
        return $this->belongsTo(Manufacturer::class, 'manufacturer_id');
    }

    public function manufacturerHash(Builder $query, $hash): Builder
    {
        return $query->whereIn('manufacturer_id', Manufacturer::hash($hash)->first()->id);
    }

    protected static function booted(): void
    {
        if (auth()->hasUser()) {
            if ((auth()->user()?->isSuperAdmin() ?? false) !== true) {
                static::addGlobalScope(new \App\Scopes\Installation());
            }
        }
    }

    public function demands(): HasMany
    {
        return $this->hasMany(ProductDemand::class, 'product_id');
    }

    public function getGrossPriceInt(): int
    {
        if (blank($this->price_per_unit)) {
            return 0;
        }

        return (int) match ((bool)$this->is_net) {
            true => $this->price_per_unit * (100 + $this->vat_rate),
            default => $this->price_per_unit * 100,
        };
    }


    public function getNetPriceInt(): int
    {
        if (blank($this->price_per_unit)) {
            return 0;
        }

        return (int) match ((bool) $this->is_net) {
            true => $this->price_per_unit * 100,
            default => $this->price_per_unit * 100 / (1 + ($this->vat_rate / 100)),
        };
    }

    public function scopeType(Builder $query, WarehouseItemTypes $type): Builder
    {
        return $query->where('item_type', $type->value);
    }
}
