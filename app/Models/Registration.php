<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Registration extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'email',
        'vat_id',
        'confirmation_code',
        'code_sent_at',
        'confirmed_at',
        'finished_at',
        'registration_hash',
        'data',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'code_sent_at' => 'datetime',
        'confirmed_at' => 'datetime',
        'finished_at' => 'datetime',
        'data' => 'array',
    ];

    /**
     * Generate a random confirmation code.
     *
     * @return string
     */
    public static function generateConfirmationCode(): string
    {
        return str_pad((string) random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    }

    /**
     * Check if the registration is confirmed.
     *
     * @return bool
     */
    public function isConfirmed(): bool
    {
        return $this->confirmed_at !== null;
    }

    public function isFinished(): bool
    {
        return $this->finished_at !== null;
    }
}
