<?php

namespace App\Models;

use App\Interface\StocktakingInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class StocktakingItem
 *
 * Represents the `stocktaking_items` table in the database.
 *
 * @property int $id Primary key, auto-incrementing.
 * @property string $hash Unique hash identifier for the stocktaking item (24 characters max).
 * @property int $stocktaking_id Reference to the `stocktaking` table (unsigned).
 * @property int $product_id ID of the associated product (unsigned).
 * @property string|null $gtin Global Trade Item Number (nullable, up to 100 characters).
 * @property string|null $series_no Series or batch number (nullable, up to 100 characters).
 * @property int $base_amount Expected quantity of the product.
 * @property int $real_amount Actual quantity of the product.
 * @property int $difference_amount Difference between expected and actual quantities.
 * @property float $price Unit price of the product (decimal with precision 8, scale 2).
 * @property float $expected_value Expected total value (decimal with precision 8, scale 2).
 * @property float $real_value Actual total value (decimal with precision 8, scale 2).
 * @property string|null $created_at Timestamp of when the record was created (nullable).
 * @property string|null $updated_at Timestamp of when the record was last updated (nullable).
 */

class StocktakingItems extends Model implements StocktakingInterface
{
    protected $table = 'stocktaking_items';
    protected $fillable = [
        'hash',
        'stocktaking_id',
        'product_id',
        'gtin' ,
        'series_no',
        'base_amount' ,
        'real_amount',
        'difference_amount',
        'price',
        'expected_value',
        'real_value'
    ];

    public function createFromArray(array $dataToSave)
    {
        $this->insert($dataToSave);
    }

    public function updateEntry($entryId, $value)
    {
        $entry = $this::find($entryId);
        $entry->real_amount = $value;
        $entry->difference_amount = $value - $entry->base_amount;
        $entry->save();
    }

    public function updateRealAmount($value)
    {
        $this->real_amount = $value;
        $this->difference_amount = $value - $this->base_amount;
        $this->real_value = $this->real_amount * $this->price;
        $this->save();
    }

    public function acceptStockItem()
    {
        $this->difference_amount = (int)$this->real_amount - (int)$this->base_amount;
        $this->real_value = $this->real_amount * $this->price;
        $this->save();
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Products::class, 'product_id');
    }


    public function stocktaking(): BelongsTo
    {
        return $this->belongsTo(Stocktaking::class, 'stocktaking_id');
    }
}
