<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PartnerObject extends Model
{
    use HasFactory;

    protected static $unguarded = true;

    protected $casts = [
        'metadata' => 'array'
    ];


    public function getMetaValue($name, $default = null): mixed
    {
        foreach ($this->metadata as $record) {
            if ($record['name'] === $name) {
                return $record['value'];
            }
        }
        return $default;
    }

    public function partner(): BelongsTo
    {
        return $this->belongsTo(Partner::class);
    }

    public function work(): BelongsTo
    {
        return $this->belongsTo(PartnerWork::class, 'partner_work_id');
    }
}
