<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JobTaskTemplate extends Model
{
    use HasFactory;

    protected static $unguarded = true;

    protected $casts = [
        'configuration' => 'array'
    ];

    protected static function booted()
    {
        if (auth()->hasUser()) {
            if ((auth()->user()?->isSuperAdmin() ?? false) !== true) {
                static::addGlobalScope(new \App\Scopes\Installation());
            }
        }
    }
}
