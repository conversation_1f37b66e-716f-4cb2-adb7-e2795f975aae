<?php

namespace App\Models;

use App\Enums\ProductDemandsStatuses;
use App\Scopes\Installation;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

/**
 * @mixin Builder
 * @property integer id
 * @property integer user_id
 * @property string context
 * @property string context_identifier
 * @property integer product_id
 * @property integer amount
 * @property \DateTime demand_at
 * @property ProductDemandsStatuses status
 * @property integer installation
 * @property \DateTime created_at
 * @property \DateTime updated_at
 */
class ProductDemand extends Model
{
    use HasFactory;

    protected $casts = [
        'demand_at' => "date:Y-m-d",
        'status' => ProductDemandsStatuses::class
    ];

    protected static $unguarded = true;

    protected static function booted()
    {
        if (false === auth()->user()?->isSuperAdmin()) {
            static::addGlobalScope(new Installation());
        }
    }


    public function product(): BelongsTo
    {
        return $this->belongsTo(Products::class);
    }


    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }


    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'installation');
    }

    public function scopeActive(Builder $builder): Builder
    {
        return $builder->where('status', ProductDemandsStatuses::ACTIVE->value);
    }

    public function scopeCompleted(Builder $builder): Builder
    {
        return $builder->where('status', ProductDemandsStatuses::COMPLETED->value);
    }

    public function scopeInProgress(Builder $builder): Builder
    {
        return $builder->where('status', ProductDemandsStatuses::IN_PROGRESS->value);
    }

    public function scopeOverdue(Builder $builder): Builder
    {
        return $builder->where('demand_at', '<', now()->format('Y-m-d'));
    }
}
