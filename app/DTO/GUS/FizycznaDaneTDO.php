<?php

namespace App\DTO\GUS;

class FizycznaDaneTDO
{
    public readonly string $fizRegon9;
    public readonly string $fizNazwa;
    public readonly string $fizNazwaSkrocona;
    public readonly string $fizDataPowstania;
    public readonly string $fizDataRozpoczeciaDzialalnosci;
    public readonly string $fizDataWpisuDzialalnosciDoRegon;
    public readonly string $fizDataZawieszeniaDzialalnosci;
    public readonly string $fizDataWznowieniaDzialalnosci;
    public readonly string $fizDataZaistnieniaZmianyDzialalnosci;
    public readonly string $fizDataZakonczeniaDzialalnosci;
    public readonly string $fizDataSkresleniaDzialalnosciZRegon;
    public readonly string $fizAdSiedzKrajSymbol;
    public readonly string $fizAdSiedzWojewodztwoSymbol;
    public readonly string $fizAdSiedzPowiatSymbol;
    public readonly string $fizAdSiedzGminaSymbol;
    public readonly string $fizAdSiedzKodPocztowy;
    public readonly string $fizAdSiedzMiejscowoscPocztySymbol;
    public readonly string $fizAdSiedzMiejscowoscSymbol;
    public readonly string $fizAdSiedzUlicaSymbol;
    public readonly string $fizAdSiedzNumerNieruchomosci;
    public readonly string $fizAdSiedzNumerLokalu;
    public readonly string $fizAdSiedzNietypoweMiejsceLokalizacji;
    public readonly string $fizNumerTelefonu;
    public readonly string $fizNumerWewnetrznyTelefonu;
    public readonly string $fizNumerFaksu;
    public readonly string $fizAdresEmail;
    public readonly string $fizAdresStronyinternetowej;
    public readonly string $fizAdSiedzKrajNazwa;
    public readonly string $fizAdSiedzWojewodztwoNazwa;
    public readonly string $fizAdSiedzPowiatNazwa;
    public readonly string $fizAdSiedzGminaNazwa;
    public readonly string $fizAdSiedzMiejscowoscNazwa;
    public readonly string $fizAdSiedzMiejscowoscPocztyNazwa;
    public readonly string $fizAdSiedzUlicaNazwa;
    public readonly string $fizCDataWpisuDoRejestruEwidencji;
    public readonly string $fizCDataSkresleniaZRejestruEwidencji;
    public readonly string $fizCNumerWRejestrzeEwidencji;
    public readonly string $fizCOrganRejestrowySymbol;
    public readonly string $fizCOrganRejestrowyNazwa;
    public readonly string $fizCRodzajRejestruSymbol;
    public readonly string $fizCRodzajRejestruNazwa;
    public readonly string $fizCNiePodjetoDzialalnosci;


    public function __construct(array $data)
    {
        foreach ($data as $key => $value) {
            $property = lcfirst(str_replace('_', '', ucwords($key, '_')));
            if (property_exists($this, $property)) {
                $this->$property = $value;
            }
        }
    }
}
