<?php
namespace App\DTO\GUS;

/**
 * @var string $prawRegon9
 * @var string $prawNip
 * @var string $prawStatusNip
 * @var string $prawNazwa
 * @var string $prawNazwaSkrocona
 * @var string $prawNumerWRejestrzeEwidencji
 * @var string $prawDataWpisuDoRejestruEwidencji
 * @var string $prawDataPowstania
 * @var string $prawDataRozpoczeciaDzialalnosci
 * @var string $prawDataWpisuDoRegon
 * @var string $prawDataZawieszeniaDzialalnosci
 * @var string $prawDataWznowieniaDzialalnosci
 * @var string $prawDataZaistnieniaZmiany
 * @var string $prawDataZakonczeniaDzialalnosci
 * @var string $prawDataSkresleniaZRegon
 * @var string $prawDataOrzeczeniaOUpadlosci
 * @var string $prawDataZakonczeniaPostepowaniaUpadlosciowego
 * @var string $prawAdSiedzKrajSymbol
 * @var string $prawAdSiedzWojewodztwoSymbol
 * @var string $prawAdSiedzPowiatSymbol
 * @var string $prawAdSiedzGminaSymbol
 * @var string $prawAdSiedzKodPocztowy
 * @var string $prawAdSiedzMiejscowoscPocztySymbol
 * @var string $prawAdSiedzMiejscowoscSymbol
 * @var string $prawAdSiedzUlicaSymbol
 * @var string $prawAdSiedzNumerNieruchomosci
 * @var string $prawAdSiedzNumerLokalu
 * @var string $prawAdSiedzNietypoweMiejsceLokalizacji
 * @var string $prawNumerTelefonu
 * @var string $prawNumerWewnetrznyTelefonu
 * @var string $prawNumerFaksu
 * @var string $prawAdresEmail
 * @var string $prawAdresStronyinternetowej
 * @var string $prawAdSiedzKrajNazwa
 * @var string $prawAdSiedzWojewodztwoNazwa
 * @var string $prawAdSiedzPowiatNazwa
 * @var string $prawAdSiedzGminaNazwa
 * @var string $prawAdSiedzMiejscowoscNazwa
 * @var string $prawAdSiedzMiejscowoscPocztyNazwa
 * @var string $prawAdSiedzUlicaNazwa
 * @var string $prawPodstawowaFormaPrawnaSymbol
 * @var string $prawSzczegolnaFormaPrawnaSymbol
 * @var string $prawFormaFinansowaniaSymbol
 * @var string $prawFormaWlasnosciSymbol
 * @var string $prawOrganZalozycielskiSymbol
 * @var string $prawOrganRejestrowySymbol
 * @var string $prawRodzajRejestruEwidencjiSymbol
 * @var string $prawPodstawowaFormaPrawnaNazwa
 * @var string $prawSzczegolnaFormaPrawnaNazwa
 * @var string $prawFormaFinansowaniaNazwa
 * @var string $prawFormaWlasnosciNazwa
 * @var string $prawOrganZalozycielskiNazwa
 * @var string $prawOrganRejestrowyNazwa
 * @var string $prawRodzajRejestruEwidencjiNazwa
 * @var string $prawLiczbaJednLokalnych
 */

class PrawneDaneDTO
{
    public readonly string $prawRegon9;
    public readonly string $prawNip;
    public readonly string $prawStatusNip;
    public readonly string $prawNazwa;
    public readonly string $prawNazwaSkrocona;
    public readonly string $prawNumerWRejestrzeEwidencji;
    public readonly string $prawDataWpisuDoRejestruEwidencji;
    public readonly string $prawDataPowstania;
    public readonly string $prawDataRozpoczeciaDzialalnosci;
    public readonly string $prawDataWpisuDoRegon;
    public readonly string $prawDataZawieszeniaDzialalnosci;
    public readonly string $prawDataWznowieniaDzialalnosci;
    public readonly string $prawDataZaistnieniaZmiany;
    public readonly string $prawDataZakonczeniaDzialalnosci;
    public readonly string $prawDataSkresleniaZRegon;
    public readonly string $prawDataOrzeczeniaOUpadlosci;
    public readonly string $prawDataZakonczeniaPostepowaniaUpadlosciowego;
    public readonly string $prawAdSiedzKrajSymbol;
    public readonly string $prawAdSiedzWojewodztwoSymbol;
    public readonly string $prawAdSiedzPowiatSymbol;
    public readonly string $prawAdSiedzGminaSymbol;
    public readonly string $prawAdSiedzKodPocztowy;
    public readonly string $prawAdSiedzMiejscowoscPocztySymbol;
    public readonly string $prawAdSiedzMiejscowoscSymbol;
    public readonly string $prawAdSiedzUlicaSymbol;
    public readonly string $prawAdSiedzNumerNieruchomosci;
    public readonly string $prawAdSiedzNumerLokalu;
    public readonly string $prawAdSiedzNietypoweMiejsceLokalizacji;
    public readonly string $prawNumerTelefonu;
    public readonly string $prawNumerWewnetrznyTelefonu;
    public readonly string $prawNumerFaksu;
    public readonly string $prawAdresEmail;
    public readonly string $prawAdresStronyinternetowej;
    public readonly string $prawAdSiedzKrajNazwa;
    public readonly string $prawAdSiedzWojewodztwoNazwa;
    public readonly string $prawAdSiedzPowiatNazwa;
    public readonly string $prawAdSiedzGminaNazwa;
    public readonly string $prawAdSiedzMiejscowoscNazwa;
    public readonly string $prawAdSiedzMiejscowoscPocztyNazwa;
    public readonly string $prawAdSiedzUlicaNazwa;
    public readonly string $prawPodstawowaFormaPrawnaSymbol;
    public readonly string $prawSzczegolnaFormaPrawnaSymbol;
    public readonly string $prawFormaFinansowaniaSymbol;
    public readonly string $prawFormaWlasnosciSymbol;
    public readonly string $prawOrganZalozycielskiSymbol;
    public readonly string $prawOrganRejestrowySymbol;
    public readonly string $prawRodzajRejestruEwidencjiSymbol;
    public readonly string $prawPodstawowaFormaPrawnaNazwa;
    public readonly string $prawSzczegolnaFormaPrawnaNazwa;
    public readonly string $prawFormaFinansowaniaNazwa;
    public readonly string $prawFormaWlasnosciNazwa;
    public readonly string $prawOrganZalozycielskiNazwa;
    public readonly string $prawOrganRejestrowyNazwa;
    public readonly string $prawRodzajRejestruEwidencjiNazwa;
    public readonly string $prawLiczbaJednLokalnych;

    public function __construct(array $data)
    {
        foreach ($data as $key => $value) {
            $property = lcfirst(str_replace('_', '', ucwords($key, '_')));
            if (property_exists($this, $property)) {
                $this->$property = $value;
            }
        }
    }

    public function __call($name, $arguments)
    {
        if (strpos($name, 'get') === 0) {
            $property = lcfirst(substr($name, 3));
            if (property_exists($this, $property)) {
                return $this->$property;
            }
        }
        throw new \Exception("Method $name does not exist");
    }
}
