<?php

namespace App\Filament\Actions;

use Filament\Actions\Action;
use Filament\Actions\StaticAction;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Get;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use Livewire\Component;

class PrintAction extends Action
{

    protected function setUp(): void
    {
        parent::setUp();
        $this->modalWidth(MaxWidth::Small);
        $this->after(function (Model $record) {
            session()->remove('print.' . $record->transaction_id);
        });
        $this->label(__('app.actions.printAction.label'));
        $this->button();
        $this->icon('heroicon-o-printer');

        $this->modalSubmitAction(
            function (StaticAction $action, Model $record, Component $livewire) {
                return $action->url(
                    route('printPDF', ['doc' => $record->transaction_id]),
                    true
                )
                    ->extraAttributes([
                        'x-on:click' => new HtmlString(
                            '$dispatch(\'close-modal\', {\'id\' : \'' . $livewire->getId() . '-action\'})'
                        )
                    ]);
            }
        );

        $this->form([
            Checkbox::make('print_note')
                ->label(__('app.actions.printAction.print_note'))
                ->live()
                ->afterStateUpdated(
                    fn($state, Model $record, Get $get) => session()->put(
                        'print.' . $record->transaction_id,
                        ['note' => $get('note'), "print_note" => $state]
                    )
                ),
            Textarea::make('note')
                ->label(__('app.actions.printAction.note'))
                ->live(onBlur: true)
                ->afterStateUpdated(
                    fn($state, Model $record, Get $get) => session()->put(
                        'print.' . $record->transaction_id,
                        ['note' => $state, "print_note" => $get("print_note")]
                    )
                ),
        ]);
        $this->fillForm(fn(Model $record) => [
            'note' => $record->notes,
        ]);
    }
}
