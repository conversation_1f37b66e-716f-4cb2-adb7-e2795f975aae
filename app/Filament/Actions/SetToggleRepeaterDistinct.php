<?php

namespace App\Filament\Actions;

use Filament\Forms\Set;
use Illuminate\Support\Arr;
use Filament\Forms\Components;

class SetToggleRepeaterDistinct
{
    public function __invoke($state, Components\Toggle $toggle, Set $set, string $componentItemStatePath): void
    {
        if (blank($state)) {
            return;
        }
        $repeater = $toggle->getParentRepeater();
        $repeaterStatePath = $repeater->getStatePath();
//        $componentItemStatePath = 'is_products_list';
        $repeaterItemKey = (string)str($toggle->getStatePath())
            ->after("{$repeaterStatePath}.")
            ->beforeLast(".{$componentItemStatePath}");

        $repeaterSiblingState = Arr::except($repeater->getState(), [$repeaterItemKey]);

        if (empty($repeaterSiblingState)) {
            return;
        }

        collect($repeaterSiblingState)
            ->map(fn(array $itemState): mixed => data_get($itemState, $componentItemStatePath))
            ->filter(function (mixed $siblingItemComponentState) use ($state): bool {
                if ($siblingItemComponentState === false) {
                    return false;
                }

                if (blank($siblingItemComponentState)) {
                    return false;
                }

                return $siblingItemComponentState === $state;
            })
            ->each(fn(mixed $siblingItemComponentState, string $itemKey) => $set(
                path: "{$repeaterStatePath}.{$itemKey}.{$componentItemStatePath}",
                state: match ($siblingItemComponentState) {
                    true => false,
                    default => null,
                },
                isAbsolute: true,
            ));
    }
}
