<?php

namespace App\Filament\App\Pages;

use App\Models\Products;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Pages\Page;
use Filament\Support\Enums\FontWeight;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use Livewire\Component;

class ProductsList extends Page implements HasTable
{
    use InteractsWithTable;

    public $record;

    public string $info = '';


    protected static ?string $navigationIcon = 'heroicon-o-gift';

    protected static string $view = 'filament.app.pages.products-list';

    /**
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return __('app.products._.navigation_label');
    }

    public static function getNavigationLabel(): string
    {
        return __('app.products._.navigation_label');
    }

    public static function shouldRegisterNavigation(): bool
    {
        return !auth()->user()->isTenantAdmin();
    }

    public function getTableRecordKey(Model $record): string
    {
        return $record->hash;
    }


    public function formatInfolistEntry($label, $state): HtmlString
    {
        return new HtmlString("<span class='font-medium'>$label:</span> $state");
    }

    public function infoInfolist()
    {
        return [
            Grid::make(2)
                ->schema([
                    TextEntry::make('name')
                        ->label('')
                        ->formatStateUsing(fn($state) => $this->formatInfolistEntry(__('app._.name'), $state)),
                    TextEntry::make('gtin')
                        ->label('')
                        ->formatStateUsing(fn($state) => $this->formatInfolistEntry(__('app._.gtin'), $state)),
                    TextEntry::make('description')
                        ->columnSpanFull()
                        ->label(__('app.products._.description'))
                        ->hidden(fn($state) => empty($state)),
                    TextEntry::make('grace_period')
                        ->columnSpanFull()
                        ->formatStateUsing(fn($state) => new HtmlString(nl2br($state)))
                        ->label(__('app.products._.grace_period'))
                        ->hidden(fn($state) => empty($state)),
                    TextEntry::make('manufacturer.name')
                        ->label(__('app.products._.manufacturer'))
                        ->hidden(fn($state) => empty($state)),
                ]),
            TextEntry::make('ext_link')
                ->hidden(fn($state) => empty($state))
                ->label(__('app.products._.ext_link'))
                ->url(function ($state) {
                    if (empty($state)) {
                        return '';
                    }
                    return $state;
                }, true),
            Grid::make(2)
                ->schema([
                    TextEntry::make('basic_unit')
                        ->label('')
                        ->hidden(fn($state) => empty($state))
                        ->formatStateUsing(
                            fn($state) => $this->formatInfolistEntry(__('app.products._.basic_unit'), $state)
                        ),
                    TextEntry::make('price_per_unit')
                        ->label('')
                        ->hidden(fn($state) => empty($state))
                        ->formatStateUsing(
                            fn($state) => $this->formatInfolistEntry(__('app.products._.price_per_unit'), $state)
                        ),
                    TextEntry::make('volume_ml')
                        ->label('')
                        ->hidden(fn($state) => empty($state))
                        ->formatStateUsing(
                            fn($state) => $this->formatInfolistEntry(__('app.products._.volume_ml'), $state)
                        ),
                    TextEntry::make('weight_gr')
                        ->label('')
                        ->hidden(fn($state) => empty($state))
                        ->formatStateUsing(
                            fn($state) => $this->formatInfolistEntry(__('app.products._.weight_gr'), $state)
                        ),
                    TextEntry::make('minimum_stock')
                        ->label('')
                        ->hidden(fn($state) => empty($state))
                        ->formatStateUsing(
                            fn($state) => $this->formatInfolistEntry(__('app.products._.minimum_stock'), $state)
                        ),
                    TextEntry::make('minimum_exp_date')
                        ->label('')
                        ->hidden(fn($state) => empty($state))
                        ->formatStateUsing(
                            fn($state) => $this->formatInfolistEntry(__('app.products._.minimum_exp_date'), $state)
                        ),
                ])
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(fn(Builder $query) => Products::query())
            ->columns([
                Tables\Columns\Layout\Stack::make([
                    Tables\Columns\Layout\Split::make([
                        TextColumn::make('name')
                            ->label(__('app._.name'))
                            ->weight(FontWeight::Bold)
                            ->sortable()
                            ->searchable(),
                        TextColumn::make('manufacturer.name')
                            ->label(__('app.products._.manufacturer'))
                            ->numeric()
                            ->sortable(),
                    ]),
                    Tables\Columns\Layout\Split::make([
                        TextColumn::make('gtin')
                            ->label(__('app.products._.gtin'))
                            ->searchable(),
                    ])
                ])
            ])
            ->recordAction("view")
            ->actionsAlignment('justify-end')
            ->defaultSort('name')
            ->filters([
                Tables\Filters\SelectFilter::make('manufacturer')
                    ->relationship('manufacturer', 'name')
                    ->label('Producenci')
            ])
            ->actions(
                $this->getViewAction()
            );
    }

    public function myInfo($id)
    {
        $il = new Infolist();
        $il->schema($this->infoInfolist())->record(Products::whereHash($id)->first());
        $this->info = $il->toHtml();
    }

    public function view($data)
    {
        $this->myInfo($data);
        $this->dispatch('open-modal', id: 'view-product');
    }

    public function getViewAction($return = 'array')
    {
        return [];
//            $action = Tables\Actions\ViewAction::make('infomodal')
//                ->infolist(fn() => $this->infoInfolist())
//                ->modalHeading('Detale produktu')
//                ->label("Detale");
//
//            return match ($return) {
//                'array' => [$action],
//                default => $action
//            };
    }
}
