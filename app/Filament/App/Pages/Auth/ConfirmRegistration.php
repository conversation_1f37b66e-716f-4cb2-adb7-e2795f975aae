<?php

namespace App\Filament\App\Pages\Auth;

use App\Events\Registration\RegistrationConfirmed;
use App\Filament\App\Resources\PartnerResource;
use App\Mail\RegistrationConfirmation;
use App\Models\Registration;
use App\Models\Tenant;
use App\Models\User;
use App\Repositories\GUSRepository;
use Filament\Facades\Filament;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Pages\Concerns\HasRoutes;
use Filament\Pages\SimplePage;
use Filament\Panel;
use Filament\Resources\Pages\PageRegistration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Route as RouteFacade;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;

class ConfirmRegistration extends SimplePage
{
    use InteractsWithForms, HasRoutes;

    protected static string $view = 'filament.app.pages.auth.confirm-registration';

    public ?string $email = null;

    protected ?string $heading = 'Potwierdź rejestrację';

    public array $data = [];

    public static function getUrl($data): string
    {
        return (string) route('filament.app.auth.register.confirm', $data);
    }


    public function mount(string $hash)
    {

        $registration = Registration::where('registration_hash', $hash)->first();

        if (! $registration) {
            return redirect(Filament::getRegistrationUrl());
        }

        if ($registration->isFinished()) {
            return redirect(Filament::getLoginUrl());
        }

        $this->email = $registration->email;

        $this->form->fill();
    }

//    public static function route(string $path): PageRegistration
//    {
//        return new PageRegistration(
//            page: static::class,
//            route: fn (Panel $panel): \Illuminate\Routing\Route => RouteFacade::get($path, static::class)
//                ->middleware(static::getRouteMiddleware($panel))
//                ->withoutMiddleware(static::getWithoutRouteMiddleware($panel)),
//        );
//    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('confirmation_code')
                    ->label('Kod potwierdzający rejestrację')
                    ->required()
                    ->rules([
                        fn($livewire): \Closure =>
                        static function (string $attribute, $value, \Closure $fail) use ($livewire) {
                            $registration = Registration::where('email', $livewire->email)->first();
                            if (! $registration) {
                                $fail('Brak rejestracji o podanym adresie email. Adres email nie jest znany.');
                            }

                            if (strlen($value) !== 6) {
                                $fail('Kod potwierdzający rejestrację musi mieć 6 znaków.');
                            }

                            if ($value !== $registration->confirmation_code) {
                                $fail('Kod potwierdzający jest nieznany.');
                            }
                            return true;
                        }
                    ])
                    ->helperText('Wprowadź 6-cio znakowy kod potwierdzjący rejestrację'),
            ])
            ->statePath('data');
    }

    public function confirm(): void
    {
        $data = $this->form->getState();

        $registration = Registration::where('email', $this->email)->first();

        if (! $registration) {
            abort(404);
        }

        if ($registration->confirmation_code !== $data['confirmation_code']) {
            abort(404);
        }
        // Mark registration as confirmed
        $registration->update([
            'confirmed_at' => now(),
        ]);

        $company = GUSRepository::findByNip($registration->vat_id);
        if (filled($company)) {
            $data = PartnerResource::mapGUSResponseToFormData($company);
            $registration->data = ['company' => $data];
            $registration->save();
        }

        Session::put('registration_code', $registration->confirmation_code);

        RegistrationConfirmed::dispatch($registration);
        // Redirect to company data page
        $this->redirect(ConfirmRegistrationData::getUrl(['hash' => $registration->registration_hash]));
    }
}
