<?php

namespace App\Filament\App\Pages\Auth;

use App\Enums\AccountingTypesPL;
use App\Enums\PartnerBusinessTypes;
use App\Enums\PartnerVATTypes;
use App\Enums\Roles;
use App\Enums\SystemModules;
use App\Enums\TaxResidencyCountries;
use App\Enums\TaxTypePL;
use App\Events\Registration\RegistrationFinished;
use App\Filament\App\Resources\PartnerResource;
use App\Models\Registration;
use App\Models\Tenant;
use App\Models\User;
use App\Repositories\CurrenciesRepository;
use App\Repositories\DocumentSeriesRepository;
use App\Repositories\GUSRepository;
use App\Repositories\TenantRepository;
use Filament\Facades\Filament;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Pages\Concerns\HasRoutes;
use Filament\Pages\Page;
use Filament\Pages\SimplePage;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\HtmlString;
use Illuminate\Validation\ValidationException;

class ConfirmRegistrationData extends SimplePage
{
    use InteractsWithForms, HasRoutes;

    protected static string $view = 'filament.app.pages.auth.confirm-user-data';

    public ?string $email = null;

    protected ?string $heading = 'Potwierdź swoje dane';


    public array $data = [];

    public function mount(string $hash)
    {

        $registration = Registration::where('registration_hash', $hash)->first();

        if (!$registration) {
            return redirect(Filament::getRegistrationUrl());
        }

        if (!$registration->isConfirmed()) {
            $this->redirect(ConfirmRegistration::getUrl(['hash' => $registration->registration_hash]));
        }

        if ($registration->isFinished()) {
            $this->redirect(Filament::getLoginUrl());
        }

        if (Session::get('registration_code') !== $registration->confirmation_code) {
            $this->redirect(ConfirmRegistration::getUrl(['hash' => $registration->registration_hash]));
        }

        $this->email = $registration->email;

        $this->form->fill($registration->data);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Wizard::make([
                    Wizard\Step::make('Dane Osobowe')
                        ->schema([
                            TextInput::make('user.name')
                                ->required()
                                ->validationMessages([
                                    'required' => 'Imię jest wymagane.',
                                    'max' => 'Imię może mieć najwyżej :max znaków.',
                                ])
                                ->label(__('app.users.profile.name'))
                                ->maxLength(100),
                            TextInput::make('user.surname')
                                ->required()
                                ->validationMessages([
                                    'required' => 'Nazwisko jest wymagane.',
                                    'max' => 'Nazwisko może mieć najwyżej :max znaków.',
                                ])
                                ->label(__('app.users.profile.surname'))
                                ->maxLength(255),
                            TextInput::make('user.password')
                                ->required()
                                ->label(__('app.users.password'))
                                ->password()
                                ->validationMessages([
                                    'required' => 'Haslo jest wymagane.',
                                    'max' => 'Hasło może mieć najwyżej :max znaków.',
                                    'min' => 'Hasło musi mieć co najmniej :min znaków.',
                                ])
                                ->minLength(10)
                                ->maxLength(255),
                            TextInput::make('user.password_confirmation')
                                ->required()
                                ->label(__('app.users.password_confirmation'))
                                ->password()
                                ->validationMessages([
                                    'required' => 'Potwierdzenie hasla jest wymagane.',
                                    'max' => 'Hasło może mieć najwyżej :max znaków.',
                                    'same' => 'Potwierdzenie hasla musi być takie samo jak hasło.',
                                ])
                                ->same('user.password')
                                ->maxLength(255),
                            TextInput::make('user.adress')
                                ->label(__('app.users.profile.address'))
                                ->validationMessages([
                                    'max' => 'Adres może mieć najwyżej :max znaków.',
                                ])
                                ->maxLength(255),
                            TextInput::make('user.number')
                                ->label(__('app.users.profile.number'))
                                ->maxLength(255)
                                ->validationMessages([
                                    'max' => 'Adres może mieć najwyżej :max znaków.',
                                ])
                                ->numeric(),
                        ]),
                    Wizard\Step::make('Dane Firmy')
                        ->columns(2)
                        ->schema([
                            TextArea::make('company.name')
                                ->label('Nazwa')
                                ->validationMessages([
                                    'required' => 'Nazwa firmy jest wymagana.',
                                    'max' => 'Nazwa firmy może mieć najwyżej :max znaków.',
                                ])
                                ->required()
                                ->columnSpanFull()
                                ->maxLength(120),
                            TextInput::make('company.postcode')
                                ->required()
                                ->validationMessages([
                                    'required' => 'Kod pocztowy jest wymagana.',
                                    'max' => 'Kod pocztowy może mieć najwyżej :max znaków.',
                                ])
                                ->label('Kod pocztowy')
                                ->maxLength(20),
                            TextInput::make('company.city')
                                ->required()
                                ->label('Miasto')
                                ->validationMessages([
                                    'required' => 'Nazwa miasta jest wymagana.',
                                    'max' => 'Nazwa miasta może mieć najwyżej :max znaków.',
                                ])
                                ->maxLength(100),
                            TextInput::make('company.phone')
                                ->label('Telefon')
                                ->validationMessages([
                                    'max_digits' => 'Numer telefonu może mieć najwyżej :max znaków.',
                                    'min_digits' => 'Numer telefonu misu mieć co najmniej :min znaków.',
                                ])
                                ->numeric()
                                ->minLength(8)
                                ->maxLength(100),
                            TextInput::make('company.email')
                                ->label('Email')
                                ->email()
                                ->maxLength(60),
                            TextInput::make('company.contact_name')
                                ->label('Osoba konaktowa'),
                            TextInput::make('company.website')
                                ->url()
                                ->label('Strona www'),
                            Textarea::make('company.address')
                                ->label('Adres')
                                ->validationMessages([
                                    'required' => 'Adres firmy jest wymagany.',
                                    'max' => 'Adres firmy może mieć najwyżej :max znaków.',
                                ])
                                ->required()
                                ->maxLength(500)
                                ->columnSpanFull(),
                        ]),
                    Wizard\Step::make('Dane księgowe')
                        ->columns(2)
                        ->schema([
                            Select::make('company.tax_residency_country')
                                ->required()
                                ->validationMessages([
                                    'required' => 'Kraj rezydencji podatkowej jest wymagany.',
                                ])
                                ->label(__('app.partners.create.tax_residency_country'))
                                ->options(
                                    TaxResidencyCountries::toArrayWithLabels()
                                )
                                ->formatStateUsing(fn($state) => $state ?? TaxResidencyCountries::PL->name),
                            Select::make('company.business_type')
                                ->required()
                                ->validationMessages([
                                    'required' => 'Rodzaj działalności jest wymagany.',
                                ])
                                ->label(__('app.partners.create.business_type'))
                                ->options(PartnerBusinessTypes::toArrayWithLabels())
                                ->default(PartnerBusinessTypes::INDIVIDUAL->value),
                            Select::make('company.vat_type')
                                ->required()
                                ->validationMessages([
                                    'required' => 'Status podatnika VAT jest wymagany.',
                                ])
                                ->label(__('app.partners.create.vat_type'))
                                ->options(PartnerVATTypes::toArrayWithLabels())
                                ->default(PartnerVATTypes::LOCAL->value),
                            TextInput::make('company.vat_id')
                                ->required(
                                    fn(Get $get) => (int) $get('company.vat_type') !== PartnerVATTypes::NOTVAT->value
                                )
                                ->validationMessages([
                                    'required' => 'W przypadku podatnika VAT numer NIP jest niezbędny.',
                                ])
                                ->label('NIP'),
                            Select::make('company.tax_type')
                                ->label(__('app.partners.create.tax_type'))
                                ->options(TaxTypePL::toArrayWithLabels())
                                ->default(TaxTypePL::LINEAR->value),
                            Select::make('company.accounting_type')
                                ->label(__('app.partners.create.accounting_type'))
                                ->options(AccountingTypesPL::toArrayWithLabels())
                                ->default(AccountingTypesPL::FULL->value),
                            TextInput::make('company.meta.accounting.regon')
                                ->label('Regon'),
                            TextInput::make('company.meta.accounting.bdo')
                                ->maxLength(10)
                                ->label('Kod BDO'),
                        ]),
                    Wizard\Step::make('Dane bankowe')
                        ->schema([
                            Repeater::make('company.meta.bank_accounts')
                                ->label('Konta bankowe')
                                ->columns(2)
                                ->addActionLabel('Dodaj konto bankowe')
                                ->schema([
                                    TextInput::make('account_name')
                                        ->label('Nazwa rachunku wyświetlana w systemie'),
                                    TextInput::make('bank_name')
                                        ->label('Nazwa banku'),
                                    TextInput::make('bank_account')
                                        ->mask('99 9999 9999 9999 9999 9999 9999')
                                        ->label('Numer konta'),
                                    TextInput::make('bank_swift')
                                        ->label('Kod SWIFT/BIC'),
                                    TextInput::make('bank_iban')
                                        ->label('Kod IBAN'),
                                    Select::make('bank_currency')
                                        ->label('Waluta')
                                        ->options(
                                            collect(CurrenciesRepository::getSystemCurrencies())
                                                ->mapWithKeys(
                                                    static fn($currency) => [
                                                        $currency->alphabeticCode => $currency->getLabel()
                                                    ]
                                                )
                                                ->toArray()
                                        )
                                        ->required()
                                        ->default('PLN'),
                                ]),
                        ]),
                ])
                ->submitAction(
                    new HtmlString(Blade::render(<<<BLADE
    <x-filament::button
        type="submit"
        size="sm"
    >
        Potwierdź rejestrację
    </x-filament::button>
BLADE))
                )
            ])
            ->statePath('data');
    }

    public function confirm(): void
    {
        $data = $this->form->getState();

        $registration = Registration::where('email', $this->email)->first();

        if (!$registration) {
            abort(404);
        }

        if ($registration->isFinished()) {
            $this->redirect(Filament::getLoginUrl());
        }

        // Mark registration as confirmed
        $registration->update([
            'data' => Arr::except($data, ['user.password','user.password_confirmation'])
        ]);

        $dataCollection = collect($data);
        if ($dataCollection->get('user')['password'] !== $dataCollection->get('user')['password_confirmation']) {
            abort(404);
        }

        // Create tenant and user
        DB::transaction(static function () use ($registration, $dataCollection) {
            // Create tenant
            $companyData = $dataCollection->get('company');
            $userData = $dataCollection->get('user');

            $tenantConfig = [
                'modules' => [
                    SystemModules::INVOICES,
                ]
            ];
            $tenant = Tenant::create([
                ...Arr::except($companyData, 'meta'),
                'hash' => bin2hex(random_bytes(16)),
                'config' => $tenantConfig,
                'is_active' => true,
            ]);

            $tenant->meta()->create(
                Arr::only($companyData, 'meta')
            );

            // Create user
            $user = User::create([
                'name' => $registration->email,
                'email' => $registration->email,
                'password' => Hash::make($userData['password']), // Random password, user will need to reset
                'active' => true,
            ]);

            $user->assignRole(Roles::TENANT_ADMIN);

            $user->profile()->create([
                ...Arr::except($userData, ['password', 'password_confirmation']),
                'lang' => 'pl'
            ]);

            // Associate user with tenant
            $user->tenant()->attach($tenant->id);

            DocumentSeriesRepository::seedDefaultSeries($tenant, 'trade');

            Auth::login($user);

            $registration->update([
                'finished_at' => now(),
            ]);
            RegistrationFinished::dispatch($registration, $user);
        });

        // Redirect to company data page
        Session::forget('registration_code');
        $this->redirect(Filament::getLoginUrl());
    }

    public static function getUrl(
        array $parameters = [],
        bool $isAbsolute = true,
        ?string $panel = null,
        null|\Illuminate\Database\Eloquent\Model $tenant = null
    ): string {
        return (string)route('filament.app.auth.register.confirm-data', $parameters);
    }

    public function getMaxWidth(): MaxWidth|string|null
    {
        return MaxWidth::FourExtraLarge;
    }
}
