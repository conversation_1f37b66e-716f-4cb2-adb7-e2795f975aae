<?php

namespace App\Filament\App\Pages;

use App\Enums\SystemModules;
use App\Filament\Filters\ExpDateWarningFilter;
use App\Filament\Filters\LowStockWarningFilter;
use App\Filament\Filters\WHSelectFilter;
use App\Models\WarehouseItem;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Pages\Page;
use Filament\Support\Enums\FontWeight;
use Filament\Support\Enums\IconPosition;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use pxlrbt\FilamentExcel\Actions\Tables\ExportAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;

class AdminStockList extends Page implements HasTable
{

    use InteractsWithTable;


    protected static ?string $navigationIcon = 'heroicon-o-inbox-stack';

    protected static string $view = 'filament.app.pages.admin-stock-list';

    protected static ?int $navigationSort = -3;

    protected static ?string $slug = 'warehouses/products';


    public $converted = '';

    public $info = '';

    public function __construct()
    {
        abort_if(!auth()->user()->isTenantAdmin(), 403);
    }

    public static function canAccess(): bool
    {
        return parent::canAccess() && tenant()->hasModule(SystemModules::WAREHOUSE);
    }

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isTenantAdmin() && tenant()->hasModule(SystemModules::WAREHOUSE);
    }


    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return __('app.mywarehouse.page.heading');
    }

    public static function getNavigationLabel(): string
    {
        return __('app.mywarehouse.page.navigationLabel');
    }

    public function getTableRecordKey(Model $record): string
    {
        return $record->hash;
    }


    public function table(Table $table): Table
    {

        return $table
            ->query(function (Builder $builder) {
                $qry = WarehouseItem::query()->with(['product', 'warehouse'])
                    ->groupBy('warehouse_id')
                    ->groupBy('transaction_id')
                    ->groupBy('series_no')
                    ->groupBy('price')
                    ->selectRaw('*, SUM(amount) as total');
                return $qry;
            })
            ->columns([
                TextColumn::make('product.name')
                    ->label(__('app.mywarehouse.list.product'))
                    ->searchable(),
                TextColumn::make('series_no')
                    ->searchable()
                    ->alignCenter()
                    ->label(__('app.mywarehouse.list.series_no')),
                TextColumn::make('exp_date')
                    ->label(__('app.mywarehouse.list.exp_date'))
                    ->sortable()
                    ->color(fn($state, Model $record) => match (true) {
                        empty($record->exp_date) => null,
                        empty($record->minimum_exp_date) => $record->exp_date->isPast() ? 'danger' : null,
                        default => match (true) {
                            $record->exp_date->isPast() => 'danger',
                            abs(now()->diffInDays($record->exp_date)) <= (int)$record->minimum_exp_date => 'warning',
                            default => null
                        }
                    })
                    ->weight(fn($state, Model $record) => match (true) {
                        empty($record->exp_date) => null,
                        empty($record->minimum_exp_date) => $record->exp_date->isPast() ? FontWeight::Bold : null,
                        default => match (now()->diffInDays($record->exp_date) <= (int)$record->minimum_exp_date) {
                            true => FontWeight::Bold,
                            default => null
                        }
                    })
                    ->date('Y-m-d')
                    ->alignCenter(),
                TextColumn::make('minimum_exp_date')
                    ->alignCenter()
                    ->visibleFrom('md')
                    ->default('--')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('app.products._.minimum_exp_date_short')),
                TextColumn::make('total')
                    ->label(__('app.mywarehouse.list.total'))
                    ->color(fn(Model $record) => match ((int)$record->total <=> (int)$record->minimum_stock ?? 0) {
                        -1, 0 => 'danger',
                        1 => null
                    })
                    ->weight(fn(Model $record) => match ((int)$record->total <=> (int)$record->minimum_stock ?? 0) {
                        -1, 0 => FontWeight::Bold,
                        1 => null
                    })
                    ->width('5%')
                    ->grow(false)
                    ->alignRight(),
                TextColumn::make('minimum_stock')
                    ->alignCenter()
                    ->visibleFrom('md')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('app.products._.minimum_stock')),
                TextColumn::make('gtin')
                    ->label(__('app._.gtin'))
                    ->visibleFrom('md')
                    ->searchable()
                    ->alignRight(),
                TextColumn::make('price')
                    ->visibleFrom('md')
                    ->alignRight()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('app.mywarehouse.list.price')),
                TextColumn::make('warehouse.name')
                    ->alignCenter()
                    ->label(__('app.mywarehouse.list.warehouse')),
                IconColumn::make('reserved')
                    ->label(__('app.mywarehouse.list.reserved'))
                    ->visibleFrom('md')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                WHSelectFilter::make('warehouse_id')
                    ->options(fn() => WHSelectFilter::getOptionsForLoggedInEmployee())
                    ->label('Magazyn')
                    ->columnSpan(1),

                SelectFilter::make('series_no')
                    ->label('Numer serii')
                    ->options(
                        WarehouseItem::groupBy('series_no')
                            ->whereNot('series_no', '')
                            ->get()
                            ->pluck('series_no', 'series_no')
                    )
                    ->searchable()
                    ->indicateUsing(function ($state) {
                        return match (empty($state['value'])) {
                            true => null,
                            default => 'Seria: ' . $state['value']
                        };
                    })
                    ->modifyQueryUsing(function (Builder $query, $state) {
                        if (empty($state['value'])) {
                            return $query;
                        }
                        return $query->where('series_no', $state['value']);
                    }),

                ExpDateWarningFilter::make('exp_date')
                    ->label('Termin przydatności')
                    ->native(true),

                LowStockWarningFilter::make('low_state')
                    ->label('Niski stan')
                    ->native(true),
            ])
            ->filtersFormColumns(4)
            ->filtersLayout(FiltersLayout::AboveContent)
            ->defaultPaginationPageOption(25)
            ->recordAction('view')
            ->headerActions([
                ExportAction::make()
                    ->exports([
                        ExcelExport::make()
                            ->withFilename(function () {
                                return 'stan_magazynu_' . now('Europe/Warsaw')->format('Y-m-d_H-i');
                            })
                            ->fromTable()
                    ])
            ]);
    }

    public function infoInfolist()
    {
        return [
            Grid::make(1)
                ->schema([
                    TextEntry::make('transaction.warehouse_doc.transaction_id')
                        ->label('')
                        ->hidden(fn($state) => empty($state))
                        ->formatStateUsing(fn($state) => $this->formatInfolistEntry('Dokument', $state)),
                    TextEntry::make('transaction_id')
                        ->label('')
                        ->hidden(fn($state) => empty($state))
                        ->tooltip('Historia transakcji')
                        ->url(fn(Model $record) => ProductHistory::buildHistoryUrl($record, 'transaction'), true)
                        ->icon(function (Model $record) {
                            return match (ProductHistory::buildFilterQuery($record, 'transaction')) {
                                null => null,
                                default => 'heroicon-o-document-text'
                            };
                        })
                        ->iconPosition(IconPosition::After)
                        ->formatStateUsing(fn($state) => $this->formatInfolistEntry('Transakcja', $state)),
                    TextEntry::make('transaction.warehouse_doc.created_at')
                        ->label('')
                        ->hidden(fn($state) => empty($state))
                        ->dateTime('Y-m-d H:i:s', 'Europe/Warsaw')
                        ->formatStateUsing(fn($state) => $this->formatInfolistEntry('Data dokumentu', $state)),
                    TextEntry::make('exp_date')
                        ->label('')
                        ->hidden(fn($state) => empty($state))
                        ->formatStateUsing(
                            fn($state) => $this->formatInfolistEntry('Data ważności', $state->format('Y-m-d'))
                        ),
                    TextEntry::make('series_no')
                        ->label('')
                        ->hidden(fn($state) => empty($state))
                        ->tooltip('Historia serii')
                        ->url(fn(Model $record) => ProductHistory::buildHistoryUrl($record), true)
                        ->icon(function (Model $record) {
                            return match (ProductHistory::buildFilterQuery($record)) {
                                null => null,
                                default => 'heroicon-o-document-text'
                            };
                        })
                        ->iconPosition(IconPosition::After)
                        ->formatStateUsing(fn($state) => $this->formatInfolistEntry('Seria', $state)),
                ]),
            Grid::make(1)
                ->schema([
                    TextEntry::make('product.name')
                        ->label('')
                        ->formatStateUsing(fn($state) => $this->formatInfolistEntry(__('app._.name'), $state)),
                    TextEntry::make('product.gtin')
                        ->label('')
                        ->formatStateUsing(fn($state) => $this->formatInfolistEntry(__('app._.gtin'), $state)),
                    TextEntry::make('product.description')
                        ->columnSpanFull()
                        ->label(__('app.products._.description'))
                        ->hidden(fn($state) => empty($state)),
                    TextEntry::make('product.grace_period')
                        ->columnSpanFull()
                        ->formatStateUsing(fn($state) => new HtmlString(nl2br($state)))
                        ->label(__('app.products._.grace_period'))
                        ->hidden(fn($state) => empty($state)),
                    TextEntry::make('product.manufacturer.name')
                        ->label(__('app.products._.manufacturer'))
                        ->hidden(fn($state) => empty($state)),
                ]),
            TextEntry::make('ext_link')
                ->hidden(fn($state) => empty($state))
                ->label(__('app.products._.ext_link'))
                ->url(function ($state) {
                    if (empty($state)) {
                        return '';
                    }
                    return $state;
                }, true),
            Grid::make(1)
                ->schema([
                    TextEntry::make('basic_unit')
                        ->label('')
                        ->hidden(fn($state) => empty($state))
                        ->formatStateUsing(
                            fn($state) => $this->formatInfolistEntry(__('app.products._.basic_unit'), $state)
                        ),
                    TextEntry::make('price_per_unit')
                        ->label('')
                        ->hidden(fn($state) => empty($state))
                        ->formatStateUsing(
                            fn($state) => $this->formatInfolistEntry(__('app.products._.price_per_unit'), $state)
                        ),
                    TextEntry::make('volume_ml')
                        ->label('')
                        ->hidden(fn($state) => empty($state))
                        ->formatStateUsing(
                            fn($state) => $this->formatInfolistEntry(__('app.products._.volume_ml'), $state)
                        ),
                    TextEntry::make('weight_gr')
                        ->label('')
                        ->hidden(fn($state) => empty($state))
                        ->formatStateUsing(
                            fn($state) => $this->formatInfolistEntry(__('app.products._.weight_gr'), $state)
                        ),
                    TextEntry::make('minimum_stock')
                        ->label('')
                        ->hidden(fn($state) => empty($state))
                        ->formatStateUsing(
                            fn($state) => $this->formatInfolistEntry(__('app.products._.minimum_stock'), $state)
                        ),
                    TextEntry::make('minimum_exp_date')
                        ->label('')
                        ->hidden(fn($state) => empty($state))
                        ->formatStateUsing(
                            fn($state) => $this->formatInfolistEntry(__('app.products._.minimum_exp_date'), $state)
                        ),
                ]),
        ];
    }

    public function formatInfolistEntry($label, $state): HtmlString
    {
        return new HtmlString("<span class='font-medium'>$label:</span> $state");
    }


    public function myInfo($id)
    {
        $il = new Infolist();
        $record = WarehouseItem::whereHash($id)->with(['transaction.warehouse_doc', 'product'])->first();
        $il->schema($this->infoInfolist())->record($record);
        $this->info = $il->toHtml();
    }

    public function view($data)
    {
        $this->myInfo($data);
        $this->dispatch('open-modal', id: 'view-product');
    }
}
