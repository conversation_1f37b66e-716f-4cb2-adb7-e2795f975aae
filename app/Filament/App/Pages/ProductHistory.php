<?php

namespace App\Filament\App\Pages;

use App\Enums\SystemModules;
use App\Filament\Filters\WHSelectFilter;
use App\Models\WarehouseItem;
use App\Models\WarehouseLog;
use Filament\Forms\Components\Select;
use Filament\Forms\Get;
use Filament\Pages\Page;
use Filament\Support\Enums\IconPosition;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\Indicator;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Livewire\Attributes\Url;
use pxlrbt\FilamentExcel\Actions\Tables\ExportAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;

class ProductHistory extends Page implements HasTable
{
    use InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.app.pages.product-history';

    #[Url]
    public ?array $tableFilters = null;

    public function __construct()
    {
        abort_if(!auth()->user()->isTenantAdmin(), 403);
    }

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isTenantAdmin() && (tenant()?->hasModule(SystemModules::WAREHOUSE) ?? false);
    }

    public static function getNavigationLabel(): string
    {
        return __('app.product_history.navigation.label');
    }

    /**
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return __('app.product_history.navigation.heading');
    }


    public function filterTableQuery(Builder $query): Builder
    {
        $state = $this->getTableFilterState('f');

        $type = $state['search_type'] ?? null;
        if (!filled($type)) {
            return $query;
        }

        switch ($type) {
            case 'transaction':
                $transaction = $state['transaction'];
                if (!filled($transaction)) {
                    return $query;
                }

                if (null === $test_model = WarehouseLog::whereTransactionId($transaction)->first()) {
                    return $query;
                }
                $target_trid = $test_model->source_doc_id ?? $transaction;
                $query->whereHas('warehouse_doc', function (Builder $sql) {
                    return $sql->where('warehouse_docs.cancelled', 0);
                })->where(
                    fn(Builder $query) => $query
                        ->where('transaction_id', $target_trid)
                        ->orWhere('source_doc_id', $target_trid)
                );
                break;
            case 'series':
                $series = $state['series_no'];
                if (!filled($series)) {
                    return $query;
                }
                $query->whereHas('warehouse_doc', function (Builder $sql) {
                    return $sql->where('warehouse_docs.cancelled', 0);
                })->where('series_no', $series);
                break;
            default:
                return $query;
        }

        $wh = $state['warehouse'];
        if (!filled($wh)) {
            return $query;
        }

        $id = WHSelectFilter::deconstructCompoundIndex($wh);
        return match (filled($id['id'])) {
            true => $query->where('warehouse_id', $id['id']),
            default => $query
        };
    }


    public function table(Table $table): Table
    {
        return $table
            ->query(
                function (Builder $query) {
                    $state = $this->getTableFilterState('f');
                    if (!filled($state['search_type'] ?? null) ||
                        (!filled($state['series_no'] ?? null) && !filled($state['transaction'] ?? null))
                    ) {
                        return WarehouseLog::query()->where('id', '<', 1);
                    }

                    return WarehouseLog::query()->with(['product', 'warehouse_doc', 'warehouse', 'user']);
                }
            )
            ->columns([
                TextColumn::make('warehouse_doc.accepted_at')
                    ->dateTime('Y-m-d H:i:s', 'Europe/Warsaw')
                    ->label(__('app.product_history.columns.accepted_at')),
                TextColumn::make('user.email')
                    ->label(__('app._.user')),
                TextColumn::make('type')
                    ->label(__('app.product_history.columns.doc_type'))
                    ->formatStateUsing(fn($state) => $state->name),
                TextColumn::make('warehouse_doc.transaction_id')
                    ->formatStateUsing(
                        fn(Model $record, $state) => $record->warehouse_doc->cancelled ? $state . ' (-)' : $state
                    )
                    ->label(__('app.product_history.columns.doc_number')),
                TextColumn::make('warehouse.name')
                    ->icon(fn(Model $record) => $this->setArrow($record))
                    ->iconPosition(IconPosition::After)
                    ->iconColor(fn(Model $record) => $this->setArrowColor($record))
                    ->label(__('app._.warehouse')),
                TextColumn::make('amount_1')
                    ->label(__('app.product_history.columns.amount'))
                    ->state(fn(Model $record) => $record->amount * $this->setInOrOutSign($record))
                    ->summarize($this->getSummarize()),
                TextColumn::make('partner_warehouse')
                    ->label(__('app.product_history.columns.partner_warehouse'))
                    ->state(fn(Model $record) => $this->setPartnerWarehouseName($record)),
                TextColumn::make('product.name')
                    ->label(__('app.product_history.columns.product')),
                TextColumn::make('exp_date')
                    ->label(__('app.product_history.columns.exp_date')),
                TextColumn::make('series_no')
                    ->label(__('app.product_history.columns.series_no')),
            ])
            ->filters(
                $this->getFilters()
            )
            ->filtersLayout(FiltersLayout::AboveContent)
            ->headerActions([
                ExportAction::make()
                    ->exports([
                        ExcelExport::make()
                            ->withFilename(function () {
                                return 'historia_produktu_' . now('Europe/Warsaw')->format('Y-m-d_H-i');
                            })
                            ->withColumns([

                            ])
                            ->fromTable()
                    ])
            ])
            ->paginated(false)
            ->deferFilters()
            ->filtersFormWidth(MaxWidth::Full)
            ->filtersFormColumns(4)
            ->filtersApplyAction(fn(Action $action) => $action->label(__('app._.apply')));
    }

    protected function getFilters()
    {
        return [
            Filter::make('f')
                ->columnSpan(3)
                ->columns(3)
                ->form([
                        Select::make('warehouse')
                            ->key('warehouse')
                            ->label(__('app._.warehouse'))
                            ->options(WHSelectFilter::getOptionsForLoggedInEmployee())
                        ,
                        Select::make('search_type')
                            ->label(__('app.product_history.filters.search_type'))
                            ->live()
                            ->options([
                                'transaction' => 'Transakcja',
                                'series' => 'Seria'
                            ]),
                        Select::make('transaction')
                            ->label(__('app.product_history.filters.transaction_number'))
                            ->searchable()
                            ->visible(fn(Get $get) => $get('search_type') === 'transaction')
                            ->options(
                                WarehouseLog::select('transaction_id', 'id')
                                    ->get()
                                    ->pluck('transaction_id', 'transaction_id')
                            ),
                        Select::make('series_no')
                            ->label(__('app.product_history.filters.series_no'))
                            ->searchable()
                            ->visible(fn(Get $get) => $get('search_type') === 'series')
                            ->options(
                                WarehouseLog::groupBy('series_no')
                                    ->whereNot('series_no', '')
                                    ->get()
                                    ->pluck('series_no', 'series_no')
                            ),
                    ])
                ->indicateUsing(function ($data, $state, Table $table) {
                    $indicators = [];
                    if (!empty($state['search_type'])) {
                        $indicators[] = Indicator::make(
                            __('app.product_history.filters.search_type')
                            . ': '
                            . __('app.product_history.filters.' . $state['search_type'])
                        )
                        ->removeField('search_type');
                    }

                    if (!empty($state['transaction'])) {
                        $indicators[] = Indicator::make(
                            __('app.product_history.filters.transaction')
                            . ': '
                            . $state['transaction']
                        )
                        ->removeField('transaction');
                    }

                    if (!empty($state['series_no'])) {
                        $indicators[] = Indicator::make(
                            __('app.product_history.filters.series_no')
                            . ': '
                            . $state['series_no']
                        )
                        ->removeField('series_no');
                    }

                    if (!empty($state['warehouse'])) {
                        $select = $table->getFilter('f')?->getForm()->getComponent('warehouse');
                        if (null !== $select) {
                            $opts = $select->getOptions();
                            $label = $opts[$select->getState()];
                            $indicators[] = Indicator::make("Magazyn: {$label}")->removeField('warehouse');
                        }
                    }
                    return $indicators;
                }),
        ];
    }

    protected function getSummarize()
    {
        return [
//                        Summarizer::make('in')
//                            ->label('Przyjęto')
//                            ->using(
//fn(Table $table) => $table->getRecords()->sum(fn($el) => $this->setInOrOutSign($el) > 0 ? $el->amount : 0)
//),
//                        Summarizer::make('out')
//                            ->label('Wydano')
//                            ->using(
//fn(Table $table) => $table->getRecords()->sum(fn($el) => $this->setInOrOutSign($el) < 0 ? $el->amount : 0)
//),
//                        Summarizer::make('left')
//                            ->label('Na stanie')
//                            ->using(fn(Table $table) => $table
//                                    ->getColumn('amount_1')
//                                    ?->getSummarizer('in')
//                                    ->getState() - $table->getColumn('amount_1')?->getSummarizer('out')->getState()),
        ];
    }

    protected static function filterGroupName(): string
    {
        return 'f';
    }

    protected static function filterSearchTypeName(): string
    {
        return 'search_type';
    }

    protected static function filterSeriesName(): string
    {
        return 'series_no';
    }

    protected static function filterTransactionName(): string
    {
        return 'transaction';
    }

    protected static function filterWarehouseName(): string
    {
        return 'warehouse';
    }

    public static function buildFilterQuery(Model|WarehouseLog|WarehouseItem $doc, string $type = 'series'): null|string
    {
        $tableFilters = [];
        $filters = [];

        switch ($type) {
            case 'series':
                if (filled($doc->series_no)) {
                    $filters[self::filterSeriesName()] = $doc->series_no;
                    $filters[self::filterSearchTypeName()] = 'series';
                } else {
                    return null;
                }
                break;
            case 'transaction':
                $filters[self::filterTransactionName()] = $doc->transaction_id;
                $filters[self::filterSearchTypeName()] = 'transaction';
                break;
            default:
                return null;
        }

        $tableFilters[self::filterGroupName()] = $filters;

        return urldecode(http_build_query([
            'tableFilters' => $tableFilters
        ]));
    }

    public static function buildHistoryUrl(Model|WarehouseLog|WarehouseItem $doc, string $type = 'series'): null|string
    {
        $qry = self::buildFilterQuery($doc, $type);
        return match ($qry) {
            null => null,
            default => self::getUrl() . '?' . $qry
        };
    }

    public function setInOrOutSign(Model $record): int
    {
        return $record->type->value < 20 ? 1 : -1;
    }

    public function setArrow(Model $record)
    {
        if (null !== $this->setReceptionDoc($record)) {
            return 'heroicon-o-arrow-left-end-on-rectangle';
        }

        if ((int)$record->type->value < 20) {
            return 'heroicon-m-arrow-left';
        }

        if ((int)$record->type->value > 20 && (int)$record->type->value !== 25) {
            return 'heroicon-o-arrow-right-end-on-rectangle';
        }

        return 'heroicon-m-arrow-right';
    }

    public function setArrowColor(Model $record)
    {
        if ((int)$record->type->value < 20) {
            return 'success';
        }
        return 'danger';
    }

    public function setPartnerWarehouseName(Model $record): string
    {
        return match (true) {
            $record->warehouse_doc->type->value < 20 && $record->warehouse_doc->target_warehouse === null =>
                $this->getTargetWHName($record, 'dostawca'),
            $record->warehouse_doc->type->value >= 20 && $record->warehouse_doc->target_warehouse === null =>
                $this->getTargetWHName($record, 'odbiorca'),
            default => $record->warehouse_doc->target_warehouse->name,
        };
    }

    public function setReceptionDoc(Model $record): null|string
    {
        return match ((int)$record->type->value < 20 && $record->type->value !== 15 && $record->warehouse_doc->target_warehouse === null) {
            true => $record->warehouse_doc->transaction_id,
            default => null
        };
    }

    protected function getTargetWHName(Model $record, string $default): string
    {
        return Str::of(explode(PHP_EOL, $record->warehouse_doc->partner_data)[0] ?? $default)->limit(25);
    }

}
