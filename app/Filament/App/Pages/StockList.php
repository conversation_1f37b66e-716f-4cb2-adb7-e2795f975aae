<?php

namespace App\Filament\App\Pages;

use App\Enums\WarehouseTypes;
use App\Filament\Actions\PrintAction;
use App\Filament\Filters\WHSelectFilter;
use App\Filament\Tables\Actions\PrintDocAction;
use App\Helpers\Identifiers;
use App\Models\WarehouseDoc;
use App\Models\WarehouseItem;
use App\Repositories\ProductsRepository;
use App\Repositories\WarehouseDocsRepository;
use BladeUI\Icons\Components\Icon;
use Filament\Actions\StaticAction;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;
use Filament\Pages\Page;
use Filament\Support\Enums\FontWeight;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\Layout\Split;
use Filament\Tables\Columns\Layout\Stack;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use Livewire\Component;

class StockList extends Page implements HasTable
{

    use InteractsWithTable;


    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static string $view = 'filament.app.pages.stock-list';

    protected static ?string $slug = 'my/warehouse';

    protected static ?int $navigationSort = -3;

    public ?WarehouseDoc $currentDoc = null;


    public $converted = '';

    public string $printUrl = '';

    public function __construct()
    {
        abort_if(!auth()->user()->hasWarehouse(), 403);
    }

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->hasWarehouse();
    }


    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return __('app.mywarehouse.page.heading');
    }

    public static function getNavigationLabel(): string
    {
        return __('app.mywarehouse.page.navigationLabel');
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(function (Builder $builder) {
                $qry = WarehouseItem::query()->with(['product', 'warehouse'])
                    ->groupBy('warehouse_id')
                    ->groupBy('transaction_id')
                    ->groupBy('series_no')
                    ->groupBy('price')
                    ->selectRaw('*, SUM(amount) as total')
                    ->where('reserved', 0);
                return $qry;
            })
            ->columns([
                Split::make([
                    Stack::make([
                        TextColumn::make('label1')
                            ->state('Produkt:')
                            ->weight(FontWeight::Bold)
                            ->hiddenFrom('sm'),
                        TextColumn::make('product.name')
                            ->label(__('app.mywarehouse.list.product'))
                            ->searchable()
                            ->grow(),
                        TextColumn::make('label1a')
                            ->state(__('app.mywarehouse.list.warehouse'))
                            ->weight(FontWeight::Bold)
                            ->hiddenFrom('sm'),
                        TextColumn::make('warehouse.name')
                            ->hiddenFrom('md')
                            ->label(__('app.mywarehouse.list.warehouse'))
                    ]),
                    Stack::make([
                        TextColumn::make('label2')
                            ->state('Seria:')
                            ->alignCenter()
                            ->weight(FontWeight::Bold)
                            ->hiddenFrom('sm'),
                        TextColumn::make('series_no')
                            ->searchable()
                            ->alignCenter()
                            ->label(__('app.mywarehouse.list.series_no')),
                        TextColumn::make('label3:')
                            ->state('Exp:')
                            ->alignCenter()
                            ->weight(FontWeight::Bold)
                            ->hiddenFrom('sm'),
                        TextColumn::make('exp_date')
                            ->date('Y-m-d')
                            ->label(__('app.mywarehouse.list.exp_date'))
                            ->alignCenter(),
                    ]),
                    Stack::make([
                        TextColumn::make('total')
                            ->label(__('app.mywarehouse.list.total'))
                            ->numeric()
                            ->alignCenter(),
                        IconColumn::make('status_ms')
                            ->state(true)
                            ->alignRight()
                            ->tooltip(fn(Model $record) => 'Minimalny stok: ' . $record->minimum_stock)
                            ->icon('heroicon-m-square-3-stack-3d')
                            ->color(
                                fn(Model $record) => match ((int)$record->total <=> (int)$record->minimum_stock ?? 0) {
                                    -1, 0 => 'warning',
                                    1 => 'success'
                                }
                            ),
                        IconColumn::make('status_exp')
                            ->state(true)
                            ->alignRight()
                            ->tooltip(
                                fn(Model $record) => 'Minimalny termin: '
                                    . now()->addDays($record->minimum_exp_date ?? 0)->format('Y-m-d')
                            )
                            ->icon('heroicon-m-calendar-days')
                            ->color(
                                fn($state, Model $record) => $this->getStatusColor($state, $record)
                            ),
                    ])->grow(false),
                    TextColumn::make('gtin')
                        ->label(__('app._.gtin'))
                        ->visibleFrom('md')
                        ->searchable()
                        ->alignRight(),
                    TextColumn::make('warehouse.name')
                        ->alignCenter()
                        ->visibleFrom('md')
                        ->label(__('app.mywarehouse.list.warehouse')),
                ]),
            ])
            ->filters([
                WHSelectFilter::make('warehouse_id')
                    ->options(fn() => WHSelectFilter::getOptionsForLoggedInEmployee())
                    ->label('Magazyn')
                    ->columnSpan(1)
                    ->default(fn() => WHSelectFilter::buildCompoundIndex(auth()->user()->warehouse)),
                Filter::make('gtin')
                    ->form([
                        TextInput::make('qrcode')
                            ->extraInputAttributes(['inputmode' => 'none'])
                            ->label('Barcode')
                            ->autofocus(true)
                            ->live(debounce: 300)
                            ->suffixAction(
                                \Filament\Forms\Components\Actions\Action::make('scan')
                                    ->icon('heroicon-o-camera')
                                    ->extraAttributes(['id' => 'scan-button'])
                                    ->livewireClickHandlerEnabled(false)
                                    ->url(null)
                                    ->action(null)
                                    ->label('Skanuj kod')
                            )
                    ])
                    ->modifyQueryUsing(function (Builder $query, $data) {
                        if (empty($data['qrcode'])) {
                            return $query;
                        }
                        return ProductsRepository::modifyQueryByBarcode($query, $data['qrcode']);
                    })
                    ->indicateUsing(function (array $data) {
                        if (empty($data['qrcode'])) {
                            return null;
                        }
                        return 'GTIN: ' . $data['qrcode'];
                    })
            ])
            ->filtersFormColumns(2)
            ->filtersLayout(FiltersLayout::AboveContent)
            ->bulkActions(
                $this->getBulkActions()
            )
            ->selectCurrentPageOnly()
            ->headerActions([
            ])
            ->checkIfRecordIsSelectableUsing(
                fn(Model $record) => $this->isRecordSelectable($record)
            );
    }


    public function getBulkActions()
    {
        $move_to_self = BulkAction::make('move_to_self')
            ->label('Przesun')
            ->form(fn(Collection $collection) => $this->GetMoveProductsForm($collection, 'move_to_self'))
            ->closeModalByClickingAway(false)
            ->action(function ($data) {
                return $this->bulkActionMoveToSelfProcess($data);
            })
            ->after(fn() => $this->deselectAllTableRecords())
            ->visible(
                fn() => $this->isBulkActionVisible(
                    'move_to_self',
                    $this->getTableFilterState('warehouse_id')['value'] ?? null
                )
            );


        $move_from_self = BulkAction::make('move_from_self')
            ->label('Zwróć')
            ->form(fn(Collection $collection) => $this->GetMoveProductsForm($collection, 'move_from_self'))
            ->closeModalByClickingAway(false)
            ->action(function ($data) {
                return $this->bulkActionMoveFromSelfProcess($data);
            })
            ->deselectRecordsAfterCompletion()
            ->visible(
                fn() => $this->isBulkActionVisible(
                    'move_from_self',
                    $this->getTableFilterState('warehouse_id')['value'] ?? null
                )
            );

        $external_issue = BulkAction::make('external_issue')
            ->label('Wyd. zewnętrzne')
            ->form(fn(Collection $collection) => $this->GetMoveProductsForm($collection, 'external_issue'))
            ->closeModalByClickingAway(false)
            ->action(function ($data, array $arguments, Component $livewire) {
                $this->printUrl = '';
                $current = $this->bulkActionExternalIssueProcess($data);
                if (false === $current) {
                    return;
                }

                $this->deselectAllTableRecords();
                $this->currentDoc = $current;
                if ($arguments['print'] ?? false) {
                    $this->printUrl = route('printPDF', ['doc' => $current->transaction_id]);
                }
            })
            ->after(function () {
                if ('' !== $this->printUrl) {
                    $this->mountAction('print', ['record' => $this->currentDoc]);
                }
            })
            ->extraModalFooterActions(fn(BulkAction $action): array => [
                $action->makeModalSubmitAction('Zatwierdź i drukuj', arguments: ['print' => true]),
            ])
            ->visible(
                fn() => $this->isBulkActionVisible(
                    'external_issue',
                    $this->getTableFilterState('warehouse_id')['value'] ?? null
                )
            );

        return [
            $move_from_self,
            $move_to_self,
            $external_issue,
        ];
    }

    public function printAction(): \Filament\Actions\Action
    {
        return PrintAction::make('pdf')
            ->modalSubmitAction(
                function (StaticAction $action, Model $record, Component $livewire) {
                    return $action->url(route('printPDF', ['doc' => $record->transaction_id]), true)
                        ->extraAttributes(
                            ['x-on:click' => new HtmlString('closePrintModal(\'' . $livewire->getId() . '-action\')')]
                        );
                }
            )
            ->modalCancelAction(
                function (StaticAction $action, Model $record, Component $livewire) {
                    return $action->extraAttributes(
                        ['x-on:click' => new HtmlString('closePrintModal(\'' . $livewire->getId() . '-action\')')]
                    )->label('Zamknij');
                }
            )
            ->record(function () {
                return match ($this->record ?? null) {
                    default => $this->record,
                    null => match (count($this->mountedActionsArguments) > 0) {
                        true => $this->mountedActionsArguments[0]['record'] ?? null,
                        default => null
                    }
                };
            });
    }

    public function isRecordSelectable(Model $record)
    {
        return $record->warehouse->owner_type === WarehouseTypes::COMPANY->value ||
            ($record->warehouse->owner_type === WarehouseTypes::USER->value &&
                auth()->user()->warehouse->id === $record->warehouse_id);
    }

    public function isBulkActionVisible($action, $filter_state): bool
    {
        if (empty($filter_state)) {
            return false;
        }
        $mywh = auth()->user()?->warehouse?->id;
        $selected = WHSelectFilter::deconstructCompoundIndex($filter_state)['id'];

        return match ($action) {
            'move_to_self' => (int)$mywh !== (int)$selected,
            default => (int)$mywh === (int)$selected
        };
    }

    public function bulkActionMoveToSelfProcess($data): bool
    {

        $src_warehouse = WHSelectFilter::deconstructCompoundIndex(
            $this->getTableFilterState('warehouse_id')['value']
        )['id'];
        $items = [];
        foreach ($data as $source_id => $amount) {
            $item = [];
            $item['source_doc_id'] = explode('_', $source_id)[1];
            $item['amount'] = $amount;
            $items[] = $item;
        }
        $doc = [
            'header' => [
                'warehouse_id' => $src_warehouse,
                'target_warehouse_id' => auth()->user()->warehouse->id,
            ],
            'items' => $items
        ];
        return WarehouseDocsRepository::ProcessEmployeeMMDoc($doc);
    }

    public function bulkActionMoveFromSelfProcess($data): bool
    {

        $src_warehouse = WHSelectFilter::deconstructCompoundIndex(
            $this->getTableFilterState('warehouse_id')['value']
        )['id'];
        $target_warehouse_id = WHSelectFilter::deconstructCompoundIndex($data['target_warehouse_id'])['id'];
        unset($data['target_warehouse_id']);
        $items = [];
        foreach ($data as $source_id => $amount) {
            $item = [];
            $item['source_doc_id'] = explode('_', $source_id)[1];
            $item['amount'] = $amount;
            $items[] = $item;
        }
        $doc = [
            'header' => [
                'warehouse_id' => $src_warehouse,
                'target_warehouse_id' => $target_warehouse_id,
            ],
            'items' => $items
        ];

        return WarehouseDocsRepository::ProcessEmployeeMMDoc($doc);
    }

    public function bulkActionExternalIssueProcess($data): bool|WarehouseDoc
    {
        $src_warehouse = auth()->user()->warehouse->id;
        $partner = Identifiers::deconstructCompoundIndex($data['partner']);
        $doc = [
            'header' => [
                'warehouse_id' => $src_warehouse,
                'partner_id' => (int)$partner['id'],
                'signatory_name' => $data['signatory_name'],
                'signatory_last_name' => $data['signatory_last_name'],
                'notes' => $data['notes'],
            ],
            'items' => []
        ];
        unset($data['partner'], $data['signatory_name'], $data['signatory_last_name'], $data['notes']);
        $items = [];
        foreach ($data as $source_id => $amount) {
            $item = [];
            $item['source_doc_id'] = explode('_', $source_id)[1];
            $item['amount'] = $amount;
            $items[] = $item;
        }

        $doc['items'] = $items;

        return WarehouseDocsRepository::ProcessEmployeeExternalIssueDoc($doc);
    }


    public function GetMoveProductsForm(Collection $records, $action = 'move_to_self'): array
    {

        $qry = WarehouseItem::query()->with(['product'])
            ->groupBy('warehouse_id')
            ->groupBy('transaction_id')
            ->groupBy('series_no')
            ->groupBy('price')
            ->selectRaw('*, SUM(amount) as total')
            ->whereIn('transaction_id', $records->pluck('transaction_id'))
            ->where('warehouse_id', $records->pluck('warehouse_id')->first());

        $selected = $qry->get();
        $form = [];
        if ($action === 'move_from_self') {
            $companywh = auth()->user()?->getTenant()?->companyWarehouse()->get();
            if (count($companywh) === 1) {
                $form[] = Hidden::make('target_warehouse_id')
                    ->formatStateUsing(fn() => Identifiers::buildCompoundIndex($companywh->first()));
            } else {
                $options = [];
                $companywh->each(function ($item) use (&$options) {
                    $options[Identifiers::buildCompoundIndex($item)] = $item->name;
                });
                $form[] = Select::make('target_warehouse_id')
                    ->required()
                    ->label('Wybierz magazyn docelowy')
                    ->options(
                        $options
                    )->name(true);
            }
        }

        if ($action === 'external_issue') {
            $partners = auth()->user()->partners;
            $options = [];
            $partners->each(function ($item) use (&$options) {
                $options[Identifiers::buildCompoundIndex($item)] = $item->name;
            });
            $form[] = Select::make('partner')
                ->required()
                ->label('Wybierz partnera')
                ->options(
                    $options
                );
            $form[] = Grid::make()
                ->columns(2)
                ->schema([
                    TextInput::make('signatory_name')
                        ->label(__('app.warehouse_docs.create.signatory_name'))
                        ->inlineLabel(),
                    TextInput::make('signatory_last_name')
                        ->label(__('app.warehouse_docs.create.signatory_last_name'))
                        ->inlineLabel(),
                    Textarea::make('notes')
                        ->label(__('app.warehouse_docs.create.notes'))
                        ->columnSpanFull()
                        ->maxLength(255),
                ]);
        }

        foreach ($selected as $record) {
            $form[] =
                TextInput::make('amount_' . $record->transaction_id)
                    ->label($record->product->name . ', seria: ' . $record->series_no)
                    ->inlineLabel()
                    ->formatStateUsing(fn() => $record->total)
                    ->numeric()
                    ->maxValue(fn() => $record->total)
                    ->minValue(1)
                    ->hint(fn() => 'Max: ' . $record->total);
        }
        return $form;
    }

    public function hasUserWarehouse()
    {
        return auth()->user()?->warehouse()->count() > 0;
    }

    public function getStatusColor($state, Model $record): string
    {
        return match (true) {
            empty($record->exp_date) => 'gray',
            empty($record->minimum_exp_date) => $record->exp_date->isPast() ? 'danger' : 'gray',
            default => match (true) {
                $record->exp_date->isPast() => 'danger',
                abs(now()->diffInDays($record->exp_date)) <= (int)$record->minimum_exp_date => 'warning',
                default => 'success'
            }
        };
    }

}
