<?php

namespace App\Filament\App\Pages;

use App\Enums\SystemModules;
use Filament\Actions\Action;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Pages\Page;

class Charts extends Page
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-presentation-chart-line';
    protected ?string $heading = 'Analiza';
    protected static ?string $navigationLabel = 'Analiza';

    protected static ?int $navigationSort = 3;

    protected static string $view = 'filament.app.pages.stats';

    protected static ?string $slug = 'charts';

    public ?array $data = [];
    public array $chartData = [];

    public function mount(): void
    {
        $this->form->fill([
            'date_range' => null,
            'start_date' => null,
            'end_date' => null,
            'resolution' => 'd',
        ]);
    }

    public static function canAccess(): bool
    {
        return parent::canAccess() && (tenant()?->hasModule(SystemModules::SIMPLE_CHARTS) ?? false);
    }

    public static function shouldRegisterNavigation(): bool
    {
        return tenant()?->hasModule(SystemModules::SIMPLE_CHARTS) ?? false;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Parametry')
                    ->collapsible(true)
                    ->headerActions([
                        \Filament\Forms\Components\Actions\Action::make('show_chart')
                            ->label('Pokaż wykres')
                            ->disabled(fn() => !$this->isFormFilled())
                            ->icon('heroicon-o-presentation-chart-line')
                            ->color('success')
                            ->action(function () {
                                $this->dispatch('chartDataUpdated', $this->data);
                            }),
                    ])
                    ->schema([
                        Select::make('date_range')
                            ->label('Zakres czasowy')
                            ->options([
                                'today' => 'Dzisiaj',
                                'this_week' => 'Ten tydzień',
                                'last_week' => 'Ostatni tydzień',
                                'this_month' => 'Ten miesiąc',
                                'last_month' => 'Ostatni miesiąc',
                                'this_year' => 'Ten rok',
                                'last_year' => 'Ostatni rok',
                                'specific_year' => 'Konkretny rok',
                                'custom' => 'Inny zakres',
                            ])
                            ->live()
                            ->required(),


                        DatePicker::make('start_date')
                            ->label('Data od')
                            ->native(false)
                            ->time(false)
                            ->seconds(false)
                            ->locale('pl')
                            ->requiredIf('date_range', 'custom')
                            ->displayFormat('Y-m-d')
                            ->hidden(fn ($get) => $get('date_range') === 'specific_year')
                            ->disabled(fn($get) => $get('date_range') !== 'custom'),

                        DatePicker::make('end_date')
                            ->label('Data do')
                            ->time(false)
                            ->minDate(fn($get) => $get('start_date'))
                            ->seconds(false)
                            ->native(false)
                            ->locale('pl')
                            ->displayFormat('Y-m-d')
                            ->requiredIf('date_range', 'custom')
                            ->hidden(fn ($get) => $get('date_range') === 'specific_year')
                            ->disabled(fn($get) => $get('date_range') !== 'custom'),

                        Select::make('year')
                            ->label('Wybierz rok')
                            ->options(fn() => array_combine(
                                range(date('Y'), date('Y') - 10),
                                range(date('Y'), date('Y') - 10)
                            ))
                            ->native(false)
                            ->columnSpan(2)
                            ->requiredIf('date_range', 'specific_year')
                            ->visible(fn ($get) => $get('date_range') === 'specific_year'),

                        Select::make('resolution')
                            ->label('Grupowanie')
                            ->options([
                                'd' => 'Dzień',
                                'm' => 'Miesiąc',
                                'y' => 'Rok',
                            ])
                            ->live()
                            ->native(false)
                            ->default(fn() => 'd')
                            ->required(),
                    ])
                ->columns(4),
            ])
            ->statePath('data')
            ->live();
    }

    public function isFormFilled(): bool
    {
        return ($this->data['date_range'] ?? false);
    }

    protected function getHeaderActions(): array
    {
        return [
        ];
    }
}
