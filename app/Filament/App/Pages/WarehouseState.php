<?php

namespace App\Filament\App\Pages;

use App\Enums\ProductDemandsStatuses;
use App\Enums\SystemModules;
use App\Filament\App\Resources\ProductDemandResource;
use App\Models\ProductDemand;
use App\Models\WarehouseItem;
use Filament\Pages\Page;
use Filament\Support\Colors\Color;
use Filament\Support\Enums\FontWeight;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Filament\Forms;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use pxlrbt\FilamentExcel\Actions\Tables\ExportAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;

class WarehouseState extends Page implements HasTable
{

    use InteractsWithTable;

    protected static string $view = 'filament.app.pages.warehouse-state';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected ?string $heading = "Stan magazynu";

    public string $info = '';

    public static function getNavigationGroup(): ?string
    {
        return __('app._.warehouse_state');
    }

    public static function getNavigationLabel(): string
    {
        return __('app.product_demands.page.stateNavigationLabel');
    }

    public static function canAccess(): bool
    {
        return parent::canAccess() && tenant()->hasModule(SystemModules::WAREHOUSE);
    }

    /**
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return __('app.product_demands.page.stateHeading');
    }

    public static function getSlug(): string
    {
        return ProductDemandResource::getSlug() . '/current-state';
    }

    protected static ?int $navigationSort = 2;

    public static function shouldRegisterNavigation(): bool
    {
        return (auth()->user()?->isTenantAdmin() ?? false) &&
            (tenant()?->hasModule(SystemModules::WAREHOUSE) ?? false);
    }


    public function table(Table $table): Table
    {

        $query = ProductDemand::query()
            ->selectRaw(
                'id, product_id, demand_at, ' .
                'SUM(IF(`status` = 1, amount, 0)) as requested, ' .
                'SUM(IF(`status` = 2, amount, 0)) as ongoing, ' .
                'IFNULL(items.total, 0) as total'
            )
            ->groupBy('product_id')
            ->where(function (Builder $query) {
                $query->where('status', ProductDemandsStatuses::ACTIVE->value)
                    ->orWhere('status', ProductDemandsStatuses::IN_PROGRESS->value);
            });
        $subquery = WarehouseItem::query();
        $subquery->groupBy('product_id');
        $subquery->selectRaw('product_id as item_product_id, SUM(amount) as total');
        $query->leftJoinSub($subquery, 'items', 'product_id', '=', 'items.item_product_id');

        return $table->query($query)
            ->columns([
                TextColumn::make('product.name')
                    ->label(__('app._.name')),
                TextColumn::make('total')
                    ->numeric()
                    ->alignRight()
                    ->state(function (Model $record) {
                        return (int)($record->total ?? 0);
                    })
                    ->label('Stan aktualny'),
                TextColumn::make('requested')
                    ->state(function (Model $record) {
                        return (int)($record->requested ?? 0);
                    })
                    ->numeric()
                    ->default(0)
                    ->alignRight()
                    ->label('Zapotrzebowanie'),
                TextColumn::make('ongoing')
                    ->state(function (Model $record) {
                        return (int)($record->ongoing ?? 0);
                    })
                    ->numeric()
                    ->default(0)
                    ->alignRight()
                    ->label('W trakcie'),
                TextColumn::make('calculated_state')
                    ->label('Stan po realizacji')
                    ->state(function (Model $record) {
                        return (int)$record->total - (int)($record->requested ?? 0) + (int)($record->ongoing ?? 0);
                    })
                    ->weight(function ($state) {
                        return match ((int)$state <=> 0) {
                            -1 => FontWeight::ExtraBold,
                            0 => FontWeight::Bold,
                            1 => null,
                        };
                    })
                    ->color(function ($state) {
                        return match ((int)$state <=> 0) {
                            -1 => Color::Red,
                            0 => Color::Amber,
                            1 => null,
                        };
                    })
                    ->alignRight()
            ])
            ->filters([
                Filter::make('demand_at')
                    ->form([
                        Forms\Components\DatePicker::make('demand_to')
                            ->minDate(now()->format('Y-m-d'))
                            ->displayFormat('Y-m-d')
                            ->native(false)
                            ->formatStateUsing(fn($state) => match ($state) {
                                null => null,
                                default => (new Carbon($state))->format('Y-m-d')
                            })
                            ->time(false)
                            ->seconds(false)
                            ->label('Data wymagania do')
                    ])
                    ->indicateUsing(fn($state) => match (filled($state['demand_to'])) {
                        false => null,
                        default => 'Data Wymagania do: ' . (new Carbon($state['demand_to']))->format('Y-m-d')
                    })
                    ->modifyQueryUsing(function (Builder $query, $state) {
                        return match (filled($state['demand_to'])) {
                            false => $query,
                            default => $query
                                ->whereDate('demand_at', '<=', (new Carbon($state['demand_to']))->format('Y-m-d'))
                                ->groupBy()
                        };
                    }),
                Filter::make('minimum')
                    ->form([
                        Forms\Components\TextInput::make('minimum_stack')
                            ->label('Minimalny oczekiwany stan')
                            ->numeric()
                    ])
                    ->indicateUsing(fn($state) => match (filled($state['minimum_stack'])) {
                        false => null,
                        default => 'Stan końcowy do: ' . $state['minimum_stack']
                    })
                    ->modifyBaseQueryUsing(function (Builder $query, $state) {
                        return match (filled($state['minimum_stack'])) {
                            false => $query,
                            default => $query
                                ->havingRaw('total - requested < ?', [$state['minimum_stack']])
                        };
                    })
            ])
            ->filtersLayout(Tables\Enums\FiltersLayout::AboveContent)
            ->headerActions([
                ExportAction::make()
                    ->exports([
                        ExcelExport::make()
                            ->withFilename(function (self $livewire) {
                                $name = 'Zapotrzebowanie_' . now('Europe/Warsaw')->format('Ymd_Hi');
                                $stack = $livewire->getTable()->getFilter('minimum')->getState();
                                $date = $livewire->getTable()->getFilter('demand_at')->getState();
                                if (filled($stack['minimum_stack'])) {
                                    $name .= '_stack-' . $stack['minimum_stack'];
                                }
                                if (filled($date['demand_to'])) {
                                    $name .= '_na_dzien-' . (new Carbon($date['demand_to']))->format('Y-m-d');
                                }
                                return $name;
                            })
                            ->fromTable()
                    ])
            ]);
    }
}
