<?php

namespace App\Filament\App\Pages;

use App\Enums\SystemModules;
use App\Filament\Filters\WHSelectFilter;
use App\Models\Stocktaking as StocktakingMod;
use App\Models\StocktakingItems;
use App\Models\Warehouse;
use App\Repositories\StocktakingRepository;
use Filament\Forms\Components\Select;
use Filament\Forms\Get;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\CreateAction;
use Filament\Pages\Page;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\Indicator;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use pxlrbt\FilamentExcel\Actions\Tables\ExportAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;

class Stocktaking extends Page implements HasTable
{

    use InteractsWithTable;


    protected static ?string $navigationIcon = 'heroicon-o-cube-transparent';

    protected static string $view = 'filament.app.pages.stocktaking';

    protected static ?int $navigationSort = -3;

    protected static ?string $slug = 'warehouses/stocktaking';

    public $converted = '';

    public static function getNavigationGroup(): ?string
    {
        return match (auth()->user()->isTenantAdmin()) {
            true => __('app._.warehouse_state'),
            default => null
        };
    }

    public static function canAccess(): bool
    {
        return parent::canAccess() && tenant()->hasModule(SystemModules::WAREHOUSE);
    }

    public function __construct()
    {
        abort_if(!auth()->user()->isTenantAdmin(), 403);
    }

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isTenantAdmin() && (tenant()?->hasModule(SystemModules::WAREHOUSE) ?? false);
    }



    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return __('app.stocktaking.navigation.heading');
    }

    public static function getNavigationLabel(): string
    {
        return __('app.stocktaking.navigation.label');
    }


    public function table(Table $table): Table
    {

        return $table
            ->query(function (Builder $builder) {
                $state = $this->getTableFilterState('f');
                if (!filled($state['status'] ?? null) ||
                    !filled($state['warehouse'] ?? null) ||
                    !filled($state['stocktaking'])
                ) {
                    return StocktakingItems::query()->where('id', '<', 1);
                }

                $query = StocktakingItems::query()->with(['product', 'stocktaking', 'stocktaking.warehouse']);
                $whid = WHSelectFilter::deconstructCompoundIndex($state['warehouse']);
                if ($state['status'] === 'open') {
                    $opened = StocktakingMod::whereNull('accepted_at')
                        ->where('warehouse_id', $whid['id'])
                        ->first();
                } else {
                    $hash = $state['stocktaking'];
                    $opened = StocktakingMod::whereNotNull('accepted_at')
                        ->where('hash', $hash)->first();
                }

                if (null !== $opened) {
                    $query->where('stocktaking_id', $opened->id);
                }

                if (filled($state['series_no'] ?? null)) {
                    $query->where('series_no', $state['series_no']);
                }

                return $query;
            })
            ->columns([
                TextColumn::make('product.name')
                    ->label(__('app.mywarehouse.list.product'))
                    ->searchable(),
                TextColumn::make('series_no')
                    ->searchable()
                    ->alignCenter()
                    ->label(__('app.stocktaking.columns.series_no')),
                TextColumn::make('base_amount')
                    ->label(__('app.stocktaking.columns.base_amount'))
                    ->width('5%')
                    ->grow(false)
                    ->alignRight(),
                TextColumn::make('gtin')
                    ->label(__('app._.gtin'))
                    ->visibleFrom('md')
                    ->searchable()
                    ->alignRight(),
                TextColumn::make('stocktaking.warehouse.name')
                    ->alignCenter()
                    ->label(__('app._.warehouse')),
                TextInputColumn::make('real_amount')
                    ->label(__('app.stocktaking.columns.real_amount'))
                    ->alignRight()
                    ->updateStateUsing(fn(Model $record, $state) => $this->updateRealStock($record, $state))
                    ->disabled(fn() => $this->showClosedFromHash()),
                TextColumn::make('difference_amount')
                    ->label(__('app.stocktaking.columns.difference'))
                    ->alignEnd()
            ])
            ->filters(
                $this->getFilters()
            )
            ->filtersFormColumns(4)
            ->deferFilters()
            ->filtersApplyAction(fn(Action $action) => $action->label(__('app._.apply')))
            ->filtersLayout(FiltersLayout::AboveContent)
            ->defaultPaginationPageOption(50)
            ->headerActions([
                ExportAction::make()
                    ->exports([
                        ExcelExport::make()
                            ->withFilename(function () {
                                $chosen = $this->getTableFilterState('f')['stocktaking'] ?? null;
                                $prefix = 'inwent_';
                                if (!filled($chosen)) {
                                    $name = $prefix . now('Europe/Warsaw')->format('Y-m-d_H-i');
                                } else {
                                    $record = StocktakingMod::hash($chosen)->first();
                                    $wh = $record->warehouse->name;
                                    $name = $prefix .
                                        $wh .
                                        '_' .
                                        $record->for_date->format('Y-m-d') .
                                        '_' .
                                        $record->hash;
                                }
                                return $name;
                            })
                            ->fromTable()
                    ])
                    ->visible(
                        fn() => $this->getTable()->getRecords()->count() > 0
                    ),
                CreateAction::make()
                    ->form([
                        Select::make('warehouse')
                            ->options(function () {
                                $currentStocktakingWarehouses = StocktakingMod::active()
                                    ->groupBy('warehouse_id')->pluck('warehouse_id');
                                $options = null;
                                foreach (Warehouse::select('id', 'name', 'hash')
                                             ->whereNotIn('id', $currentStocktakingWarehouses)
                                             ->get() as $row) {
                                    $options[WHSelectFilter::buildCompoundIndex($row)] = $row['name'];
                                }
                                return $options;
                            })
                            ->label(__('app._.warehouse'))
                            ->required()
                            ->columnSpan(1),
                        Select::make('user')
                            ->options(function () {
                                return tenant()->user()->get()->pluck('email', 'id');
                            })
                            ->label(__('app.stocktaking.actions.responsible_person'))
                            ->columnSpan(1),
                    ])
                    ->name('create_stocktaking')
                    ->modalHeading(__('app.stocktaking.actions.create_stocktaking'))
                    ->createAnother(false)
                    ->label(__('app.stocktaking.actions.create_stocktaking'))
                    ->after(fn() => $this->resetTable())
                    ->using(fn(array $data) => $this->createStocktaking($data)),
                Action::make('end_stocktaking')
                    ->label('Zakończ aktywną')
                    ->color('danger')
                    ->visible(fn() => $this->canFinish())
                    ->requiresConfirmation()
                    ->after(fn() => $this->resetTable())
                    ->action(fn() => $this->finishStocktaking()),
                Action::make('delete')
                    ->label(__('app.stocktaking.actions.delete_stocktaking'))
                    ->color('danger')
                    ->visible(fn() => $this->getAllTableRecordsCount() > 0)
                    ->after(fn() => $this->resetTable())
                    ->requiresConfirmation()
                    ->action(fn() => $this->removeStockTaking()),
            ]);
    }

    protected function getFilters()
    {
        return [
            Filter::make('f')
                ->columnSpan(3)
                ->columns(3)
                ->form([
                    Select::make('status')
                        ->key('status')
                        ->label(__('app.stocktaking.filters.status'))
                        ->live()
                        ->afterStateUpdated(function () {
                            $filterFormGroup = $this->getTableFiltersForm()->getComponent('f');
                            $filterFields = $filterFormGroup?->getChildComponentContainer()->getFlatFields();
                            $stk = $filterFields['stocktaking'] ?? null;
                            $wh = $filterFields['warehouse'] ?? null;

                            if (null === $wh || !filled($wh->getState())) {
                                return;
                            }

                            $stk->state(null);
                            $wh->state(null);
                            $this->applyTableFilters();
                        })
                        ->options([
                            'open' => __('app.stocktaking.filters.open'),
                            'closed' => __('app.stocktaking.filters.closed'),
                        ]),
                    Select::make('warehouse')
                        ->key('warehouse')
                        ->live()
                        ->afterStateUpdated(function () {
                            $filterFormGroup = $this->getTableFiltersForm()->getComponent('f');
                            $filterFields = $filterFormGroup?->getChildComponentContainer()->getFlatFields();
                            $stk = $filterFields['stocktaking'] ?? null;
                            if (null === $stk || !filled($stk->getState())) {
                                return;
                            }
                            $stk->state(null);
                            $this->applyTableFilters();
                        })
                        ->visible(fn(Get $get) => filled($get('status')))
                        ->label(__('app._.warehouse'))
                        ->options(WHSelectFilter::getOptionsForLoggedInEmployee())
                    ,
                    Select::make('stocktaking')
                        ->key('stocktaking')
                        ->visible(fn(Get $get) => filled($get('warehouse')))
                        ->label(__('app.stocktaking.filters.stocktaking_label'))
                        ->requiredWith('warehouse')
                        ->options(function (Get $get) {
                            $whid = WHSelectFilter::deconstructCompoundIndex($get('warehouse'));
                            $status = $get('status');
                            return match ($status) {
                                'closed' => StocktakingMod::where('warehouse_id', $whid['id'])->closed()->get()
                                    ->mapWithKeys(
                                        fn($row, $idx) => [
                                            $row->hash => $row->for_date->format('Y-m-d') . ' (' . $row->hash . ')'
                                        ]
                                    ),
                                default => StocktakingMod::where('warehouse_id', $whid['id'])->active()->get()
                                    ->mapWithKeys(
                                        fn($row, $idx) => [
                                            $row->hash => $row->for_date->format('Y-m-d') . ' (' . $row->hash . ')'
                                        ]
                                    ),
                            };
                        })
                    ,
                ])
                ->indicateUsing(function ($data, $state, Table $table) {
                    $indicators = [];
                    if (!empty($state['status'])) {
                        $indicators[] = Indicator::make(
                            __('app.stocktaking.filters.status') .
                            ': ' .
                            __('app.stocktaking.filters.' . $state['status'])
                        )
                            ->removeField('status');
                    }

                    if (!empty($state['warehouse'])) {
                        $select = $table->getFilter('f')?->getForm()->getComponent('warehouse');
                        if (null !== $select) {
                            $opts = $select->getOptions();
                            if (filled($select->getState())) {
                                $label = $opts[$select->getState()];
                                $indicators[] = Indicator::make("Magazyn: {$label}")->removeField('warehouse');
                            }
                        }
                    }

                    if (!empty($state['stocktaking'])) {
                        $select = $table->getFilter('f')?->getForm()->getComponent('warehouse');
                        if (null !== $select && filled($select->getState())) {
                            $indicators[] = Indicator::make(
                                __('app.stocktaking.filters.stocktaking') . ': ' . $state['stocktaking']
                            )
                                ->removeField('stocktaking');
                        }
                    }

                    if (!empty($state['series_no'])) {
                        $indicators[] = Indicator::make(
                            __('app.product_history.filters.series_no') . ': ' . $state['series_no']
                        )
                            ->removeField('series_no');
                    }

                    return $indicators;
                }),
        ];
    }

    public function updateRealStock(Model|StocktakingItems $record, $state): Model|StocktakingItems
    {
        $record->updateRealAmount((int)$state);
        return $record;
    }

    public function createStocktaking($data): StocktakingMod
    {
        return (new StocktakingRepository())->createStockTakingWithItems($data);
    }

    public function itemsExist(): bool
    {
        $activeFilter = $this->getWarehouseFilterValue();
        $id = WHSelectFilter::deconstructCompoundIndex($activeFilter['value'])['id'];
        return StocktakingMod::where('warehouse_id', $id)
                ->whereNull('accepted_at')->count() > 0;
    }

    public function canFinish(): bool
    {
        $hash = $this->getTableFilterState('f')['stocktaking'];
        return match (filled($hash)) {
            default => false,
            true => StocktakingMod::where('hash', $hash)->whereNull('accepted_at')->count() > 0
        };
    }

    public function getWarehouseFilterValue(): ?string
    {
        return $this->getTableFilterState('f')['warehouse'];
    }

    public function showClosedFromHash(): bool
    {
        return $this->getTableFilterState('f')['status'] === 'closed';
    }

    public function removeStockTaking()
    {
        $stock = $this->getTableFilterState('f')['stocktaking'];
        (new StocktakingRepository())->removeStockTaking(StocktakingMod::hash($stock)->first());
    }

    public function finishStocktaking(): bool
    {
        $hash = $this->getTableFilterState('f')['stocktaking'] ?? null;
        if (filled($hash)) {
            $stocktaking = StocktakingMod::where('hash', $hash)->first();
            $repo = new StocktakingRepository();
            $repo->finishStockTaking($stocktaking);
        }
        return true;
    }
}
