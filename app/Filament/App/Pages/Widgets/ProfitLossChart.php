<?php

namespace App\Filament\App\Pages\Widgets;

//use App\Models\Sensor;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Support\RawJs;
use Filament\Widgets\ChartWidget;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class ProfitLossChart extends ChartWidget implements HasForms
{
    use InteractsWithForms;

    protected static ?string $heading = 'Przychód / Koszt';


    protected static ?string $maxHeight = '600px';

    public ?string $filter = 'today';
    public ?string $sensor = '';
    protected static ?string $pollingInterval = null;

    public array $filters = [];

    protected static string $view = 'filament.app.pages.chart-widget';

    public function mount(): void
    {
        $this->form->fill();
        parent::mount(); // TODO: Change the autogenerated stub
    }

    public function form(Form $form)
    {
        return $form
            ->columns(2)
            ->statePath('filters')
            ->schema([
                DatePicker::make('date')
                    ->live()
                    ->displayFormat('Y-m-d')
                    ->format('Y-m-d')
                    ->native(false)
                    ->default(Carbon::now()->format('Y-m-d'))
                    ->closeOnDateSelection()
                    ->formatStateUsing(fn($state) => $state ?? Carbon::now()->format('Y-m-d'))
                    ->inlineLabel(true)
                    ->hiddenLabel()
                    ->label('Data'),
                Select::make('sensor')
                    ->live(onBlur: false)
                    ->selectablePlaceholder()
                    ->hiddenLabel()
                    ->placeholder('Wybierz sensor')
                    ->options(Sensor::active()->forUser(auth()->user())->pluck('address', 'id'))
                    ->inlineLabel(true)
                    ->label('Time')
            ]);
    }

    protected function getData(): array
    {
        if (empty($this->filters['date']) || blank($this->filters['sensor'])) {
            return [];
        }

        $date = Carbon::make($this->filters['date'])->format('Y-m-d');
        $sensor = $this->filters['sensor'];

        $data = DB::select("WITH TimeIntervals AS (
SELECT timestamp, TEMP_EXTERNAL, TEMP_CO_ORDERED, TEMP_CO_ACTUAL,
        FLOOR((timestamp - UNIX_TIMESTAMP(?)) / 600) AS interval_id
FROM measurements
WHERE DATE(FROM_UNIXTIME(timestamp)) = ? AND sensor_object_id = ?
ORDER BY timestamp ASC
)
SELECT
    FROM_UNIXTIME(interval_id * 600 + UNIX_TIMESTAMP(?)) AS interval_start,
    AVG(TEMP_EXTERNAL) AS av_temp,
    AVG(TEMP_CO_ORDERED) AS av_temp_ord,
    AVG(TEMP_CO_ACTUAL) AS av_temp_act,
    MIN(TEMP_CO_ACTUAL) as minimum,
    MAX(TEMP_CO_ACTUAL) as maximum
FROM TimeIntervals
GROUP BY interval_id
ORDER BY interval_start;", [$date, $date, $sensor, $date]);

        if (blank($data)) {
            return [];
        }

        $ext_dataset = array_column($data, 'av_temp');
        $ord_dataset = array_column($data, 'av_temp_ord');
        $act_dataset = array_column($data, 'av_temp_act');
        $min_dataset = array_column($data, 'minimum');
        $max_dataset = array_column($data, 'maximum');
        $labels = collect(array_column($data, 'interval_start'))
            ->map(fn($el) => Carbon::make($el)->format('y-m-d H:i'));

        foreach ($labels as $key => $label) {
            $min_points[] = ['x' => $label, 'y' => $min_dataset[$key]];
            $max_points[] = ['x' => $label, 'y' => $max_dataset[$key]];
        }

        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => 'Temp. zewn.',
                    'pointRadius' => 0,
                    'data' => $ext_dataset,
                    'backgroundColor' => 'rgba(255, 99, 132, 0.2)',
                    'borderColor' => 'green',
                    'borderWidth' => 2,
                    'yAxisID' => 'y-axis-2',
                ],
                [
                    'label' => 'Temp. zadana',
                    'type' => 'line',
                    'fill' => 'origin',
                    'pointRadius' => 0,
                    'data' => $ord_dataset,
                    'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                    'borderColor' => 'rgba(54, 162, 235, 1)',
                    'borderWidth' => 1,
                    'yAxisID' => 'y',
                ],
                [
                    'label' => 'Temp. aktualna Min',
                    'data' => $min_points,
                    'fill' => '+1',
                    'backgroundColor' => 'rgba(75, 192, 192, 0.2)',
                    'borderColor' => 'rgba(75, 192, 192, 1)',
                    'borderWidth' => 1,
                    'yAxisID' => 'y',
                    'hidden' => true,
                ],
                [
                    'label' => 'Temp. aktualna',
                    'data' => $act_dataset,
                    'backgroundColor' => 'rgba(255, 206, 86, 0.2)',
                    'borderColor' => 'rgba(255, 206, 86, 1)',
                    'borderWidth' => 1,
                    'yAxisID' => 'y',
                ],
                [
                    'label' => 'Temp. aktualna Max',
                    'data' => $max_points,
                    'backgroundColor' => 'rgba(153, 102, 255, 0.2)',
                    'borderColor' => 'rgba(153, 102, 255, 1)',
                    'borderWidth' => 1,
                    'fill' => '-1',
                    'yAxisID' => 'y',
                    'hidden' => true,
                ]
            ],
        ];
    }

    /**
     * @return array|null
     */
    public function getOptions(): array|RawJs
    {
        return $this->getOptionsJson();
    }

    public function getOptionsJson(): string|RawJs
    {
        $content = str_replace('"', "'", file_get_contents(__DIR__ . '/profit-loss.chart.json'));
        return RawJs::make(<<<JS
$content
JS
        );
    }

    protected function getType(): string
    {
        return 'line';
    }

    public function getDescription(): string|Htmlable|null
    {
        return '';
    }

    protected function getFilters(): ?array
    {
        return [true];
    }
}
