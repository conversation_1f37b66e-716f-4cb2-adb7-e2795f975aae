<?php

namespace App\Filament\App\Pages;

use App\Enums\Langs;
use App\Helpers\UserHelper;
use App\Models\ProfileData;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;

class EditProfile extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.app.pages.edit-profile';

    protected static bool $shouldRegisterNavigation = false;

    use InteractsWithForms;

    public ?array $profileData = [];
    public ?array $passwordData = [];

    public ?array $additionalData = [];

    public $profile;

    public function mount(): void
    {
        $this->fillForms();
    }

    protected function getForms(): array
    {
        return [
            'editProfileForm',
            'editPasswordForm',
            'editProfileAdditionalDataForm'
        ];
    }

    public function editProfileForm(Form $form): Form
    {
        return
            $form->schema([
                Section::make('Dane podstawowe')
                    ->schema([
                        TextInput::make('name')
                            ->label('Nazwa konta')
                            ->required(),
                        TextInput::make('email')
                            ->label('Email')
                            ->email()
                            ->required()
                            ->unique(ignoreRecord: true),
                    ])->columns(2),
            ])
                ->model($this->getUser())
                ->statePath('profileData');
    }

    public function editPasswordForm(Form $form): Form
    {
        return $form->schema([
            Section::make('Zmień hasło')
                ->description('Wprowadź nowe hasło jeżeli chcesz je zmienić')
                ->schema([
                    TextInput::make('password')
                        ->label(__('app._.password'))
                        ->password()
                        ->confirmed()
                        ->maxLength(255),
                    TextInput::make('password_confirmation')
                        ->label(__('app._.password_confirmation'))
                        ->password()
                        ->requiredWith('password')
                        ->maxLength(255),
                ]),
        ])
            ->model($this->getUser())
            ->statePath('passwordData');
    }

    public function editProfileAdditionalDataForm(Form $form): Form
    {
        return $form->schema([
            Section::make(__('app.users.profile.section_heading'))
                ->description(__('app.users.profile.section_description'))
                ->columns(2)
                ->schema([
                    TextInput::make('name')
                        ->label(__('app.users.profile.name'))
                        ->maxLength(255),
                    TextInput::make('surname')
                        ->label(__('app.users.profile.surname'))
                        ->maxLength(255),
                    TextInput::make('adress')
                        ->label(__('app.users.profile.address'))
                        ->maxLength(255),
                    TextInput::make('number')
                        ->label(__('app.users.profile.number'))
                        ->maxLength(255)
                        ->numeric(),
//                    Select::make('lang')
//                        ->label(__('app.users.profile.lang'))
//                        ->options(Langs::toArray())
//                        ->required()
//                        ->default('pl')
                ])
        ])->statePath('additionalData');
    }

    protected function getUpdateProfileFormActions(): array
    {
        return [
            Action::make('updateProfileAction')
                ->label(__('filament-panels::pages/auth/edit-profile.form.actions.save.label'))
                ->submit('editProfileForm'),
        ];
    }

    protected function getUpdatePasswordFormActions(): array
    {
        return [
            Action::make('updatePasswordAction')
                ->label(__('filament-panels::pages/auth/edit-profile.form.actions.save.label'))
                ->submit('editPasswordForm'),
        ];
    }

    protected function getUpdateProfileAdditionalDataActions(): array
    {
        return [
            Action::make('updateProfileAdditionalDataAction')
                ->label(__('filament-panels::pages/auth/edit-profile.form.actions.save.label'))
                ->submit('editProfileAdditionalDataForm'),
        ];
    }


    protected function getUser(): Model
    {
        $user = auth()->user();
        $this->profile = $user;
        return $user;
    }

    protected function getUserData(): ?Model
    {
        return auth()->user()?->profile()->first();
    }

    protected function fillForms(): void
    {
        $data = $this->getUser()->attributesToArray();
        $this->editProfileForm->fill($data);
        $this->editPasswordForm->fill();
        $additionalData = $this->getUserData();
        $this->editProfileAdditionalDataForm->fill($additionalData?->attributesToArray() ?? []);
    }

    /**
     * Called from szablon xDd
     * @return void
     */
    public function updateAdditionalData(): void
    {
        $data = $this->editProfileAdditionalDataForm->getState();
        $data['lang'] = 'pl';
        if (UserHelper::editUserAdditionalData(auth()->user(), $data)) {
            Notification::make()
                ->title('Zapisano pomyślnie')
                ->success()
                ->send();
        } else {
            Notification::make()
                ->title('Problem z zapisem danych')
                ->danger()
                ->send();
        }
    }

    public function updateProfile(): void
    {
        $data = $this->editProfileForm->getState();
        $model = auth()->user();
        if ($model->update($data)) {
            Notification::make()
                ->title('Zapisano pomyślnie')
                ->success()
                ->send();
        } else {
            Notification::make()
                ->title('Problem z zapisem danych')
                ->danger()
                ->send();
        }
    }

    public function updatePassword()
    {
        $data = $this->editPasswordForm->getState();
        if ($data['password'] === $data['password_confirmation']) {
            if (auth()->user()->update([
                'password' => Hash::make($data['password'])
            ])) {
                Notification::make()
                    ->title('Zapisano pomyślnie')
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('Problem z zapisem danych')
                    ->danger()
                    ->send();
            }
        } else {
            Notification::make()
                ->title('Hasła nie są identyczne !')
                ->danger()
                ->send();
        }
    }
}
