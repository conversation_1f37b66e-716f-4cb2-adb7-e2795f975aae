<?php

namespace App\Filament\App\Resources\JobTaskTemplateResource\Pages;

use App\Filament\App\Resources\JobTaskTemplateResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListJobTaskTemplates extends ListRecords
{
    protected static string $resource = JobTaskTemplateResource::class;

    public static function getNavigationLabel(): string
    {
        return 'Szablony'; // TODO: Change the autogenerated stub
    }

    /**
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return 'Szablony zleceń';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
