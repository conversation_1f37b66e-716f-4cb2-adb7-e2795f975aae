<?php

namespace App\Filament\App\Resources\JobTaskTemplateResource\Pages;

use App\Filament\App\Resources\JobTaskTemplateResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Js;

class EditJobTaskTemplate extends EditRecord
{
    protected static string $resource = JobTaskTemplateResource::class;

    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return 'Edycja szablonu zlecenia';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function getCancelFormAction(): Action
    {
        return Action::make('cancel')
            ->label(__('filament-panels::resources/pages/edit-record.form.actions.cancel.label'))
            ->alpineClickHandler('(window.location.href = ' . Js::from(static::getResource()::getUrl()) . ')')
            ->color('gray');
    }
}
