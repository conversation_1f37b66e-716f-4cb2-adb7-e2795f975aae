<?php

namespace App\Filament\App\Resources\JobTaskTemplateResource\Pages;

use App\Filament\App\Resources\JobTaskTemplateResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateJobTaskTemplate extends CreateRecord
{
    protected static string $resource = JobTaskTemplateResource::class;

    /**
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return 'Utwórz szablon zlecenia';
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['installation'] = tenant()->id;
        return $data; // TODO: Change the autogenerated stub
    }
}
