<?php

namespace App\Filament\App\Resources\WarehouseDocResource\RelationManagers;

use App\Enums\DocumentTypes;
use App\Enums\WarehouseItemTypes;
use App\Filament\App\Resources\ManufacturerResource;
use App\Filament\App\Resources\ProductsResource;
use App\Models\Manufacturer;
use App\Models\Products;
use App\Models\WarehouseItem;
use App\Models\WarehouseLog;
use App\Repositories\DrugsRepository;
use App\Repositories\ManufacturersRepository;
use App\Repositories\ProductsRepository;
use App\Repositories\WarehouseDocsRepository;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;

class LogRelationManager extends RelationManager
{
    protected static string $relationship = 'log';

    public function isReadOnly(): bool
    {
        return false;
    }

    public $maxAmount = 1;

    public function form(Form $form): Form
    {

        return match ($this->getOwnerRecord()->type) {
            DocumentTypes::PZ,
            DocumentTypes::PW,
            DocumentTypes::ZW, => $this->receiptForm($form),
            DocumentTypes::WZ,
            DocumentTypes::RW,
            DocumentTypes::MW => $this->issueForm($form),
        };
    }

    #[Computed]
    public function getMaxAmount()
    {
        return $this->maxAmount;
    }

    protected function receiptForm(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('product_id')
                            ->options(Products::type(WarehouseItemTypes::PRODUCT)->pluck('name', 'id'))
                            ->label(__('app.warehouse_log.create.product_id'))
                            ->searchable()
                            ->getSearchResultsUsing(fn(string $search) => $this->searchReceiptFormProducts($search))
                            ->createOptionForm(ProductsResource::getCreateModalFormSchema())
                            ->suffixAction(function () {
                                return DrugsRepository::getImportAction();
                            })
                            ->createOptionUsing(function (array $data) {
                                return ProductsRepository::createFromModalForm($data)?->id;
                            })
                            ->columnSpanFull()
                            ->native(false)
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                if (empty($state)) {
                                    $set('price', 0);
                                    $set('series_no', '');
                                    $set('exp_date', '');
                                    $set('amount', 1);
                                    $set('product_id', '');
                                    $this->maxAmount = 1;
                                    return;
                                }
                                $product = auth()->user()?->getTenant()?->products()->find($state);
                                $set('price', $product?->price_per_unit ?? 0);
                            })
                            ->preload()
                            ->required(),
                        Forms\Components\TextInput::make('amount')
                            ->label(__('app.warehouse_log.create.amount'))
                            ->numeric()
                            ->default(1)
                            ->required(),
                        Forms\Components\TextInput::make('price')
                            ->label(__('app.warehouse_log.create.price'))
                            ->numeric()->step(0.01)
                            ->default(0)
                            ->formatStateUsing(fn($state) => empty($state) ? 0 : $state)
                            ->required(),
                        Forms\Components\TextInput::make('series_no')
                            ->label(__('app.warehouse_log.create.series_no')),
                        Forms\Components\DatePicker::make('exp_date')
                            ->format('Y-m-d')
                            ->displayFormat('Y-m-d')
                            ->native(false)
                            ->label(__('app.warehouse_log.create.exp_date')),
                    ]),
            ]);
    }

    public function searchReceiptFormProducts(string $search)
    {
        return Products::where('name', 'LIKE', '%' . $search . '%')
            ->orWhere('gtin', 'LIKE', '%' . $search . '%')->pluck('name', 'id')->toArray();
    }


    public function searchIssueFormProducts(string $search, $current)
    {
        $products = $this->searchReceiptFormProducts($search);
        if (empty($products)) {
            return [];
        }
        return $this->getWarehouseItemsForOption($current, array_keys($products));
    }

    protected function getCreateProductFormSchema()
    {
        return [
            Forms\Components\Grid::make(2)
                ->schema([
                    Forms\Components\Select::make('manufacturer_id')
                        ->label(__('app.products.create.manufacturer_id'))
                        ->options($this->getManufacturersForSelect())
                        ->createOptionForm(ManufacturerResource::GetCreateModalForm())
                        ->createOptionUsing(function ($data, Forms\ComponentContainer $form, self $livewire) {
                            $this->createManufacturer($data);
                            $livewire
                                ->getMountedFormComponentAction()
                                ?->getComponent()
                                ->options($this->getManufacturersForSelect())
                                ->render();
                            return true;
                        }),
                    Forms\Components\TextInput::make('name')
                        ->label(__('app._.name')),
                    Forms\Components\TextInput::make('gtin')
                        ->label(__('app.products.create.gtin')),
                ])
        ];
    }

    public function createManufacturer($data)
    {
        return ManufacturersRepository::CreateFromModalForm($data);
    }

    public function getManufacturersForSelect(): \Illuminate\Support\Collection|array
    {
        return Manufacturer::orderBy('name')->get()->pluck('name', 'id');
    }


    protected function issueForm(Form $form): Form
    {

        if ($form->getOperation() === 'edit') {
            $product = $this->getWarehouseItemOption(
                $form->getRecord()->source_doc_id,
                $form->getRecord(),
                'maxamount'
            );
            $this->maxAmount = $product->total ?? $form->getRecord()->amount;
        }

        return $form
            ->columns(1)
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Hidden::make('max_value')
                            ->default($this->maxAmount),
                        Forms\Components\Select::make('source_doc_id')
                            ->label(__('app.warehouse_log.create.product_id'))
                            ->options($this->getWarehouseItemsForOption($form->getRecord()))
                            ->getSearchResultsUsing(
                                fn(string $search) => $this->searchIssueFormProducts($search, $form->getRecord())
                            )
                            ->searchable()
                            ->columnSpanFull()
                            ->native(false)
                            ->live()
                            ->afterStateUpdated(function ($state, callable $set, callable $get, ?Model $record) {
                                if (empty($state)) {
                                    $set('price', 0);
                                    $set('series_no', '');
                                    $set('exp_date', '');
                                    $set('amount', 1);
                                    $set('source_doc_id', '');
                                    $this->maxAmount = 1;
                                    return;
                                }
                                $product = $this->getWarehouseItemOption($state, $record, 'select');
                                $this->maxAmount = $product->total;
                                $set('price', $product->price);
                                $set('series_no', $product->series_no);
                                $set('exp_date', $product->exp_date);
                                $set('source_doc_id', $product->transaction_id);
                                $set('max_value', $this->maxAmount);
                            })
                            ->preload(),
                        Forms\Components\Grid::make(3)->schema([
                            Forms\Components\TextInput::make('amount')
                                ->label(__('app.warehouse_log.create.amount'))
                                ->numeric()
                                ->maxValue(fn(Forms\Get $get) => $get('max_value'))
                                ->minValue(1)
                                ->hint(function (Forms\Get $get) {
                                    return 'Maksymalnie: ' . $get('max_value');
                                })
                                ->default(1)
                                ->live(onBlur: true)
                                ->required(),
                            Forms\Components\TextInput::make('price')
                                ->label(__('app.warehouse_log.create.price'))
                                ->numeric()->step(0.01)
                                ->required()
                                ->readOnly(),
                            Forms\Components\DatePicker::make('exp_date')
                                ->format('Y-m-d')
                                ->displayFormat('Y-m-d')
                                ->native(false)
                                ->label(__('app.warehouse_log.create.exp_date'))
                                ->readOnly(),
                        ]),
                        Forms\Components\TextInput::make('series_no')
                            ->label(__('app.warehouse_log.create.series_no'))
                            ->readOnly(),
                        Forms\Components\TextInput::make('source_doc_id')
                            ->label(__('app.warehouse_log.create.source_doc_id'))
                            ->readOnly(),
                    ]),
            ]);
    }

    protected function getWarehouseItemsForOption($current, $scope = [])
    {
        $options = [];

        /**
         * @var Collection $items
         */

        if ($current === null) {
            $qry = $this->getOwnerRecord()
                ->warehouse
                ->items()->with(['product'])
                ->where('reserved', false)
                ->groupBy('transaction_id')
                ->select(DB::raw('*, SUM(amount) as total'));
            if ([] !== $scope) {
                $qry->whereIn('product_id', $scope);
            }
            $items = $qry->get();
        } else {
            $qry = $this->getOwnerRecord()
                ->warehouse
                ->items()->with(['product'])
                ->where(function ($query) use ($current) {
                    $query->orWhere('reserved', '=', false,)
                        ->orWhere('reserved_by', '=', $current->transaction_id,);
                })
                ->groupBy('transaction_id')
                ->select(DB::raw('*, SUM(amount) as total'));
            if ([] !== $scope) {
                $qry->whereIn('product_id', $scope);
            }
            $items = $qry->get();
        }

        $items->each(function (WarehouseItem $item, $key) use (&$options) {
            $options[$item->transaction_id] = $item->product->name . ', cena: ' . $item->price . ', seria: ' . $item->series_no . ', stan: ' . $item->total;
        });
        return $options;
    }

    protected function getWarehouseItemOption($item_transaction_id, ?WarehouseLog $record, $context)
    {
        if ($context === 'maxamount') {
            return $this->getOwnerRecord()
                ->warehouse
                ->items()->with(['product'])
                ->groupBy('transaction_id')
                ->where('reserved', false)
                ->select(DB::raw('*, SUM(amount) as total'))
                ->where('transaction_id', $item_transaction_id)
                ->first();
        }

        if ($record?->source_doc_id !== $item_transaction_id) {
            $item = $this->getOwnerRecord()
                ->warehouse
                ->items()->with(['product'])
                ->groupBy('transaction_id')
                ->where('reserved', false)
                ->select(DB::raw('*, SUM(amount) as total'))
                ->where('transaction_id', $item_transaction_id)
                ->first();
        } else {
            $item = $this->getOwnerRecord()
                ->warehouse
                ->items()->with(['product'])
                ->groupBy('transaction_id')
                ->where(function ($query) use ($record) {
                    $query->orWhere('reserved', '=', false)
                        ->orWhere('reserved_by', '=', $record->transaction_id);
                })
                ->select(DB::raw('*, SUM(amount) as total'))
                ->where('transaction_id', $item_transaction_id)
                ->first();
        }

        return $item;
    }

    protected function IssueMMForm(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('product_id')
                            ->options(Products::all()->pluck('name', 'id'))
                            ->searchable()
                            ->columnSpanFull()
                            ->native(false)
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                $product = auth()->user()->getTenant()->products()->find($state);
                                $set('price', $product->price_per_unit);
                            })
                            ->preload(),
                        Forms\Components\TextInput::make('amount')
                            ->label('Ilość')
                            ->numeric()
                            ->default(1)
                            ->required(),
                        Forms\Components\TextInput::make('price')
                            ->label('Cena jednostkowa')
                            ->numeric()->step(0.01)
                            ->required(),
                        Forms\Components\TextInput::make('series_no')
                            ->label('Numer serii'),
                        Forms\Components\DatePicker::make('exp_date')
                            ->format('Y-m-d')
                            ->displayFormat('Y-m-d')
                            ->native(false)
                            ->label('Data ważności'),
                    ]),
            ]);
    }

    #[On('refreshItems')]
    public function refresh(): void
    {
        $this->render();
    }


    public function table(Table $table): Table
    {

        return match (auth()->user()?->isTenantAdmin()) {
            true => $this->adminTable($table),
            false => $this->employeeTable($table),
            default => $table
        };
    }

    public function employeeTable(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->heading('Produkty')
            ->columns([
                Tables\Columns\Layout\Split::make([
                    Tables\Columns\TextColumn::make('product.name')
                        ->label(__('app._.name')),
                    Tables\Columns\TextColumn::make('series_no')
                        ->label(__('app.warehouse_log.list.series_no')),
                    Tables\Columns\TextColumn::make('gtin')
                        ->label(__('app._.gtin')),
                    Tables\Columns\TextColumn::make('transaction_id')
                        ->label(__('app.warehouse_log.list.transaction_id')),
                    Tables\Columns\TextColumn::make('amount_units')
                        ->state(function (WarehouseLog $record) {
                            return $record->amount . ' ' . $record->unit;
                        })
                        ->label(__('app.warehouse_log.list.amount_units')),
                ])->from('md'),
            ])
            ->filters([
                //
            ])
            ->headerActions(
                $this->getTableHeaderActions()
            )
            ->actions([
                Tables\Actions\EditAction::make()
                    ->hidden(function () {
                        return $this->getOwnerRecord()->accepted;
                    })
                    ->using(
                        fn($data, Model $record, Table $table) => $this->updateActionProcess($data, $record, $table)
                    )
                    ->closeModalByClickingAway(false),

                Tables\Actions\DeleteAction::make()
                    ->hidden(function () {
                        return $this->getOwnerRecord()->accepted;
                    })
                    ->using(fn(Model $record) => $this->removeLogRecord($record))
                    ->after(fn() => $this->dispatchDeleteRecord()),
            ])
            ->bulkActions($this->getTableBulkActions());
    }


    public function adminTable(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->heading('Produkty')
            ->columns([
                Tables\Columns\TextColumn::make('product.name')
                    ->label(__('app._.name')),
                Tables\Columns\TextColumn::make('series_no')
                    ->label(__('app.warehouse_log.list.series_no')),
                Tables\Columns\TextColumn::make('amount_units')
                    ->state(function (WarehouseLog $record) {
                        return $record->amount . ' ' . $record->unit;
                    })
                    ->label(__('app.warehouse_log.list.amount_units')),
                Tables\Columns\TextColumn::make('gtin')
                    ->label(__('app._.gtin')),
                Tables\Columns\TextColumn::make('transaction_id')
                    ->label(__('app.warehouse_log.list.transaction_id')),
            ])
            ->filters([
                //
            ])
            ->headerActions(
                $this->getTableHeaderActions()
            )
            ->actions([
                Tables\Actions\EditAction::make()
                    ->hidden(
                        fn() => !WarehouseDocsRepository::canDocBeEdited($this->getOwnerRecord())
                    )
                    ->using(
                        fn($data, Model $record, Table $table) => $this->updateActionProcess($data, $record, $table)
                    )
                    ->closeModalByClickingAway(false),

                Tables\Actions\DeleteAction::make()
                    ->hidden(function () {
                        return !WarehouseDocsRepository::canDocBeEdited($this->getOwnerRecord());
                    })
                    ->using(fn(Model $record) => $this->removeLogRecord($record))
                    ->after(fn() => $this->dispatchDeleteRecord()),
            ])
            ->bulkActions($this->getTableBulkActions());
    }


    public function dispatchDeleteRecord(): void
    {
        if ($this->table->getRecords()->count() === 0) {
            $this->dispatch('itemRemoved');
        }
    }


    public function dispatchAddedRecord(): void
    {
        if ($this->table->getRecords()->count() > 0) {
            $this->dispatch('itemAdded');
        }
    }

    protected function getTableBulkActions(): array
    {
        if ($this->getOwnerRecord()->accepted) {
            return [];
        }

        return [];
    }

    public function getTableHeaderActions(): array
    {
        if (!WarehouseDocsRepository::canDocBeEdited($this->getOwnerRecord())) {
            return [];
        }
        $action = Tables\Actions\CreateAction::make()
            ->label('Dodaj produkt')
            ->modalHeading("Dodaj produkt");
        $action->using(function (array $data, Table $table): Model {
            return $this->createDocItem($data);
        })
            ->closeModalByClickingAway(false)
            ->after(fn() => $this->dispatchAddedRecord())
            ->createAnother(false);
        return [$action];
    }


    public function createDocItem(array $data): WarehouseLog
    {
        return WarehouseDocsRepository::CreateWarehouseDocItemModel($data, $this->getOwnerRecord());
    }


    public function updateActionProcess($data, WarehouseLog|Model $record, Table $table): WarehouseLog
    {
        return WarehouseDocsRepository::UpdateWarehouseDocItem($data, $record);
    }


    public function removeLogRecord(WarehouseLog|Model $record): ?bool
    {
        return WarehouseDocsRepository::RemoveWarehouseDocItemModel($record);
    }
}
