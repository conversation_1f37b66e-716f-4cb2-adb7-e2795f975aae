<?php

namespace App\Filament\App\Resources\WarehouseDocResource\Actions;

use App\Models\WarehouseDoc;
use Filament\Actions\Action;
use Filament\Actions\Concerns\CanCustomizeProcess;
use Filament\Forms\Components\DateTimePicker;
use Illuminate\Database\Eloquent\Model;
use Livewire\Component;

class AcceptAction extends Action
{
    use CanCustomizeProcess;

    protected function setUp(): void
    {
        parent::setUp();
        $this->button();
        $this->color(fn(WarehouseDoc $record) => $record->accepted ? 'success' : 'warning');
        $this->label(function (WarehouseDoc $record) {
            return $record->accepted ? 'Zaakceptowany' : 'Zaakceptuj';
        });
        $this->form([
            DateTimePicker::make('items_issue_date')
                ->label('Data wydania / przyjęcia')
                ->native(false)
                ->seconds(false)
                ->minDate(
                    fn(WarehouseDoc $record) => $record->created_at->setTimezone(
                        new \DateTimeZone('Europe/Warsaw')
                    )
                )
                ->default(now())
                ->required()
                ->timezone('Europe/Warsaw')
                ->displayFormat('Y-m-d H:i')
        ]);
        $this->requiresConfirmation();
        $this->modalHeading('Akceptacja');
        $this->modalDescription('Chcesz zaakceptować dokument? Akceptacja jest nieodwracalna.');
        $this->modalSubmitActionLabel('Tak, zaakceptuj');
        $this->closeModalByClickingAway(false);
        $this->hidden(fn (Model $record) => $record->isCancelled() || $record->accepted);
        $this->disabled(fn (Model $record) => ! ($record->log()->count() > 0));
        $this->action(
            function ($data, Model $record, Component $livewire) {
                $nrecord = $livewire->acceptDoc($data, $record);
                if (null === $nrecord) {
                    $this->halt();
                }
                $this->record = $nrecord;
                $this->fillForm($nrecord->attributesToArray());
            }
        )
            ->after(function (Component $livewire) {
                $livewire->dispatch('refreshItems');
            });
    }

    public static function getDefaultName(): ?string
    {
        return 'Zaakceptuj';
    }
}
