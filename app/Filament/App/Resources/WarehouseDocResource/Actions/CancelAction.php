<?php

namespace App\Filament\App\Resources\WarehouseDocResource\Actions;

use App\Enums\DocumentTypes;
use Filament\Actions\Action;
use Illuminate\Database\Eloquent\Model;
use Livewire\Component;

class CancelAction extends Action
{
    protected function setUp(): void
    {
        parent::setUp();
        $this->label('Anuluj dokument');
        $this->requiresConfirmation();
        $this->modalDescription(fn(Model $record): string => match ($record->type) {
            DocumentTypes::PZ => 'Przyjęte produkty zostaną usunięte z magazynu',
            default => '<PERSON><PERSON> chcesz anulować ten dokument? ta czynność jest nieodwracalna!',
        });
        $this->icon('heroicon-o-x-circle');
        $this->color('warning');
        $this->after(function (Component $livewire) {
            $livewire->dispatch('refreshItems');
        });
    }

    public static function getDefaultName(): ?string
    {
        return 'cancel';
    }
}
