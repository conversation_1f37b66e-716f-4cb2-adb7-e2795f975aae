<?php

namespace App\Filament\App\Resources\WarehouseDocResource\Pages;

use App\Enums\DocumentTypes;
use App\Filament\App\Resources\PartnerResource;
use App\Filament\App\Resources\WarehouseDocResource;
use App\Filament\Filters\WHSelectFilter;
use App\Models\Partner;
use App\Models\Warehouse;
use App\Models\WarehouseDoc;
use App\Repositories\DocumentSeriesRepository;
use App\Repositories\EmployeesRepository;
use App\Repositories\PartnersRepository;
use App\Repositories\WarehouseDocsRepository;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use Livewire\Attributes\On;

class EditWarehouseDoc extends EditRecord
{
    protected static string $resource = WarehouseDocResource::class;
    public $installation;

    public $originalWH;


    #[On('warehouse')]
    public function showCheckbox()
    {
        $this->form->getComponent('whconfirm', true)->hidden(false);
    }


    public function mount(int|string $record): void
    {
        $this->record = WarehouseDoc::where('transaction_id', $record)->where('installation', INSTALLATION)->first();
        $this->installation = INSTALLATION;
        $this->originalWH = $this->record->werehouse_id;
        $this->authorizeAccess();

        $this->fillForm();
    }

    public function getHeading(): string|Htmlable
    {
        return 'Edytuj dokument ' . $this->record->type->name . ' nr: ' . $this->record->full_doc_number;
    }


    public function form(Form $form): Form
    {
        return $form
            ->columns(3)
            ->schema([
                Forms\Components\Grid::make(5)
                    ->schema([
                        Forms\Components\Select::make('document_series_id')
                            ->label('Seria i numer dokumentu')
                            ->required()
                            ->preload()
                            ->options(function (WarehouseDoc $doc) {
                                return DocumentSeriesRepository::getSeriesForDocType($doc->type, true)
                                    ->pluck('name', 'id');
                            })
                            ->disabled(fn(WarehouseDoc $doc) => filled($doc->doc_number))
                            ->selectablePlaceholder(false),
                        Forms\Components\Toggle::make("autogenerate")
                            ->inline(false)
                            ->live()
                            ->disabled(fn(WarehouseDoc $doc) => filled($doc->doc_number))
                            ->helperText(
                                fn($state) => match ($state) {
                                    true => 'Numer zostanie wygenerowany podczas zapisu',
                                    default => 'Numer zostanie wygenerowany podczas akceptacji'
                                }
                            )
                            ->default(true)
                            ->label(__('app.warehouse_docs.create.autogenerate')),
                        Forms\Components\DatePicker::make('doc_date')
                            ->default(now())
                            ->required()
                            ->disabled(fn(WarehouseDoc $doc) => filled($doc->doc_number))
                            ->label(__('app.warehouse_docs.create.doc_date'))
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->format('Y-m-d'),
                        Forms\Components\TextInput::make('transaction_id')
                            ->disabled()
                            ->label(__('app.warehouse_docs.create.transaction_id')),
                        Forms\Components\TextInput::make('related_doc')
                            ->label(__('app.warehouse_docs._.related_doc'))
                            ->hidden(fn(WarehouseDoc $record) => match ($record->type) {
                                default => false,
                                DocumentTypes::MW, DocumentTypes::MP => true
                            }),
                    ]),
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('warehouse_id')
                            ->options(WHSelectFilter::getOptionsForLoggedInEmployee())
                            ->live()
                            ->afterStateUpdated(function (Model $record, $state) {
                                $state = WHSelectFilter::deconstructCompoundIndex($state)['id'];
                                $this->toggleWarehouseChangeWarning($this->form, $record, $state);
                            })
                            ->dehydrateStateUsing(fn ($state) => WHSelectFilter::deconstructCompoundIndex($state)['id'])
                            ->label(__('app.warehouse_docs.create.warehouse'))
                            ->required(),
                        Forms\Components\Select::make('target_warehouse_id')
                            ->options(
                                fn (Forms\Get $get) => WHSelectFilter::getOptionsForLoggedInEmployee(true, $get('warehouse_id'))
                            )
//                            ->options(Warehouse::all()->pluck('name', 'id'))
                            ->label(__('app.warehouse_docs.create.target_warehouse'))
                            ->dehydrateStateUsing(fn ($state) => WHSelectFilter::deconstructCompoundIndex($state)['id'])
                            ->disabled(
                                fn(Forms\Get $get) => (int)($get('type') ?? 0) !== DocumentTypes::MW->value
                            )
                            ->required(
                                fn(Forms\Get $get) => (int)($get('type') ?? 0) === DocumentTypes::MW->value
                            ),

                    ]),
                Forms\Components\Grid::make(3)
                    ->columnSpanFull()
                    ->schema([
                        Forms\Components\Select::make('signatory_id')
                            ->options(EmployeesRepository::getEmployees()->pluck('email', 'id'))
                            ->label(__('app.warehouse_docs.create.signatory'))
                            ->required(
                                fn(Forms\Get $get) => (int)($get('type') ?? 0) === DocumentTypes::MW->value
                            )
                            ->hint('Wybierz, jeżeli to pracownik'),
                        Forms\Components\TextInput::make('signatory_name')
                            ->label(__('app.warehouse_docs.create.signatory_name'))
                            ->hint('Sygnatariusz')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('signatory_last_name')
                            ->label(__('app.warehouse_docs.create.signatory_last_name'))
                            ->hint('Sygnatariusz')
                            ->maxLength(255),
                    ]),
                Forms\Components\Grid::make(1)
                    ->columnSpan(1)
                    ->schema([
                        Forms\Components\Select::make('partner_id')
                            ->options(PartnersRepository::getPartnerForSelect())
                            ->label(__('app.warehouse_docs.create.partner'))
                            ->reactive()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                $set('partner_data', Partner::find($state)->getAddressText() ?? 'brak');
                            })
                            ->createOptionForm(PartnerResource::getCreatePartnerModalForm())
                            ->hidden(function (Forms\Get $get) {
                                return $this->isPartnerInputHidden((int)$get('type'));
                            }),
                        Forms\Components\Textarea::make('partner_data')
                            ->rows(5)
                            ->label(__('app.warehouse_docs.create.recipient_data'))
                            ->hidden(function (Forms\Get $get) {
                                return $this->isPartnerInputHidden((int)$get('type'));
                            })
                            ->maxLength(65535),
                    ]),
                Forms\Components\Textarea::make('notes')
                    ->label(__('app.warehouse_docs.create.notes'))
                    ->maxLength(255),

                Forms\Components\Checkbox::make('confirmation')
                    ->name('confirmation')
                    ->columnSpan(2)
                    ->dehydrated(false)
                    ->label('Potwierdzam zmianę magazynu')
                    ->visible(false)
                    ->columnSpanFull()
                    ->key('whconfirm'),
            ]);
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['warehouse_id'] = WHSelectFilter::buildCompoundIndex(Warehouse::find($data['warehouse_id']));
        if ($data['target_warehouse_id'] !== null) {
            $data['target_warehouse_id'] = WHSelectFilter::buildCompoundIndex(
                Warehouse::find($data['target_warehouse_id'])
            );
        }
        return $data;
    }


    public function checkItemsLength(Model $record, $state)
    {
        if ((int)$record->getRawOriginal('warehouse_id') === (int)$state) {
            return 0;
        }
        return $record->log()->count();
    }

    public function toggleWarehouseChangeWarning(Form $form, Model $record, $state)
    {
        if (in_array($this->getRecord()->type->value, [
            DocumentTypes::PZ->value,
            DocumentTypes::PW->value,
            DocumentTypes::ZW->value
        ], true)) {
            return false;
        }

        if ($this->checkItemsLength($record, $state) > 0) {
            $chk = $this->form->getComponent('whconfirm', true);
            $chk?->required(true)
                ->visible(true)
                ->helperText(
                    new HtmlString('<strong>Dokument zawiera pozycje. Zmiana magazynu usunie te pozycje</strong>')
                );
            Notification::make('confirmChange')
                ->danger()
                ->title('Potwierdź zmianę magazynu')
                ->persistent()
                ->send();
        } else {
            $chk = $this->form->getComponent('whconfirm', true);
            $chk?->required(false)->visible(false)->helperText('');
        }
    }

    protected function isPartnerInputHidden(int $document_type): bool
    {
        return match ($document_type) {
            DocumentTypes::MW->value,
            DocumentTypes::MP->value,
            DocumentTypes::PW->value,
            DocumentTypes::ZW->value,
            DocumentTypes::RW->value => true,
            default => false
        };
    }

    protected function handleRecordUpdate(WarehouseDoc|Model $record, $data): Model
    {
        WarehouseDocsRepository::updateWarehouseDocModel($record, $data);
        return $record;
    }


    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->disabled(fn(WarehouseDoc $record) => !WarehouseDocsRepository::canDocBeRemoved($record))
                ->using(function (WarehouseDoc $record) {
                    if (false === $this->deleteActionProcess($record)) {
                        Notification::make()
                            ->title('Nie można skasować dokumentu!')
                            ->body($this->getDeleteError())
                            ->send();
                        $this->halt();
                    }
                    return true;
                })
        ];
    }

    protected function getFormActions(): array
    {
        return [
            $this->getSubmitFormAction(),
            Actions\Action::make('to-list')
                ->button()
                ->icon('heroicon-o-list-bullet')
                ->label('Lista')
                ->url(fn() => self::getResource()::getUrl('index')),
            Actions\Action::make('to-products')
                ->button()
                ->icon('heroicon-o-queue-list')
                ->label('Pozycje')
                ->url(fn() => self::getResource()::getUrl('add-item', ['record' => $this->record->transaction_id])),

        ];
    }

    public function deleteActionProcess(WarehouseDoc $record): bool
    {
        return WarehouseDocsRepository::deleteWarehouseDoc($record);
    }

    public function getDeleteError(): string
    {
        return WarehouseDocsRepository::$error;
    }

}
