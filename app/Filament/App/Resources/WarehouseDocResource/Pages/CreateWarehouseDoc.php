<?php

namespace App\Filament\App\Resources\WarehouseDocResource\Pages;

use App\Enums\DocumentGeneralTypes;
use App\Enums\DocumentTypes;
use App\Filament\App\Resources\PartnerResource;
use App\Filament\App\Resources\WarehouseDocResource;
use App\Filament\Filters\WHSelectFilter;
use App\Models\Partner;
use App\Models\User;
use App\Repositories\DocumentSeriesRepository;
use App\Repositories\EmployeesRepository;
use App\Repositories\PartnersRepository;
use App\Repositories\WarehouseDocsRepository;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class CreateWarehouseDoc extends CreateRecord
{
    protected static string $resource = WarehouseDocResource::class;

    protected static bool $canCreateAnother = false;


    public function form(Form $form): Form
    {
        return $form
            ->columns(2)
            ->schema([
                Forms\Components\Grid::make(5)
                    ->schema([
                        Forms\Components\Select::make('type')
                            ->label(__('app.warehouse_docs.create.type'))
                            ->options(
                                collect(
                                    DocumentTypes::getDocTypesOf(DocumentGeneralTypes::WAREHOUSE)
                                )->mapWithKeys(
                                    fn($type) => [$type->value => $type->label()]
                                )->except(15)
                            )
                            ->native(true)
                            ->preload()
                            ->live()
                            ->afterStateUpdated(
                                fn(Forms\Set $set, $state) => match (filled($state)) {
                                    false => null,
                                    default => $set(
                                        'document_series_id',
                                        DocumentSeriesRepository::getDefaultSeriesForDocType($state)?->id
                                    )
                                }
                            )
                            ->required(),
                        Forms\Components\Select::make('document_series_id')
                            ->label('Seria i numer dokumentu')
                            ->required()
                            ->preload()
                            ->options(function (Forms\Get $get) {
                                return match (blank($get('type'))) {
                                    true => [],
                                    default => DocumentSeriesRepository::getSeriesForDocType($get('type'), true)
                                        ->pluck('name', 'id')
                                };
                            })
                            ->selectablePlaceholder(false),
                        Forms\Components\Toggle::make("autogenerate")
                            ->inline(false)
                            ->live()
                            ->helperText(fn($state) => match ($state) {
                                true => 'Numer zostanie wygenerowany podczas tworzenia',
                                default => 'Numer zostanie wygenerowany podczas akceptacji'
                            })
                            ->default(true)
                            ->label(__('app.warehouse_docs.create.autogenerate')),
                        Forms\Components\DatePicker::make('doc_date')
                            ->default(now())
                            ->required()
                            ->label(__('app.warehouse_docs.create.doc_date'))
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->format('Y-m-d'),
                        Forms\Components\TextInput::make('related_doc')
                            ->label(__('app.warehouse_docs._.related_doc'))
                            ->hidden(function (Forms\Get $get) {
                                return $this->isRelatedHidden((int)$get('type'));
                            }),
                    ]),
                Forms\Components\Grid::make(2)
                    ->schema([

                        Forms\Components\Select::make('warehouse_id')
                            ->options(WHSelectFilter::getOptionsForLoggedInEmployee(true))
                            ->label(fn(Forms\Get $get) => $this->getLabel((int)($get('type') ?? 0), 'warehouse_id'))
                            ->live()
                            ->required(),
                        Forms\Components\Select::make('target_warehouse_id')
                            ->options(
                                fn(Forms\Get $get) => WHSelectFilter::getOptionsForLoggedInEmployee(
                                    true,
                                    $get('warehouse_id')
                                )
                            )
                            ->label(
                                fn(Forms\Get $get) => $this->getLabel((int)($get('type') ?? 0), 'target_warehouse_id')
                            )
                            ->disabled(
                                fn(Forms\Get $get) => (int)($get('type') ?? 0) !== DocumentTypes::MW->value
                            )
                            ->required(
                                fn(Forms\Get $get) => (int)($get('type') ?? 0) === DocumentTypes::MW->value
                            ),
                    ]),
                Forms\Components\Grid::make(3)
                    ->columnSpanFull()
                    ->schema([
                        Forms\Components\Select::make('signatory_id')
                            ->options(EmployeesRepository::getEmployees()->pluck('email', 'id'))
                            ->label(__('app.warehouse_docs.create.signatory'))
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                $profile = User::find($state)?->profile;
                                if (empty($profile)) {
                                    $set('signatory_name', '');
                                    $set('signatory_last_name', '');
                                    return;
                                }

                                $set('signatory_name', $profile->name);
                                $set('signatory_last_name', $profile->surname);
                            })
                            ->visible(function (Forms\Get $get) {
                                return match ((int)($get('type') ?? 0)) {
                                    DocumentTypes::PZ->value => false,
                                    default => true,
                                };
                            })
                            ->required(
                                fn(Forms\Get $get) => (int)($get('type') ?? 0) === DocumentTypes::MW->value
                            )
                            ->hint('Wybierz, jeżeli to pracownik'),
                        Forms\Components\TextInput::make('signatory_name')
                            ->label(__('app.warehouse_docs.create.signatory_name'))
                            ->hint('Sygnatariusz')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('signatory_last_name')
                            ->label(__('app.warehouse_docs.create.signatory_last_name'))
                            ->hint('Sygnatariusz')
                            ->maxLength(255),
                    ]),
                Forms\Components\Grid::make(1)
                    ->columns(1)
                    ->columnSpan(1)
                    ->schema([
                        Forms\Components\Select::make('partner_id')
                            ->options(PartnersRepository::getPartnerForSelect())
                            ->label(__('app.warehouse_docs.create.partner'))
                            ->live()
                            ->searchable()
                            ->required()
                            ->preload()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                $set('partner_data', Partner::find($state)->getAddressText() ?? 'brak');
                            })
                            ->createOptionForm(PartnerResource::getCreatePartnerModalForm())
                            ->createOptionUsing(function (array $data) {
                                return PartnersRepository::createFromModalForm($data)?->id;
                            })->hidden(function (Forms\Get $get) {
                                return $this->isPartnerInputHidden((int)$get('type'));
                            }),
                        Forms\Components\Textarea::make('partner_data')
                            ->rows(5)
                            ->label(__('app.warehouse_docs.create.recipient_data'))
                            ->maxLength(65535)
                            ->hidden(function (Forms\Get $get) {
                                return $this->isPartnerInputHidden((int)$get('type'));
                            }),

                    ]),
                Forms\Components\Textarea::make('notes')
                    ->label(__('app.warehouse_docs.create.notes'))
                    ->maxLength(255),
            ]);
    }

    protected function isRelatedHidden(int $type)
    {
        return match ($type) {
            default => false,
            DocumentTypes::MW->value, DocumentTypes::MP->value => true
        };
    }

    protected function isPartnerInputHidden(int $document_type): bool
    {
        return match ($document_type) {
            DocumentTypes::MW->value,
            DocumentTypes::MP->value,
            DocumentTypes::PW->value,
            DocumentTypes::ZW->value,
            DocumentTypes::RW->value => true,
            default => false
        };
    }

    protected function getLabel(int $document_type, string $field): string
    {
        return match ($field) {
            'warehouse_id' => match ($document_type) {
                DocumentTypes::MW->value,
                DocumentTypes::MP->value => __('app.warehouse_docs.create.source_warehouse'),
                default => __('app.warehouse_docs.create.warehouse')
            },
            'target_warehouse_id' => match ($document_type) {
                DocumentTypes::MW->value,
                DocumentTypes::MP->value => __('app.warehouse_docs.create.target_warehouse'),
                default => '--'
            },
            default => '--',
        };
    }


    protected function mutateFormDataBeforeCreate(array $data): array
    {
        return WarehouseDocsRepository::mutateFilamentDataOnDocCreate($data);
    }

    protected function handleRecordCreation(array $data): Model
    {
        $model = WarehouseDocsRepository::createWarehouseDocModel($data);
        if (null === $model) {
            Log::error('WarehouseDoc create error: ' . WarehouseDocsRepository::$error);
            Notification::make()
                ->danger()
                ->title('Processing error')
                ->send();
            $this->halt();
        }

        return $model;
    }


    protected function getRedirectUrl(): string
    {
        return self::getResource()::getUrl('add-item', ['record' => $this->getRecord()->transaction_id]);
    }
}
