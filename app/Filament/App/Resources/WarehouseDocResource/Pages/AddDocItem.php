<?php

namespace App\Filament\App\Resources\WarehouseDocResource\Pages;

use App\Enums\DocumentTypes;
use App\Filament\Actions\PrintAction;
use App\Filament\App\Resources\WarehouseDocResource;
use App\Models\WarehouseDoc;
use App\Repositories\WarehouseDocsRepository;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\Textarea;
use Filament\Infolists\Components\Fieldset;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;
use Livewire\Attributes\On;

class AddDocItem extends ViewRecord
{
    use InteractsWithInfolists;
    use InteractsWithFormActions;

    protected static string $resource = WarehouseDocResource::class;

    public $transaction_id;
    public $accepted = false;

    public $previousUrl = null;

    public array $items = [];

    public function mount($record): void
    {
        $this->transaction_id = $record;
        $this->loadParentRecord($this->transaction_id);
    }

    protected function getForms(): array
    {
        return [
            'form',
        ];
    }


    public function getHeading(): string|Htmlable
    {
        if ($this->record === null) {
            return 'Dodaj produkt';
        }

        if ($this->getRecord()->isCancelled()) {
            return 'Dokument ' .
                $this->record->type->name .
                ' nr: ' .
                $this->record->full_doc_number .
                ' jest anulowany!!!';
        }

        return match ($this->accepted) {
            true => 'Produkty w dokumencie ' .
                $this->record->type->name .
                ' nr: ' .
                $this->record->full_doc_number,
            default => 'Dodaj produkty do dokumentu ' .
                $this->record->type->name .
                ' nr: ' .
                $this->record->full_doc_number
        };
    }

    protected function loadParentRecord($record)
    {
        $this->record = WarehouseDoc::where('transaction_id', $record)->first();
        if (empty($this->record)) {
            abort(404);
        }
        $this->accepted = (bool)$this->record->accepted;
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return parent::infolist($infolist)
            ->columns(3)
            ->record($this->record)
            ->schema([
                Fieldset::make('A')
                    ->columnSpan(1)
                    ->columns(1)
                    ->schema([
                        TextEntry::make('warehouse.name')
                            ->inlineLabel()
                            ->label(__('app.warehouse_docs.create.warehouse') . ':'),
                        TextEntry::make('transaction_id')
                            ->inlineLabel()
                            ->label(__('app.warehouse_docs.create.transaction_id')),
                        TextEntry::make('user')
                            ->state(fn(Model $record) => $record->user->fullName() ?? $record->user->email)
                            ->inlineLabel()
                            ->label(__('app.warehouse_docs.create.creator')),
                        TextEntry::make('notes')
                            ->inlineLabel()
                            ->label(__('app.warehouse_docs.create.notes')),
                    ]),
                Fieldset::make('B')
                    ->columnSpan(1)
                    ->columns(1)
                    ->schema([
                        TextEntry::make('partner.name')->inlineLabel()->default('Wewnętrzny'),
                        TextEntry::make('target_warehouse.name')
                            ->inlineLabel()
                            ->label(__('app.warehouse_docs.create.warehouse') . ':')
                            ->visible(
                                fn(Model $record) => $record->type === DocumentTypes::MW ||
                                    $record->type === DocumentTypes::MP
                            ),
                        TextEntry::make('signatory')
                            ->inlineLabel()
                            ->label(__('app.warehouse_docs.create.signatory_fullname'))
                            ->state(fn(Model $record) => $record->signatory_name . ' ' . $record->signatory_last_name),
                        TextEntry::make('related')
                            ->label('Dokument powiązany')
                            ->inlineLabel()
                            ->state(fn(Model $record) => $this->relatedDocumentState($record))
                            ->url(fn(Model $record) => $this->relatedDocumentUrl($record)),
                    ]),
                Fieldset::make('Daty')
                    ->columnSpan(1)
                    ->columns(1)
                    ->schema([
                        TextEntry::make('doc_date')
                            ->state(fn(Model $record) => match (filled($record->doc_date)) {
                                false => null,
                                default => $record->doc_date->format('Y-m-d')
                            })
                            ->inlineLabel()
                            ->label(__('app.warehouse_docs.create.doc_date')),
                        TextEntry::make('created_at')
                            ->dateTime('Y-m-d H:i:s', 'Europe/Warsaw')
                            ->inlineLabel()
                            ->label(__('app.warehouse_docs.create.created_at')),
                        TextEntry::make('accepted_at')
                            ->dateTime('Y-m-d H:i:s', 'Europe/Warsaw')
                            ->inlineLabel()
                            ->label(__('app.warehouse_docs.create.accepted_at'))
                            ->default(''),
                        TextEntry::make('items_issue_date')
                            ->dateTime('Y-m-d H:i:s', 'Europe/Warsaw')
                            ->default('')
                            ->inlineLabel()
                            ->label(__('app.warehouse_docs.create.items_issue_date_short')),
                        TextEntry::make('cancelled_at')
                            ->dateTime('Y-m-d H:i:s', 'Europe/Warsaw')
                            ->default('')
                            ->inlineLabel()
                            ->visible(fn(Model $record) => $record->cancelled_at)
                            ->label('Anulowany'),
                        TextEntry::make('cancelled_by')
                            ->state(fn(Model $record) => $record->cancelledby->email)
                            ->default('')
                            ->inlineLabel()
                            ->visible(fn(Model $record) => $record->cancelled_at)
                            ->label('Anulowany przez'),

                    ])
            ]);
    }

    public function relatedDocumentState(WarehouseDoc|Model $record): ?string
    {
        return match ($record->type) {
            default => $record->related_doc,
            DocumentTypes::MW,
            DocumentTypes::MP => $record->getCounterpart()?->transaction_id,
        };
    }

    public function relatedDocumentUrl(WarehouseDoc|Model $record): string
    {
        return match ($record->type) {
            default => '',
            DocumentTypes::MW,
            DocumentTypes::MP => match ($this->relatedDocumentState($record)) {
                null => '',
                default => self::getUrl(['record' => $this->relatedDocumentState($record)]),
            }
        };
    }

    public function relatedDocumentVisible(WarehouseDoc|Model $record): bool
    {
        return $record->accepted === 1 &&
            ($record->type === DocumentTypes::MW || $record->type === DocumentTypes::MP);
    }


    public function getHeaderActions(): array
    {
        return [
            Action::make('Lista')
                ->button()
                ->icon('heroicon-o-list-bullet')
                ->url(function () {
                    return $this->previousUrl ?? self::getResource()::getUrl('index');
                }),
            WarehouseDocResource\Actions\AcceptAction::make(),
            PrintAction::make('print_doc'),
            ActionGroup::make([
                EditAction::make()
                    ->url(
                        fn(Model $record) => self::getResource()::getUrl('edit', ['record' => $record->transaction_id])
                    )
                    ->hidden(fn(WarehouseDoc $record) => !WarehouseDocsRepository::canDocBeEdited($record)),
                WarehouseDocResource\Actions\CancelAction::make()
                    ->form([
                        Textarea::make('note')
                            ->label('Powód anulowania dokumentu')
                            ->maxLength(191)
                    ])
                    ->visible(
                        fn(WarehouseDoc $record) => WarehouseDocsRepository::canDocBeCancelled($record)
                    )
                    ->action(
                        fn(WarehouseDoc $record, $data) => $this->cancelActionProcess($record, $data)
                    ),
                DeleteAction::make()
                    ->visible(
                        fn(WarehouseDoc $record) => WarehouseDocsRepository::canDocBeRemoved($record)
                    )
                    ->using(function (WarehouseDoc $record) {
                        if (false === $this->deleteActionProcess($record)) {
                            Notification::make()
                                ->title('Nie można skasować dokumentu!')
                                ->body($this->getDeleteError())
                                ->send();
                            $this->halt();
                        }
                        return true;
                    })
            ]),
        ];
    }


    #[On('itemAdded')]
    public function onItemAdded()
    {
        $this->getHeaderActions()[2]->disabled(false);
    }

    #[On('itemRemoved')]
    public function onItemRemoved()
    {
        $this->getHeaderActions()[2]->disabled(true);
    }

    public function acceptDoc($data, WarehouseDoc|Model $record): Model
    {
        $record->items_issue_date = $data['items_issue_date'];
        if (null === WarehouseDocsRepository::acceptDocProcess($record)) {
            Notification::make()
                ->title('Error while creating document')
                ->body(WarehouseDocsRepository::$error)
                ->danger()
                ->send();
            $this->halt();
        }

        return $record;
    }

    public function deleteActionProcess(WarehouseDoc $record): bool
    {
        return WarehouseDocsRepository::deleteWarehouseDoc($record);
    }

    public function cancelActionProcess(WarehouseDoc $record, $data): bool
    {
        return WarehouseDocsRepository::cancelWarehouseDoc($record, $data['note'] ?? null);
    }

    public function getRelationManagers(): array
    {
        return [
            WarehouseDocResource\RelationManagers\LogRelationManager::class
        ];
    }
}
