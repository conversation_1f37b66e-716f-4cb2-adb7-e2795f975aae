<?php

namespace App\Filament\App\Resources\WarehouseDocResource\Pages;

use App\Enums\DocumentGeneralTypes;
use App\Enums\DocumentTypes;
use App\Enums\SystemModules;
use App\Filament\App\Resources\WarehouseDocResource;
use App\Filament\Filters\WHSelectFilter;
use App\Filament\Tables\Actions\PrintDocAction;
use Filament\Actions;
use Filament\Forms\Get;
use Filament\Resources\Pages\ListRecords;
use Filament\Support\Colors\Color;
use Filament\Support\Enums\FontWeight;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use pxlrbt\FilamentExcel\Actions\Tables\ExportAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;

class ListWarehouseDocs extends ListRecords
{
    protected static string $resource = WarehouseDocResource::class;

    protected static ?string $navigationIcon = 'heroicon-o-newspaper';

    /**
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return __('app.warehouse_docs._.heading');
    }

    protected function getHeaderActions(): array
    {
        return match (auth()->user()?->isTenantAdmin() ?? false) {
            true => [
                Actions\CreateAction::make(),
            ],
            default => []
        };
    }

    public static function getNavigationLabel(): string
    {
        return 'Dokumenty';
    }

    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return parent::shouldRegisterNavigation($parameters) && tenant()->hasModule(SystemModules::WAREHOUSE);
    }

    public function table(Table $table): Table
    {
        return match (auth()->user()?->isTenantAdmin() ?? false) {
            default => $this->adminTable($table),
            false => $this->emplyeeTable($table)
        };
    }


    public function emplyeeTable(Table $table): Table
    {
        return $table
            ->columns(
                [
                    Tables\Columns\Layout\Stack::make([

                        Tables\Columns\Layout\Split::make([
                            Tables\Columns\TextColumn::make('warehouse.name')
                                ->label(__('app.warehouse_docs.list.warehouse'))
                                ->numeric()
                                ->weight(FontWeight::Bold)
                                ->extraCellAttributes(function (Model $record) {
                                    return match ((bool)$record->accepted) {
                                        default => ['style' => 'border-left:3px solid rgb(22 163 74)!important'],
                                        false => ['style' => 'border-left:3px solid rgb(234 88 12)!important']
                                    };
                                })
                                ->sortable(),
                            Tables\Columns\TextColumn::make('partner.name')
                                ->label(__('app.warehouse_docs.list.partner'))
                                ->icon('heroicon-m-user-group')
                                ->default(__('app.warehouse_docs.list.partner_name_default'))
                                ->searchable(),
                        ]),
                        Tables\Columns\Layout\Split::make([
                            Tables\Columns\Layout\Stack::make([
                                Tables\Columns\TextColumn::make('type_number')
                                    ->state(fn(Model $record) => $record->type->name . ' ' . $record->doc_number)
                                    ->label(__('app.warehouse_docs.list.doctype'))
                                    ->sortable(['type'])
                                    ->searchable(['type', 'doc_number']),
                                Tables\Columns\TextColumn::make('transaction_id')
                                    ->label(__('app.warehouse_docs.list.transaction_id'))
                                    ->searchable(),
                            ]),
                            Tables\Columns\TextColumn::make('items_issue_date')
                                ->label(__('app.warehouse_docs.list.issue_date'))
                                ->dateTime('Y-m-d H:i', 'Europe/Warsaw')
                                ->icon('heroicon-m-calendar-days')
                                ->sortable(),
                        ])->extraAttributes(['class' => 'py-4'], true)
                    ])
                ]
            )
            ->defaultSort('created_at', 'desc')
            ->recordUrl(null)
            ->filters(
                $this->getTableFilters()
            )
            ->filtersLayout(Tables\Enums\FiltersLayout::Modal)
            ->actions(
                actions: $this->getRowActions(),
            )
            ->modifyQueryUsing(function (Builder $query) {
                if (false === (auth()->user()?->isTenantAdmin() ?? false)) {
                    return $query->where('user_id', (auth()->user()?->id ?? -1))
                        ->where('cancelled', false);
                }
                return $query;
            });
    }

    public function adminTable(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('warehouse.name')
                    ->label(__('app.warehouse_docs.list.warehouse'))
                    ->numeric()
                    ->extraCellAttributes(function (Model $record) {
                        return match ((bool)$record->cancelled) {
                            false => match ((bool)$record->accepted) {
                                default => ['style' => 'border-left:3px solid rgb(22 163 74)!important'],
                                false => ['style' => 'border-left:3px solid rgb(234 88 12)!important']
                            },
                            default => ['style' => 'border-left:3px solid rgb(55 55 55)!important']
                        };
                    })
                    ->formatStateUsing(
                        fn(Model $record, $state) => match ((bool)$record->warehouse->is_active) {
                            false => $state . ' - inactive',
                            default => $state
                        }
                    )
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->state(fn(Model $record) => $record->type->name)
                    ->label(__('app.warehouse_docs.list.doctype'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('partner_name')
                    ->label(__('app.warehouse_docs.list.partner'))
                    ->state(fn(Model $record) => $record->partner?->displayName() ?? auth()->user()->getTenant()->name)
                    ->searchable(),
                Tables\Columns\TextColumn::make('items_issue_date')
                    ->label(__('app.warehouse_docs.list.issue_date'))
                    ->dateTime('Y-m-d H:i', 'Europe/Warsaw')
                    ->sortable(),
                Tables\Columns\TextColumn::make('doc_number')
                    ->label(__('app.warehouse_docs.list.doc_number'))
                    ->searchable(),
                Tables\Columns\IconColumn::make('accepted')
                    ->label(__('app.warehouse_docs.list.accepted'))
                    ->boolean()
                    ->state(fn($record) => match ($record->cancelled) {
                        true => 'cancelled',
                        default => $record->accepted ? 'accepted' : 'not_accepted'
                    })
                    ->icon(function (Model $record) {
                        return match ((bool)$record->cancelled) {
                            true => 'heroicon-o-x-circle',
                            default => match ((bool)$record->accepted) {
                                true => 'heroicon-o-check-circle',
                                default => 'heroicon-o-minus-circle'
                            }
                        };
                    })
                    ->color(function (Model $record) {
                        return match ((bool)$record->cancelled) {
                            true => 'gray',
                            default => match ((bool)$record->accepted) {
                                true => 'success',
                                default => 'warning'
                            }
                        };
                    })
                    ->tooltip(function (Model $record) {
                        return match ((bool)$record->cancelled) {
                            true => __('app._.cancelled'),
                            default => match ((bool)$record->accepted) {
                                true => __('app._.accepted'),
                                default => __('app._.not_accepted')
                            }
                        };
                    })
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->sortable(),
                Tables\Columns\TextColumn::make('transaction_id')
                    ->label(__('app.warehouse_docs.list.transaction_id'))
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('app.warehouse_docs.list.created_at'))
                    ->dateTime('Y-m-d H:i:s', 'Europe/Warsaw')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('app.warehouse_docs.list.updated_at'))
                    ->dateTime('Y-m-d H:i:s', 'Europe/Warsaw')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('creator')
                    ->label(__('app.warehouse_docs.list.creator'))
                    ->state(fn(Model $record) => $record->user->fullName())
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('document_series_id')
                    ->label(__('app.warehouse_docs.list.document_series'))
                    ->state(fn(Model $record) => $record->documentSeries->name)
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: false),
            ])
            ->defaultSort('created_at', 'desc')
            ->recordUrl(null)
            ->filters(
                $this->getTableFilters()
            )
            ->persistFiltersInSession(true)
            ->headerActions([
                ExportAction::make()
                    ->exports([
                        ExcelExport::make()
                            ->withFilename(function () {
                                return 'documents_' . now('Europe/Warsaw')->format('Y-m-d_H-i');
                            })
                            ->fromTable()
                    ])
            ])
            ->filtersLayout(Tables\Enums\FiltersLayout::AboveContent)
            ->actions(
                $this->getRowActions()
            )->modifyQueryUsing(function (Builder $query) {
                if (false === (auth()->user()?->isTenantAdmin() ?? false)) {
                    return $query->where('user_id', auth()->user()->id);
                }
                return $query;
            });
    }

    public function getTableFilters(): array
    {
        return [
            Tables\Filters\TernaryFilter::make('accepted')
                ->label(__('app.warehouse_docs.list.filters.accepted_label'))
                ->falseLabel(__('app.warehouse_docs.list.filters.false_label'))
                ->trueLabel(__('app.warehouse_docs.list.filters.true_label'))
                ->modifyQueryUsing(fn(Builder $query, $state, Get $get) => match ($state['value']) {
                    '1' => $query->where('accepted', true),
                    '0' => $query->where('accepted', false),
                    default => $query,
                })
            ,
            Tables\Filters\SelectFilter::make('type')
                ->label(__('app.warehouse_docs.list.filters.doctype_label'))
                ->options(
                    collect(DocumentTypes::getDocTypesOf(DocumentGeneralTypes::WAREHOUSE))
                        ->mapWithKeys(fn($type) => [$type->value => $type->label()])
                ),
            Tables\Filters\SelectFilter::make('document_series_id')
                ->label(__('app.warehouse_docs.list.filters.docseries_label'))
                ->relationship('documentSeries', 'name'),
            WHSelectFilter::make('warehouse_id')
                ->options(fn() => WHSelectFilter::getOptionsForLoggedInEmployee())
                ->label(__('app.warehouse_docs.list.warehouse')),
            Tables\Filters\SelectFilter::make('cancelled')
                ->default('no')
                ->visible(auth()->user()?->isTenantAdmin() ?? false)
                ->options([
                    'no' => __('app.warehouse_docs.list.filters.false_label'),
                    'yes' => __('app.warehouse_docs.list.filters.true_label')
                ])
                ->selectablePlaceholder(false)
                ->label(__('app.warehouse_docs.list.filters.cancelled_label'))
                ->modifyQueryUsing(fn(Builder $query, $state, Get $get) => match ($state['value']) {
                    'yes' => $query->where('cancelled', true),
                    'no' => $query->where('cancelled', false),
                    default => $query,
                })
        ];
    }

    public function getRowActions(): array
    {

        return match (auth()->user()?->isTenantAdmin() ?? false) {
            default => [
                Tables\Actions\Action::make('Podgląd')
                    ->url(
                        fn(Model $record): string => $this->getResUrl('add-item', ['record' => $record->transaction_id])
                    )
                    ->color(Color::Gray)
                    ->tooltip(__('app.warehouse_docs.list.actions.preview_tooltip'))
                    ->label(__('app.warehouse_docs.list.actions.preview_label'))
                    ->iconButton()
                    ->icon('heroicon-m-eye')
                    ->visible(fn(Model $record): bool => $record->accepted || $record->isCancelled()),
                PrintDocAction::make('printAction')
                    ->visible(fn(Model $record): bool => $record->accepted || $record->isCancelled()),
                Tables\Actions\Action::make('Produkty')
                    ->label(__('app.warehouse_docs.list.actions.products_label'))
                    ->url(
                        fn(Model $record): string => $this->getResUrl(
                            'add-item',
                            ['record' => $record->transaction_id]
                        )
                    )
                    ->tooltip(__('app.warehouse_docs.list.actions.products_tooltip'))
                    ->iconButton()
                    ->icon('heroicon-o-queue-list')
                    ->hidden(fn(Model $record): bool => $record->accepted || $record->isCancelled()),
            ],
            false => [
                Tables\Actions\Action::make('Podgląd')
                    ->url(
                        fn(Model $record): string => $this->getResUrl('add-item', ['record' => $record->transaction_id])
                    )
                    ->tooltip(__('app.warehouse_docs.list.actions.preview_tooltip'))
                    ->label(__('app.warehouse_docs.list.actions.preview_label'))
                    ->visible(fn(Model $record): bool => $record->accepted || $record->isCancelled()),
                Tables\Actions\Action::make('Produkty')
                    ->label(__('app.warehouse_docs.list.actions.products_label'))
                    ->url(
                        fn(Model $record): string => $this->getResUrl('add-item', ['record' => $record->transaction_id])
                    )
                    ->hidden(fn(Model $record): bool => $record->accepted || $record->isCancelled()),
                PrintDocAction::make('printAction')
                    ->visible(fn(Model $record): bool => $record->accepted || $record->isCancelled()),
            ]
        };
    }

    public function getResUrl(string $type, array $data = []): string
    {
        return self::getResource()::getUrl($type, $data);
    }
}
