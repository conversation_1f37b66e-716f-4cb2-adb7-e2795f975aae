<?php

namespace App\Filament\App\Resources\WarehouseDocResource\Pages;

use App\Enums\DocumentTypes;
use App\Filament\App\Resources\WarehouseDocResource;
use App\Models\WarehouseDoc;
use Filament\Actions\Action;
use Filament\Infolists\Components\Fieldset;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Infolist;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;
use Livewire\Attributes\On;
use Livewire\Component;

class CreateWz extends ViewRecord
{
    use InteractsWithInfolists;
    use InteractsWithFormActions;

    protected static string $resource = WarehouseDocResource::class;

    public $transaction_id;

    public $document_type = '';

    public array $items = [];

    public function mount($record): void
    {
        $this->document_type = DocumentTypes::ZW->name;
        $this->transaction_id = $record;
        $this->loadParentRecord($this->transaction_id);
    }

    protected function getForms(): array
    {
        return [
            'form',
        ];
    }


    public function getHeading(): string|Htmlable
    {
        if ($this->record === null) {
            return 'Dodaj produkt';
        }
        return 'Dodaj produkty do dokumentu ' . $this->record->type->name . ' nr: ' . $this->record->doc_number;
    }

    protected function loadParentRecord($record)
    {
        $this->record = WarehouseDoc::where('transaction_id', $record)->first();
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return parent::infolist($infolist)
            ->columns(2)
            ->record($this->record)
            ->schema([
                Fieldset::make('A')
                    ->columnSpan(1)
                    ->columns(1)
                    ->schema([
                        TextEntry::make('warehouse.name')
                            ->inlineLabel()
                            ->label(__('app.warehouse_docs.create.warehouse') . ':'),
                        TextEntry::make('transaction_id')
                            ->inlineLabel()
                            ->label(__('app.warehouse_docs.create.transaction_id')),

                        TextEntry::make('partner.name')->inlineLabel(),
                    ]),
                Fieldset::make('B')
                    ->columnSpan(1)
                    ->columns(1)
                    ->schema([
                        TextEntry::make('items_issue_date')
                            ->default(now())
                            ->inlineLabel()
                            ->label(__('app.warehouse_docs.create.items_issue_date_short')),
                        TextEntry::make('created_at')
                            ->inlineLabel()
                            ->label(__('app.warehouse_docs.create.created_at')),
                        TextEntry::make('notes')
                            ->inlineLabel()
                            ->label(__('app.warehouse_docs.create.notes')),
                    ])
            ]);
    }


    public function getHeaderActions(): array
    {
        return [
            Action::make('Lista')
                ->button()
                ->url(self::getResource()::getUrl('index')),
            \Filament\Actions\EditAction::make()
                ->url(fn(Model $record) => self::getResource()::getUrl('edit', ['record' => $record->transaction_id]))
            ->disabled(fn(WarehouseDoc $record) => $record->accepted),
            Action::make('Zaakceptuj')
                ->button()
                ->color(fn(WarehouseDoc $record) => $record->accepted ? 'success' : 'warning')
                ->disabled(fn(WarehouseDoc $record) => $record->accepted)
                ->label(function (WarehouseDoc $record) {
                    return $record->accepted ? 'Zaakceptowany' : 'Zaakceptuj';
                })
                ->requiresConfirmation()
                ->modalHeading('Akceptacja')
                ->modalDescription('Chcesz zaakceptować dokument? Akceptacja jest na nieodwracalna.')
                ->modalSubmitActionLabel('Tak, zaakceptuj')
                ->closeModalByClickingAway(false)
                ->action(
                    function ($data, Model $record) {
                        $record = $this->AcceptDoc($data, $record);
                        $this->record = $record;
                        $this->fillForm();
                    }
                )
                ->disabled(function (Model $record) {
                    return !($record->log()->count() > 0);
                })->after(function (Component $livewire){
                    $livewire->dispatch('refreshItems');
                }),
        ];
    }

    #[On('itemAdded')]
    public function onItemAdded()
    {
        $this->getHeaderActions()[2]->disabled(false);
    }

    #[On('itemRemoved')]
    public function onItemRemoved()
    {
        $this->getHeaderActions()[2]->disabled(true);
    }

    public function AcceptDoc($data, Model $record): Model
    {
        $record->accept()->save();
        return $record;
    }

    public function getRelationManagers(): array
    {
        return [
            WarehouseDocResource\RelationManagers\LogRelationManager::class
        ];
    }


}
