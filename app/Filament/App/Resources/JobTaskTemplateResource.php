<?php

namespace App\Filament\App\Resources;

use App\Filament\Actions\SetToggleRepeaterDistinct;
use App\Filament\App\Resources\JobTaskTemplateResource\Pages;
use App\Models\JobTaskTemplate;
use Filament\Forms;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class JobTaskTemplateResource extends Resource
{
    protected static ?string $model = JobTaskTemplate::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->label('Tytuł')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Repeater::make('configuration')
                    ->label('Konfiguracja')
                    ->grid(1)
                    ->columns(2)
                    ->addActionLabel('Dodaj pole')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->columnSpan(1)
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->label('Tytuł')
                                    ->required()
                                    ->columnSpanFull(),
                                Forms\Components\Toggle::make('is_products_list')
                                    ->default(false)
                                    ->inline(false)
                                    ->live()
                                    ->formatStateUsing(fn ($state) => ($state ?? false) === true)
                                    ->afterStateUpdated(function ($state, Forms\Components\Toggle $toggle, Set $set) {
                                        (new SetToggleRepeaterDistinct)($state, $toggle, $set, 'is_products_list');
                                    })
                                    ->label('Lista produktów'),
                                Select::make('valueType')
                                    ->label('Wstaw wartość')
                                    ->suffixAction(
                                        Action::make('add')
                                            ->iconButton()
                                            ->icon('heroicon-s-chevron-right')
                                            ->label('Dodaj')
                                            ->action(fn(Get $get, Set $set) => $set('value', $get('valueType')))
                                    )
                                    ->disabled(fn(Forms\Get $get) => $get('is_products_list') === true)
                                    ->options([
                                        ...JobTaskResource::getShortcuts(),
                                    ]),
                            ]),
                        Forms\Components\Grid::make(1)
                            ->columnSpan(1)
                            ->schema([
                                Forms\Components\Textarea::make('value')
                                    ->rows(5)
                                    ->label('Wartość')
                                    ->visible(fn($get) => $get('is_products_list') === false),
                                Forms\Components\Placeholder::make('value')
                                    ->content(fn() => 'Lista produktów')
                                    ->label('Wartość')
                                    ->visible(fn($get) => $get('is_products_list') === true)
                            ])
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Nazwa'),
                TextColumn::make('created_at')
                    ->label('Dodany'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListJobTaskTemplates::route('/'),
            'create' => Pages\CreateJobTaskTemplate::route('/create'),
            'edit' => Pages\EditJobTaskTemplate::route('/{record}/edit'),
        ];
    }
}
