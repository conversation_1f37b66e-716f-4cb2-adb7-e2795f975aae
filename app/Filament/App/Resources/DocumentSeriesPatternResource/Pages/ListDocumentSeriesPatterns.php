<?php

namespace App\Filament\App\Resources\DocumentSeriesPatternResource\Pages;

use App\Filament\App\Resources\DocumentSeriesPatternResource;
use App\Models\DocumentSeriesPattern;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;

class ListDocumentSeriesPatterns extends ListRecords
{
    protected static string $resource = DocumentSeriesPatternResource::class;

    public function getHeading(): string|Htmlable
    {
        return 'Numeracja dokumentów';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
