<?php

namespace App\Filament\App\Resources\DocumentSeriesPatternResource\Pages;

use App\Filament\App\Resources\DocumentSeriesPatternResource;
use App\Filament\Traits\HasBackToListButton;
use Filament\Resources\Pages\CreateRecord;

class CreateDocumentSeriesPattern extends CreateRecord
{
    use HasBackToListButton;

    protected static string $resource = DocumentSeriesPatternResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['installation'] = auth()->user()->installation();
        $data['is_default'] = $data['is_default'] ?? false;
        $data['is_active'] = $data['is_active'] ?? false;
        return $data;
    }

    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction(),
            self::getBackToListFormAction(),
        ];
    }
}
