<?php

namespace App\Filament\App\Resources\DocumentSeriesPatternResource\Pages;

use App\Filament\App\Resources\DocumentSeriesPatternResource;
use App\Filament\Traits\HasBackToListButton;
use App\Models\DocumentSeriesPattern;
use App\Repositories\DocumentSeriesRepository;
use Filament\Actions;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditDocumentSeriesPattern extends EditRecord
{
    use HasBackToListButton;

    protected static string $resource = DocumentSeriesPatternResource::class;

    public static function getNavigationLabel(): string
    {
        return 'Edycja rekordu';
    }

    /**
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return 'Edycja rekordu';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->using(fn(DocumentSeriesPattern $record) => $this->handleDeleteAction($record))
                ->modalHeading('Usunąć wzór numeracji dokumentu?')
                ->modalDescription('Ta czynność jest nieodwracalna!')
                ->visible(fn($record) => $record->isEnabledToRemove())
                ->after(fn() => $this->redirect($this::getResource()::getUrl('index'))),
        ];
    }

    protected function handleRecordUpdate(Model|DocumentSeriesPattern $record, array $data): Model
    {
        return DocumentSeriesRepository::handleUpdate($record, $data);
    }

    public function handleDeleteAction(DocumentSeriesPattern $model): void
    {
        DocumentSeriesRepository::deleteOrSoftDeletePattern($model);
    }

    protected function getFormActions(): array
    {
        return [
            $this->getSaveFormAction(),
            self::getBackToListFormAction(),
        ];
    }
}
