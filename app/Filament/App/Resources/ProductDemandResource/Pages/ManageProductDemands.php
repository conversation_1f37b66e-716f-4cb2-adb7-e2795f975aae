<?php

namespace App\Filament\App\Resources\ProductDemandResource\Pages;

use App\Enums\ProductDemandsStatuses;
use App\Filament\App\Resources\ProductDemandResource;
use App\Models\ProductDemand;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Database\Eloquent\Builder;
use pxlrbt\FilamentExcel\Actions\Pages\ExportAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;

class ManageProductDemands extends ManageRecords
{
    protected static string $resource = ProductDemandResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->modalHeading(__('app.product_demands.page.create_heading'))
                ->using(function ($data) {
                    return $this->saveDemand($data);
                }),
            ExportAction::make()
                ->exports([
                    ExcelExport::make()->fromTable()
                ])->visible()
        ];
    }

    /**
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return __('app.product_demands.page.heading');
    }

    protected function applyFiltersToTableQuery(Builder $query): Builder
    {
        return parent::applyFiltersToTableQuery($query);
    }

    public function form(Form $form): Form
    {

        $schema = [
            Forms\Components\Select::make('product_id')
                ->relationship(
                    name: 'product',
                    titleAttribute: 'name',
                    modifyQueryUsing: fn (Builder $query) => $query->where('is_active', 1)
                )
                ->label(__('app.product_demands.list.product'))
                ->required(),
            Forms\Components\TextInput::make('amount')
                ->label(__('app.product_demands.list.amount'))
                ->required()
                ->numeric()
                ->minValue(0),
        ];


        switch ($form->getOperation()) {
            case 'view':
                $schema[] = Forms\Components\DatePicker::make('demand_at')
                    ->displayFormat('Y-m-d')
                    ->label(__('app.product_demands.list.demand_at'));
                $schema[] = Forms\Components\TextInput::make('status')
                    ->formatStateUsing(fn($state) => ProductDemandsStatuses::tryFrom($state)?->label());
                break;
            case 'edit':
                $schema[] = Forms\Components\DatePicker::make('demand_at')
                    ->displayFormat('Y-m-d')
                    ->native(false)
                    ->default(now()->addDays(1))
                    ->label(__('app.product_demands.list.demand_at'))
                    ->required();
                $schema[] = Forms\Components\Select::make('status')
                    ->required()
                    ->options(ProductDemandsStatuses::class);
                break;
            default:
                $schema[] = Forms\Components\DatePicker::make('demand_at')
                    ->displayFormat('Y-m-d')
                    ->native(false)
                    ->minDate(now()->addDays(1)->format('Y-m-d'))
                    ->default(now()->addDays(1))
                    ->label(__('app.product_demands.list.demand_at'))
                    ->required();
                $schema[] = Forms\Components\Select::make('status')
                    ->required()
                    ->default(ProductDemandsStatuses::ACTIVE->value)
                    ->options(ProductDemandsStatuses::class);
                break;
        }

        return $form->schema($schema);
    }


    public function saveDemand($data)
    {
        $data['user_id'] = auth()->user()->id;
        $data['installation'] = auth()->user()->installation();
        $data['status'] = ProductDemandsStatuses::ACTIVE->value;
        $demand = new ProductDemand();
        $demand->fill($data);
        $demand->save();
        return $demand;
    }
}
