<?php

namespace App\Filament\App\Resources\WarehouseResource\Pages;

use App\Enums\WarehouseTypes;
use App\Filament\App\Resources\WarehouseResource;
use App\Models\User;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateWarehouse extends CreateRecord
{
    protected static string $resource = WarehouseResource::class;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label(__('app._.name'))
                    ->required()
                    ->maxLength(100)
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('address')
                    ->label(__('app._.address'))
                    ->maxLength(65535)
                    ->columnSpanFull(),
                Forms\Components\Select::make('owner_type')
                    ->label(__('app.warehouses.edit.owner_type'))
                    ->required()
                    ->live()
                    ->options(WarehouseTypes::toArrayWithLabels())
                    ->default(1),
                Forms\Components\Select::make('owner_identifier')
                    ->key('ownerIdentifier')
                    ->label(__('app.warehouses.edit.owner_identifier'))
                    ->options(
                        function (Forms\Get $get) {
                            if ((int) $get('owner_type') === WarehouseTypes::COMPANY->value) {
                                return auth()->user()->tenant->pluck('name', 'id');
                            }

                            if ((int) $get('owner_type') === WarehouseTypes::USER->value) {
                                return User::all('email', 'id')->pluck('email', 'id');
                            }
                        }
                    )
                    ->required(),
                Forms\Components\Toggle::make('is_active')
                    ->label(__('app._.is_active'))
                    ->required(),
            ]);
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['installation'] = auth()->user()?->installation() ?? 0;
        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return self::getResource()::getUrl('edit', ['record' => $this->getRecord()->hash]);
    }


}
