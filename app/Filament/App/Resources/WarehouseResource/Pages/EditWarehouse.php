<?php

namespace App\Filament\App\Resources\WarehouseResource\Pages;

use App\Enums\WarehouseTypes;
use App\Filament\App\Resources\WarehouseResource;
use App\Models\Tenant;
use App\Models\User;
use App\Models\Warehouse;
use App\Repositories\WarehouseRepository;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditWarehouse extends EditRecord
{
    protected static string $resource = WarehouseResource::class;
    public $installation;


    public function mount(int | string $record): void
    {
        $this->record = Warehouse::where('hash', $record)->where('installation', INSTALLATION)->first();
        $this->installation = INSTALLATION;
        $this->authorizeAccess();

        $this->fillForm();
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
            ->using(fn(Model $record) => $this->processDelete($record))
            ->visible(
                fn() => $this->record->items()->count() === 0 && $this->record->docs()->count() === 0
            ),
        ];
    }

    public function getModelLabel(): string
    {
//        return __('app.warehouse.edit.Warehouse') . self::getResource()->installation;
        return 'Magazyn' . $this->installation;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label(__('app._.name'))
                    ->required()
                    ->maxLength(100)
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('address')
                    ->label(__('app._.address'))
                    ->maxLength(65535)
                    ->columnSpanFull(),
                Forms\Components\Select::make('owner_type')
                    ->label(__('app.warehouses.edit.owner_type'))
                    ->required()
                    ->live()
                    ->options(WarehouseTypes::toArrayWithLabels())
                    ->default(1),
                Forms\Components\Select::make('owner_identifier')
                    ->key('ownerIdentifier')
                    ->label(__('app.warehouses.edit.owner_identifier'))
                    ->options(
                        function (Forms\Get $get) {
                            if ((int) $get('owner_type') === WarehouseTypes::COMPANY->value) {
                                return auth()->user()->tenant->pluck('name', 'id');
                            }

                            if ((int) $get('owner_type') === WarehouseTypes::USER->value) {
                                return User::all('email', 'id')->pluck('email', 'id');
                            }
                        }
                    )
                    ->required(),
                Forms\Components\Toggle::make('is_active')
                    ->label(__('app._.is_active'))
                    ->required(),
            ]);
    }


    public function processDelete(Model|Warehouse $record): bool
    {
        if ($record->items()->count() > 0) {
            Notification::make()
                ->danger()
                ->body('Magazyn nie jest pusty!')
                ->send();
            return false;
        }

        return WarehouseRepository::removeWarehouse($this->record);
    }
}
