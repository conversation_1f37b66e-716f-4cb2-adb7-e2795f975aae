<?php

namespace App\Filament\App\Resources\JobTaskResource\RelationManagers;

use App\Enums\JobTaskStatuses;
use App\Models\JobTaskItem;
use App\Models\Products;
use App\Models\WarehouseItem;
use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use Livewire\Attributes\On;

class ItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'items';


    public function isReadOnly(): bool
    {
        return false;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('product_id')
                    ->relationship('product', 'name')
                    ->label('Produkt')
                    ->searchable()
                    ->preload()
                    ->live()
                    ->afterStateUpdated(
                        function (Forms\Set $set, $state) {
                            $product = Products::find($state);
                            $set('product_name', $product->name ?? '');
                            $set('quantity_unit', $product->base_unit ?? 'szt');
                        }
                    ),
                Forms\Components\TextInput::make('product_name')
                    ->label('Nazwa produktu')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Grid::make(3)
                    ->schema([
                        TextInput::make('quantity_unit')
                            ->label('Jednostka')
                            ->readOnly(),
                        TextInput::make('expected_quantity')
                            ->numeric()
                            ->label('Ilość oczekiwana')
                            ->required(),
                        TextInput::make('quantity')
                            ->label('Ilość')
                            ->numeric()
                            ->default(null),
                    ]),
                Forms\Components\Toggle::make('make_demand')
                    ->label('Utwórz zapotrzebowanie')
            ]);
    }


    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->heading('Produkty')
            ->columns([
                Tables\Columns\TextColumn::make('product_name')
                    ->label('Nazwa produktu'),
                Tables\Columns\TextColumn::make('expected_quantity')
                    ->formatStateUsing(fn($state, JobTaskItem $item) => $state)
                    ->label('Oczekiwana ilość'),
                Tables\Columns\TextColumn::make('quantity')
                    ->formatStateUsing(fn($state, JobTaskItem $item) => $state)
                    ->label('Ilość'),
                Tables\Columns\IconColumn::make('make_demand')
                    ->label('Zapotrzebowanie')
                    ->boolean(),
                Tables\Columns\TextColumn::make('wh_state')
                    ->label('Stan mag. wykonawcy')
                    ->getStateUsing(function (JobTaskItem $record) {
                        if (!$this->getOwnerRecord()->executor) {
                            return '-';
                        }
                        $amount = $this->getOwnerRecord()
                            ->executor
                            ->warehouse->items()
                            ->where('product_id', $record->product_id)->sum('amount');
                        return match ($amount < $record->expected_quantity) {
                            true => new HtmlString('<span style="color:red">' . $amount . '</span>'),
                            default => new HtmlString('<span>' . $amount . '</span>'),
                        };
                    }),
                Tables\Columns\TextColumn::make('wh_overall_state')
                    ->label('Stan magazynów')
                    ->getStateUsing(function (JobTaskItem $record) {
//                        if (!$this->getOwnerRecord()->executor) {
//                            return '-';
//                        }
                        $amount = WarehouseItem::where('product_id', $record->product_id)->sum('amount');
                        return match ($amount < $record->expected_quantity) {
                            true => new HtmlString('<span style="color:red">' . $amount . '</span>'),
                            default => new HtmlString('<span>' . $amount . '</span>'),
                        };
                    }),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Dodaj produkt')
                    ->modalHeading('Dodaj produkt')
                    ->modalSubmitActionLabel('Dodaj')
                    ->extraModalFooterActions([])
                    ->visible(fn() => $this->getOwnerRecord()->isActive())
                    ->using(fn($data) => $this->handleCreate($data)),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->visible(fn() => $this->getOwnerRecord()->isActive())
                    ->using(fn($data, JobTaskItem $record) => $this->handleUpdate($data, $record)),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn() => $this->getOwnerRecord()->isActive())
                    ->using(fn(Model $record) => $this->handleDelete($record)),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn() => $this->getOwnerRecord()->isActive())
                        ->using(fn(Collection $records) => $this->handleDeleteBulk($records)),
                ]),
            ]);
    }

    public function handleDelete(JobTaskItem|Model $record): bool
    {
        $record->removeDemand();
        return $record->delete();
    }

    public function handleDeleteBulk(Collection $records): void
    {
        $records->each(fn(Model $record) => $this->handleDelete($record));
    }

    public function handleCreate(array $data): JobTaskItem
    {
        $owner = $this->getOwnerRecord();
        $data['job_task_id'] = $owner->id;
        $data['installation'] = tenant()->id;
        $record = new JobTaskItem();
        $record->fill($data)->save();
        if ($record->make_demand) {
            $record->createDemand();
        }
        return $record;
    }

    public function handleUpdate(array $data, Model|JobTaskItem $record): JobTaskItem
    {
        $record->update($data);
        if ($record->make_demand) {
            $record->removeDemand();
            $record->createDemand();
        } else {
            $record->removeDemand();
        }
        return $record;
    }

    #[On('refreshItems')]
    public function refresh(): void
    {
        $this->render();
    }
}
