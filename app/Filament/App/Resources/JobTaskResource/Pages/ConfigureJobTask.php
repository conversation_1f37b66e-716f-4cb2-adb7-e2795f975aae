<?php

namespace App\Filament\App\Resources\JobTaskResource\Pages;

use App\Filament\Actions\SetToggleRepeaterDistinct;
use App\Filament\App\Resources\JobTaskResource;
use App\Models\JobTaskTemplate;
use App\Models\PartnerWork;
use Filament\Forms;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Forms\Components;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Js;

class ConfigureJobTask extends EditRecord
{
    protected static string $resource = JobTaskResource::class;

    public function getHeading(): string|Htmlable
    {
        return 'Konfiguracja dokumentu'; // TODO: Change the autogenerated stub
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('template')
                    ->dehydrated(false)
                    ->label('Użyj pól z szablonu')
                    ->options(JobTaskTemplate::pluck('name', 'id'))
                    ->suffixAction(
                        Action::make('add')
                            ->label('Zastosuj')
                            ->icon('heroicon-s-plus')
                            ->requiresConfirmation()
                            ->modalIconColor('danger')
                            ->modalDescription('Ta opcja usunie aktualną konfigurację')
                            ->modalHeading('Pobrać dane z szablonu?')
                            ->action(
                                function ($state, Set $set) {
                                    if (blank($state)) {
                                        return null;
                                    }
                                    $this->fillFromTemplate($state, $set);
                                    $this->refreshFormData(['configuration']);
                                }
                            )
                    ),
                Forms\Components\Repeater::make('configuration')
                    ->grid(1)
                    ->columns(2)
                    ->addActionLabel('Dodaj pole')
                    ->schema(fn() => $this->getRepeaterSchema())
//                    ->extraItemActions([
//                        Action::make('refresh')
//                            ->hiddenLabel()
//                            ->iconButton()
//                            ->icon('heroicon-s-arrow-path')
//                            ->action(
//                                fn(
//                                    array $arguments,
//                                    Components\Repeater $component,
//                                ) => $this->refreshRepeaterItemData(
//                                    $arguments,
//                                    $component,
//                                )
//                            )
//                    ])
                    ->columnSpanFull(),
            ]);
    }

    protected function getRepeaterSchema(): array
    {
        return [
            Components\Grid::make(2)
                ->columnSpan(1)
                ->schema([
                    Forms\Components\TextInput::make('name')
                        ->label('Tytuł pola')
                        ->columnSpanFull(),
                    Components\Toggle::make('is_products_list')
                        ->default(false)
                        ->inline(false)
                        ->required(false)
                        ->formatStateUsing(fn($state) => ($state ?? false) === true)
                        ->live()
                        ->afterStateUpdated(function ($state, Components\Toggle $toggle, Set $set) {
                            (new SetToggleRepeaterDistinct)($state, $toggle, $set, 'is_products_list');
                        })
                        ->label('Lista produktów'),
                    Select::make('valueType')
                        ->label('Wstaw wartość')
                        ->suffixAction(
                            Action::make('add')
                                ->iconButton()
                                ->icon('heroicon-s-chevron-right')
                                ->label('Dodaj')
                                ->action(fn(Get $get, Set $set) => self::setConfValue($get('valueType'), $set, $get))
                        )
                        ->disabled(fn(Forms\Get $get) => $get('is_products_list') === true)
                        ->options([
                            ...self::getResource()::getShortcuts(),
                        ]),
                ]),
            Components\Grid::make(1)
                ->columnSpan(1)
                ->schema([
                    Forms\Components\Textarea::make('value')
                        ->key('valueTextarea')
                        ->label('Wartość')
                        ->rows(5)
                        ->visible(fn($get) => $get('is_products_list') === false),
                    Forms\Components\Placeholder::make('value')
                        ->content(fn() => 'Lista produktów')
                        ->label('Wartość')
                        ->visible(fn($get) => $get('is_products_list') === true)
                ])
        ];
    }

    public function fillFromTemplate($templateId, Set $set): self
    {
        $template = JobTaskTemplate::find($templateId);
        $old = $this->getRecord();
        $old->configuration = $template->configuration;
        $old->save();
        $this->record = $old;
        return $this;
    }

    protected function getFormActions(): array
    {
        return [
            $this->getSubmitFormAction(),
            $this->getCancelFormAction()
                ->label('Powrót')
                ->alpineClickHandler(
                    '(window.location.href = ' .
                    Js::from(static::getResource()::getUrl('add-item', ['record' => $this->getRecord()])) .
                    ')'
                ),
        ];
    }

    public function refreshRepeaterItemData(
        array $arguments,
        Components\Repeater $repeater,
    ): void {
        $data = $repeater->getItemState($arguments['item']);
        $newData = $this->returnConfValue($data['valueType'], $data['dataSource'], $data['value']);
        $allData = $repeater->getState();
        $allData[$arguments['item']]['value'] = $newData;
        $repeater->state($allData);
    }

//    protected function getHeaderActions(): array
//    {
//        return [
//            Actions\Action::make('to-list')
//                ->button()
//                ->icon('heroicon-o-list-bullet')
//                ->label('Lista')
//                ->url(fn() => self::getResource()::getUrl('index')),
//            Actions\Action::make('to-products')
//                ->button()
//                ->icon('heroicon-o-queue-list')
//                ->label('Produkty')
//                ->url(fn() => self::getResource()::getUrl('add-item', ['record' => $this->record->id])),
//        ];
//    }


    public function getDataSource($type): array
    {
        $attributes = [];
        switch ($type) {
            case 'modelPartner':
                $attributes = [
                    'method:getAddressText' => 'Adres',
                ];
                break;
            case 'modelObject':
                $record = $this->getRecord();
                $config = $record->object?->metadata ?? [];
                $attributes['objectName:short_name'] = 'Skrót';
                foreach ($config as $configItem) {
                    $attributes['configName:' . $configItem['name']] = $configItem['name'];
                }
                break;
            case 'text':
            case 'number':
                $attributes = [
                    'userInput:value' => 'Wartość',
                ];
                break;
        }
        return $attributes;
    }

    public function setConfValue($value, $set, $get)
    {
        if (blank($value)) {
            $set('value', null);
            return null;
        }

        if (!array_key_exists($value, self::getResource()::getShortcuts())) {
            return null;
        }


        switch ($value) {
            case '{{location}}':
                    $set('value', $this->getRecord()->location) ?? '----';
                break;
            case '{{objects.short_name}}':
                $vals = $this->getRecord()->objects()->get()?->implode('short_name', ', ') ?? '---';
                $set('value', $vals);
                break;
            case '{{objects.name}}':
                $vals = $this->getRecord()->objects()->get()?->implode('name', ', ') ?? '---';
                $set('value', $vals);
                break;
            case '{{objects.area}}':
                $areas = [];
                $vals = $this->getRecord()->objects()->get()?->
                each(
                    function ($object) use (&$areas) {
                        $areas[] = $object->short_name . ': ' . $object->getMetaValue('area', 'unknown');
                    }
                ) ?? '---';
                $set('value', implode(', ', $areas));
                break;
            case '{{workshop.name}}':
                if ($this->getRecord()->location_model !== PartnerWork::class) {
                    return null;
                }

                $vals = PartnerWork::find($this->getRecord()->location_id)->name ?? '---';
                $set('value', $vals);
                break;
            case '{{workshop.address}}':
                if ($this->getRecord()->location_model !== PartnerWork::class) {
                    return null;
                }
                $vals = PartnerWork::find($this->getRecord()->location_id)->address ?? '---';
                $set('value', $vals);
                break;
            case '{{workshop.short_name}}':
                if ($this->getRecord()->location_model !== PartnerWork::class) {
                    return null;
                }
                $vals = PartnerWork::find($this->getRecord()->location_id)->short_name ?? '---';
                $set('value', $vals);
                break;
        }
    }

    public function returnConfValue($valueType, $dataSource, $currentValue)
    {
        if (blank($dataSource)) {
            return null;
        }
        switch ($valueType) {
            case 'text':
            case 'number':
                return $currentValue;
            case 'modelPartner':
                return $this->getRecord()->partner->getAddressText();
            case 'modelObject':
                $varName = explode(':', $dataSource);
                if (count($varName) < 2) {
                    return null;
                }
                return match ($varName[0]) {
                    'objectName' => $this->getRecord()->object->{$varName[1]} ?? '--',
                    'configName' => $this->getRecord()->object->getMetaValue($varName[1], '--'),
                    default => null
                };
        }
        return null;
    }
}
