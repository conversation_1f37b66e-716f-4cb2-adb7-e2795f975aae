<?php

namespace App\Filament\App\Resources\JobTaskResource\Pages;

use App\Filament\App\Resources\JobTaskResource;
use App\Models\JobTask;
use App\Repositories\JobTasksRepository;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditJobTask extends EditRecord
{
    protected static string $resource = JobTaskResource::class;

    /**
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return 'Edycja zlecenia';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
            ->using(fn(JobTask $record) => $this->handleRecordDelete($record))
            ->after(fn() => $this->redirect(self::getResource()::getUrl('index'))),
            Actions\Action::make('to-products')
                ->button()
                ->icon('heroicon-o-queue-list')
                ->label(fn(JobTask $record) => $record->isActive() ? 'Podgląd' : 'Podgląd')
                ->url(fn() => self::getResource()::getUrl('add-item', ['record' => $this->record->id])),
            Actions\Action::make('to-list')
                ->button()
                ->icon('heroicon-o-list-bullet')
                ->label('Lista')
                ->url(fn() => self::getResource()::getUrl('index')),
        ];
    }

    public function handleRecordDelete(Model|JobTask $record): bool
    {
        return JobTasksRepository::removeJobTask($record);
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        return self::$resource::mutateLocationBeforeSave($data);
    }

    protected function handleRecordUpdate(JobTask|Model $record, array $data): Model|JobTask
    {
        return parent::handleRecordUpdate($record, $data); // TODO: Change the autogenerated stub
    }

    protected function getFormActions(): array
    {
        return [
            $this->getSaveFormAction(),
        ];
    }
}
