<?php

namespace App\Filament\App\Resources\JobTaskResource\Pages;

use App\Filament\App\Resources\JobTaskResource;
use Filament\Actions;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;

class ViewJobTask extends ViewRecord
{
    protected static string $resource = JobTaskResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\Action::make('to-list')
                ->button()
                ->icon('heroicon-o-list-bullet')
                ->label('Lista')
                ->url(fn() => self::getResource()::getUrl('index')),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return parent::infolist($infolist);
    }
}
