<?php

namespace App\Filament\App\Resources\JobTaskResource\Pages;

use App\Filament\App\Resources\JobTaskResource;
use App\Models\JobTask;
use App\Models\Partner;
use App\Models\PartnerWork;
use App\Repositories\JobTasksRepository;
use Barryvdh\DomPDF\Facade\Pdf;
use Filament\Actions;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Infolists\Components\Actions\Action;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\HtmlString;

class AddJobItems extends ViewRecord
{
    protected static string $resource = JobTaskResource::class;

    /**
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return 'Podgląd zlecenia';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('to-list')
                ->button()
                ->icon('heroicon-o-list-bullet')
                ->label('Lista')
                ->url(fn() => self::getResource()::getUrl('index')),
            Actions\Action::make('pdf')
                ->button()
                ->icon('heroicon-o-printer')
                ->color('primary')
                ->label('PDF')
                ->action(function (Model $record) {
                    return response()->streamDownload(function () use ($record) {
                        echo Pdf::loadHtml(
                            Blade::render('print.zz', ['record' => $record])
                        )->stream();
                    }, 'zlecenie.pdf');
                }),
            Actions\Action::make('html')
                ->button()
                ->icon('heroicon-o-document-magnifying-glass')
                ->color('primary')
                ->label('HTML')
                ->url(
                    fn(Model $record) => route(
                        'previewPrint',
                        ['type' => 'zz', 'doc' => $record->id]
                    ),
                    true
                ),
            Actions\Action::make('accept')
                ->button()
                ->icon('heroicon-o-check')
                ->color('success')
                ->label('Zaakceptuj')
                ->modalHeading('Zaakceptuj zlecenie')
                ->visible(fn(Model $record) => $this->canAccept($record))
                ->form(fn(Form $form) => $form
                    ->schema([
                        Textarea::make('comment')
                            ->label('Komentarz')
                            ->default('')
                            ->maxLength(255),
                        Toggle::make('create_rw_doc')
                            ->label(
                                fn(Component $component) => $component->isDisabled() ?
                                    'Nie można utworzuć dokument RW. Brak produktów na magazynie' :
                                    'Utwórz dokument RW'
                            )
                            ->visible(fn(Model $record) => $record->items->count() > 0)
                            ->disabled(fn(Model $record) => !$record->canIssueWHDocument())
                            ->live()
                            ->default(false),
                        Textarea::make('doc_notes')
                            ->label('Komentarz do dokumentu')
                            ->default('')
                            ->visible(fn(Get $get) => $get('create_rw_doc') === true)
                            ->maxLength(255),
                        TextInput::make('doc_related_doc')
                            ->label('Dokumenty związane')
                            ->default(fn(Model $record) => $record->number)
                            ->visible(fn(Get $get) => $get('create_rw_doc') === true)
                            ->maxLength(100),
                    ]))
                ->action(fn(Model $record, array $data) => JobTasksRepository::finishJobTask($record, $data))
                ->after(function (\Livewire\Component $livewire) {
                    $livewire->dispatch('refreshItems');
                })
        ];
    }

    public function canAccept(Model $record): bool
    {
        if (!$record->isActive()) {
            return false;
        }
        if (blank($record->executor)) {
            return false;
        }
        return true;
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist->schema(
            [
                Section::make('Zlecenie')
                    ->heading(
                        fn($record) => 'Informacje o zleceniu' . ($record->isActive() ?
                                ' (niezrealizowane)' :
                                ' (zrealizowane)'
                            )
                    )
                    ->headerActions([
                        Action::make('edit')
                            ->button()
                            ->outlined()
                            ->label('Edycja')
                            ->icon('heroicon-o-cog-6-tooth')
                            ->url(self::getResource()::getUrl('edit', ['record' => $this->record->id])),
                    ])
                    ->collapsible()
                    ->collapsed()
                    ->schema([
                        Grid::make(4)
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Tytuł dokumetu'),
                                TextEntry::make('number')
                                    ->label('Numer dokumentu')
                                    ->default(null),
                                TextEntry::make('planned_date_at')
                                    ->label('Planowana Data realizacji')
                                    ->formatStateUsing(fn($state) => $state->format('Y-m-d')),
                                TextEntry::make('done_at')
                                    ->label('Data realizacji')
                                    ->default(null)
                                    ->formatStateUsing(fn($state) => $state?->format('Y-m-d') ?? '-'),
                            ]),
                        Grid::make(4)
                            ->schema([
                                TextEntry::make('partner.name'),
                                TextEntry::make('location')
                                    ->formatStateUsing(fn($state) => new HtmlString(nl2br($state)))
                                    ->label('Adres'),
                                TextEntry::make('location_id')
                                    ->label('Lokalizacja')
                                    ->formatStateUsing(
                                        function ($state, $record) {
                                            return match ($record->location_model) {
                                                PartnerWork::class => PartnerWork::find($state)->name ?? '-',
                                                Partner::class => Partner::find($state)->name ?? '-',
                                                default => '--',
                                            };
                                        }
                                    ),
                                TextEntry::make('objects')
                                    ->label('Obiekty')
                                    ->formatStateUsing(
                                        function ($state, $record) {
                                            return blank($state) ?
                                                '---' :
                                                $record->objects()->get()->implode('name', ', ');
                                        }
                                    ),
                            ]),
                        Grid::make(4)
                            ->schema([
                                IconEntry::make('status')
                                    ->label('Zrealizowane')
                                    ->getStateUsing(fn(JobTask|Model $record) => !$record->isActive())
                                    ->default(false),
                                TextEntry::make('executor.email')
                                    ->default('--')
                                    ->label('Wykonawca'),
                            TextEntry::make('comment')
                                ->label('Notatka')
                                ->columnSpan(2),
                            ]),
                    ]),
                Section::make('Konfiguracja')
                    ->headerActions([
                        Action::make('configure')
                            ->button()
                            ->outlined()
                            ->icon('heroicon-o-cog-6-tooth')
                            ->label('Edycja')
                            ->url(self::getResource()::getUrl('configure', ['record' => $this->record->id])),
                    ])
                    ->schema([
                        RepeatableEntry::make('configuration')
                            ->hiddenLabel()
                            ->getStateUsing(
                                fn(Model $record) => array_filter(
                                    $record->configuration ?? [],
                                    fn($item) => !$item['is_products_list']
                                )
                            )
                            ->schema([
                                TextEntry::make('name')
                                    ->hiddenLabel()
                                    ->formatStateUsing(fn($state) => new HtmlString('<b>' . $state . '</b>')),
                                TextEntry::make('value')
                                    ->hiddenLabel(),
                            ])
                            ->columns(2)
                            ->grid(2)
                    ])
                    ->collapsible()
                    ->collapsed()
            ]
        );
    }

    public function getRepeaterCurrentRecord($component): array
    {
        $index = explode('.', $component->getStatePath())[1] ?? 0;
        return $component
            ->getContainer()
            ->getParentComponent()
            ->getState()[$index] ?? [];
    }

    public function getRelationManagers(): array
    {
        return [
            JobTaskResource\RelationManagers\ItemsRelationManager::class
        ];
    }
}
