<?php

namespace App\Filament\App\Resources\JobTaskResource\Pages;

use App\Filament\App\Resources\JobTaskResource;
use App\Models\JobTaskTemplate;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Contracts\Support\Htmlable;

class CreateJobTask extends CreateRecord
{
    protected static string $resource = JobTaskResource::class;

    public function getHeading(): string | Htmlable
    {
        return 'Utwórz zlecenie';
    }


    public function form(Form $form): Form
    {
        $components = $form->getComponents(true);
        $element = Select::make('template')
            ->dehydrated(false)
            ->label('Użyj tytułu z szablonu')
            ->options(JobTaskTemplate::pluck('name', 'id'))
            ->suffixAction(
                Action::make('add')
                    ->iconButton()
                    ->icon('heroicon-s-plus')
                    ->action(
                        function ($state, Set $set) {
                            return filled($state) ?
                                $set('name', JobTaskTemplate::find($state)?->name ?? null) :
                                null;
                        }
                    )
            );


        array_unshift($components, $element);
        return $form->components($components);
    }


    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['installation'] = tenant()->id;
        $data['created_by'] = auth()->id();
        $data = self::getResource()::mutateLocationBeforeSave($data);
        return parent::mutateFormDataBeforeCreate($data);
    }

    protected function getRedirectUrl(): string
    {
        return self::getResource()::getUrl('add-item', ['record' => $this->getRecord()->id]);
    }
}
