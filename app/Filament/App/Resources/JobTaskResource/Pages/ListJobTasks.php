<?php

namespace App\Filament\App\Resources\JobTaskResource\Pages;

use App\Filament\App\Resources\JobTaskResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;

class ListJobTasks extends ListRecords
{
    protected static string $resource = JobTaskResource::class;

    public static function getNavigationLabel(): string
    {
        return 'Lista zleceń';
    }

    public function getHeading(): string | Htmlable
    {
        return 'Lista zleceń';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
