<?php

namespace App\Filament\App\Resources\ManufacturerResource\Pages;

use App\Filament\App\Resources\ManufacturerResource;
use App\Filament\Traits\HasBackToListButton;
use App\Helpers\Identifiers;
use App\Models\Tenant;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateManufacturer extends CreateRecord
{
    use HasBackToListButton;

    protected static string $resource = ManufacturerResource::class;

    protected ?string $heading = "Dodaj producenta";
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Dane podstawowe')
                    ->columns(2)
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(__('app._.name'))
                            ->required()
                            ->columnSpanFull()
                            ->maxLength(120),
                        Forms\Components\TextInput::make('phone')
                            ->label(__('app._.phone'))
                            ->maxLength(100),
                        Forms\Components\TextInput::make('email')
                            ->label(__('app._.email'))
                            ->email()
                            ->maxLength(60),
                        Forms\Components\TextInput::make('contact_name')
                            ->label(__('app.manufacturers.create.contact_name')),
                        Forms\Components\TextInput::make('website')
                            ->url()
                            ->label(__('app._.website')),
                        Forms\Components\Textarea::make('address')
                            ->label(__('app.manufacturers.create.address'))
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ]),
                Forms\Components\Toggle::make('is_active')
                    ->label(__('app._.is_active'))
                    ->default(true)
                    ->required(),
            ]);
    }

    protected function getRedirectUrl(): string
    {
        return self::getResource()::getUrl('edit', ['record' => $this->getRecord()->hash]);
    }

    protected function handleRecordCreation(array $data): Model
    {
        $data['hash'] = Identifiers::getRandomHash();
        $data['installation'] = auth()->user()->installation();
        return parent::handleRecordCreation($data);
    }

    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction(),
            $this->getCreateAnotherFormAction(),
            self::getBackToListFormAction(),
        ];
    }
}
