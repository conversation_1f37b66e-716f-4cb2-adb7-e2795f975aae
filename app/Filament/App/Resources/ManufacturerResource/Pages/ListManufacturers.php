<?php

namespace App\Filament\App\Resources\ManufacturerResource\Pages;

use App\Filament\App\Resources\ManufacturerResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListManufacturers extends ListRecords
{
    protected static string $resource = ManufacturerResource::class;

    protected ?string $heading = "Producenci";


    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
