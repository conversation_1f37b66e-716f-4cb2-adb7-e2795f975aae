<?php

namespace App\Filament\App\Resources\ManufacturerResource\Pages;

use App\Filament\App\Resources\ManufacturerResource;
use App\Filament\Traits\HasBackToListButton;
use App\Models\Manufacturer;
use App\Models\Tenant;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Resources\Pages\EditRecord;
use Webbingbrasil\FilamentCopyActions\Forms\Actions\CopyAction;

class EditManufacturer extends EditRecord
{
    use HasBackToListButton;

    protected static string $resource = ManufacturerResource::class;

    protected ?string $heading = "Edycja producenta";

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
            ->label('Usuń')
            ->modalHeading('Usuń producenta?')
            ->modalDescription('Ta czynność jest nieodwracalna!')
//            ->visible(fn($record) => $record->isEnabledToRemove())
            ->after(fn() => $this->redirect($this::getResource()::getUrl('index'))),
        ];
    }

    public function mount(int|string $record): void
    {
        $this->record = Manufacturer::hash($record)->first();

        $this->authorizeAccess();

        $this->fillForm();

        $this->previousUrl = url()->previous();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Dane podstawowe')
                    ->columns(2)
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(__('app._.name'))
                            ->columnSpanFull()
                            ->required()
                            ->maxLength(120),
                        Forms\Components\TextInput::make('phone')
                            ->label(__('app._.phone'))
                            ->maxLength(100),
                        Forms\Components\TextInput::make('email')
                            ->label(__('app._.email'))
                            ->email()
                            ->maxLength(60),
                        Forms\Components\TextInput::make('contact_name')
                            ->label(__('app.manufacturers.create.contact_name')),
                        Forms\Components\TextInput::make('website')
                            ->url()
                            ->label(__('app._.website')),
                        Forms\Components\Textarea::make('address')
                            ->label(__('app.manufacturers.create.address'))
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ]),
                Forms\Components\Toggle::make('is_active')
                    ->label(__('app._.is_active'))
                    ->default(true)
                    ->required(),
            ]);
    }

    protected function getFormActions(): array
    {
        return [
            $this->getSaveFormAction(),
            self::getBackToListFormAction(),
        ];
    }
}
