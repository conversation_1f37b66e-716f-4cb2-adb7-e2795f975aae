<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\JobTaskResource\Pages;
use App\Models\JobTask;
use App\Models\Partner;
use App\Models\PartnerObject;
use App\Models\PartnerWork;
use App\Repositories\JobTasksRepository;
use App\Repositories\PartnersRepository;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class JobTaskResource extends Resource
{
    protected static ?string $model = JobTask::class;

    public static array|\Illuminate\Support\Collection $objects = [];

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(3)
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Tytuł dokumentu')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('number')
                            ->label('Numer dokumentu')
                            ->maxLength(255)
                            ->default(null),
                        Forms\Components\DatePicker::make('planned_date_at')
                            ->label('Planowana Data realizacji')
                            ->native(false)
                            ->displayFormat('Y-m-d'),
                    ]),
                Forms\Components\Grid::make(4)
                    ->schema([
                        Forms\Components\Select::make('partner_id')
                            ->relationship('partner', 'name')
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                self::$objects = [];
                                $set('location_model', null);
                                $set('location', null);
                                $set('objects', null);
                            })
                            ->required(),
                        Forms\Components\Select::make('location_model')
                            ->label('Miejsce wykonania usługi')
                            ->disabled(fn(Forms\Get $get) => blank($get('partner_id')))
                            ->options(
                                fn(Forms\Get $get) => self::getLocationModelOptions($get)
                            )
                            ->live()
                            ->formatStateUsing(fn($state, Forms\Get $get, $record) => match ($state) {
                                PartnerWork::class => PartnerWork::class . '|' . $record->location_id,
                                default => $state,
                            })
                            ->afterStateUpdated(fn($state, Forms\Get $get, Forms\Set $set) => match ($state) {
                                'address' => null,
                                Partner::class => $set('location', Partner::find($get('partner_id'))->getAddressText()),
                                default => match ([$className, $id] = explode('|', $state)) {
                                    [PartnerWork::class, $id] => $set('location', PartnerWork::find($id)->address),
                                    default => null,
                                },
                            })
                            ->required(),
                        Forms\Components\Textarea::make('location')
                            ->maxLength(255)
                            ->rows(5)
                            ->label('Adres')
                            ->default(null),
                        Forms\Components\Select::make('objects')
                            ->label('Obiekty')
                            ->searchable(false)
                            ->multiple()
                            ->options(
                                fn(Forms\Get $get) => self::getLocationObjectsOptions($get)
                            )
                            ->default(null),
                    ]),
                Forms\Components\Grid::make(3)
                    ->schema([
                        Forms\Components\Select::make('executed_by')
                            ->label('Wykonawca')
                            ->relationship('executor', 'email')
                            ->required(fn(Forms\Get $get) => $get('status') === true)
                            ->default(null),
                        Forms\Components\DatePicker::make('done_at')
                            ->required(fn(Forms\Get $get) => $get('status') === true)
                            ->label('Data realizacji')
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->disabled(fn(Forms\Get $get) => $get('status') !== true),
                        Forms\Components\Textarea::make('comment')
                            ->label('Notatka'),
                    ]),
                Forms\Components\Grid::make(3)
                    ->schema([
                        Forms\Components\Toggle::make('status')
                            ->inline(false)
                            ->label('Zrealizowane')
                            ->disabled()
                            ->columnSpan(1)
                            ->formatStateUsing(fn(?Model $record) => !($record?->isActive() ?? true))
                            ->default(false),
                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('partner.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('executed_by')
                    ->formatStateUsing(fn($state, JobTask $record): string => $record->executor->email ?? '-')
                    ->sortable(),
                Tables\Columns\TextColumn::make('number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('location_c')
                    ->label('Adres')
                    ->default('')
                    ->formatStateUsing(
                        function ($state, JobTask $record): string {
                            return self::getTableRecordLocation($record);
                        }
                    ),
                Tables\Columns\IconColumn::make('status')
                    ->label('Zrealizowane')
                    ->getStateUsing(fn(Model $record) => !$record->status)
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_by')
                    ->formatStateUsing(fn($state, JobTask $record): string => $record->creator->email ?? '-')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\Action::make('Produkty')
                    ->label(__('app.warehouse_docs.list.actions.products_label'))
                    ->url(
                        fn(Model $record): string => self::getUrl(
                            'add-item',
                            ['record' => $record->id]
                        )
                    )
                    ->tooltip('Lista produktów')
                    ->iconButton()
                    ->icon('heroicon-o-queue-list'),
            ])
            ->recordUrl(fn($record) => self::getUrl('add-item', ['record' => $record->id]))
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->using(fn(Collection $records) => self::handleBulkDeleteAction($records)),
                ]),
            ]);
    }

    public static function handleBulkDeleteAction(Collection $records): void
    {
        foreach ($records as $record) {
            JobTasksRepository::removeJobTask($record);
        }
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListJobTasks::route('/'),
            'create' => Pages\CreateJobTask::route('/create'),
            'configure' => Pages\ConfigureJobTask::route('/{record}/configure'),
            'add-item' => Pages\AddJobItems::route('/{record}/add-item'),
            'view' => Pages\ViewJobTask::route('/{record}'),
            'edit' => Pages\EditJobTask::route('/{record}/edit'),
        ];
    }

    public static function getTableRecordLocation(JobTask $record)
    {
        if (filled($record->location)) {
            return Str::limit($record->location, 30);
        }

        if ($record->location_model === PartnerObject::class) {
            $object = PartnerObject::find($record->location_id);
            return $object?->address ?? 'Obiekt ' . $object?->short_name ?? 'Obiekt ' . $object?->name ?? '--';
        }

        if ($record->location_model === 'address') {
            return $record->location ?? '---';
        }
        return '-';
    }

    public static function mutateLocationBeforeSave(array $data): array
    {
        if (Str::of($data['location_model'])->contains(PartnerWork::class)) {
            $data['location_id'] = Str::of($data['location_model'])->afterLast('|')->toInteger();
            $data['location_model'] = PartnerWork::class;
        } elseif (Str::of($data['location_model'])->contains(Partner::class)) {
            $data['location_model'] = Partner::class;
            $data['location_id'] = $data['partner_id'];
        } else {
            $data['location_id'] = null;
        }

        return $data;
    }

    public static function getLocationModelOptions(Forms\Get $get): array
    {
        if (blank($get('partner_id'))) {
            return [];
        }

        $pws = PartnersRepository::getPartnerWorkshops($get('partner_id'))
            ->pluck('name', 'id')
            ->toArray();
        $keys = collect(array_keys($pws))->map(fn($key) => PartnerWork::class . '|' . $key)
            ->toArray();

        return [
            'address' => 'Inny adres',
            Partner::class => 'Adres partnera',
            ...array_combine($keys, $pws),
        ];
    }

    public static function getLocationObjectsOptions(Forms\Get $get): array
    {
        if (blank($get('partner_id'))) {
            return [];
        }
        if (empty(self::$objects)) {
            self::$objects = PartnersRepository::getPartnerObjects($get('partner_id'), Partner::class);
        }

        if (filled($get('location_model')) && Str::of($get('location_model'))->contains(PartnerWork::class)) {
            $id = Str::of($get('location_model'))->afterLast('|')->toInteger();
            $objects = self::$objects->filter(fn($object) => $object->partner_work_id === $id);
        } else {
            $objects = self::$objects;
        }

        return $objects->pluck('name', 'id')->toArray();
    }


    public static function getShortcuts(): array
    {
        return [
            '{{location}}' => 'Lokalizacja zlecenia',
            '{{objects.short_name}}' => "Skrót obiektu",
            '{{objects.name}}' => "Nazwa obiektu",
            '{{objects.area}}' => "Powierzchnia obiektu",
            '{{workshop.name}}' => 'Nazwa zakładu',
            '{{workshop.address}}' => 'Adres zakładu',
            '{{workshop.short_name}}' => "Skrót zakładu",
        ];
    }
}
