<?php

namespace App\Filament\App\Resources\PurchaseDocResource\Pages;

use App\Enums\SystemModules;
use App\Filament\App\Resources\PurchaseDocResource;
use App\Models\PurchaseDoc;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Collection;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use pxlrbt\FilamentExcel\Actions\Pages\ExportAction;
use pxlrbt\FilamentExcel\Columns\Column;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use function Livewire\invade;

class ListPurchaseDocs extends ListRecords
{
    protected static string $resource = PurchaseDocResource::class;

    public $currentRecord = PurchaseDoc::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationLabel(): string
    {
        return __('app.purchase_docs.navigation.list-label');
    }

    public function getBreadcrumb(): ?string
    {
        return __('app.purchase_docs.navigation.list-label');
    }

    public function getHeading(): string|Htmlable
    {
        return __('app.purchase_docs._.heading');
    }

    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return tenant()?->hasModule(SystemModules::PURCHASE_INVOICES) ?? false;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            ExportAction::make('tableHeader')
                ->exports([
                    ExcelExport::make()
                        ->askForWriterType()
                        ->withFilename(function () {
                            return 'faktury_' . now('Europe/Warsaw')->format('Y-m-d_H-i');
                        })
                        ->withColumns(function () {
                            return $this->createFieldMappingFromTable()
                                ->map(
                                    fn(Column $column) => in_array(
                                        $column->getName(),
                                        ['gross', 'vat_amount', 'net_value']
                                    ) ?
                                        $column
                                            ->format(NumberFormat::FORMAT_NUMBER_00)
                                            ->formatStateUsing(fn($state) => number_format($state, 2, '.', '')) :
                                        $column
                                )
                                ->toArray();
                        })
                        ->fromTable()
                ])
        ];
    }

    public function triggerAction(PurchaseDoc $tradeDoc): void
    {
        $this->currentRecord = $tradeDoc;
        $this->mountAction('viewMyRecordAction');
    }

    public function viewMyRecordAction()
    {
        $action = Actions\Action::make('viewMyRecord')
            ->record($this->currentRecord)
            ->slideOver(true)
            ->modalHeading(fn(PurchaseDoc $record) => $record->type->label() . ': ' . $record->full_doc_number)
            ->infolist(PurchaseDocResource\Forms\PurchaseDocView::getInfolistSchema($this->currentRecord));
        $action->modalFooterActions([
            $action->getModalCancelAction()->label('Zamknij'),
            Tables\Actions\Action::make('additem')
                ->url(
                    function (PurchaseDoc $record) {
                        return self::getResource()::getUrl('add-item', ['record' => $record->getKey()]);
                    }
                )
                ->label('Edycja')
                ->icon('heroicon-o-queue-list'),
            Tables\Actions\Action::make('print_doc')
                ->label('PDF')
                ->icon('heroicon-o-printer')
                ->url(
                    fn(PurchaseDoc $record) => route('print', [
                        'doctype' => strtolower($record->type->getGeneralType()->value),
                        'doc' => $record->getKey(),
                        'output' => 'pdf'
                    ]),
                    true
                ),
        ]);
        return $action;
    }

    protected function createFieldMappingFromTable(): Collection
    {
        $livewire = $this;

        if ($livewire instanceof HasTable) {
            $columns = collect($livewire->getTable()->getColumns());
        } else {
            $table = $this->getResourceClass()::table(new Table);
            $columns = collect($table->getColumns());
        }

        return $columns
            ->when(
                $livewire->getTable()->hasToggleableColumns(),
                fn($collection) => $collection->reject(
                    fn(Tables\Columns\Column $column) => $livewire->isTableColumnToggledHidden($column->getName())
                )
            )
            ->mapWithKeys(function (Tables\Columns\Column $column) {
                $clonedCol = clone $column;

                $invadedColumn = invade($clonedCol);

                $exportColumn = Column::make($column->getName())
                    ->heading($column->getLabel())
                    ->getStateUsing($invadedColumn->getStateUsing)
                    ->tableColumn($clonedCol);

                rescue(fn() => $exportColumn->formatStateUsing($invadedColumn->formatStateUsing), report: false);

                return [
                    $column->getName() => $exportColumn,
                ];
            });
    }
}
