<?php

namespace App\Filament\App\Resources\PurchaseDocResource\Pages;

use App\Enums\DocumentTypes;
use App\Enums\SystemModules;
use App\Enums\TradeDocVatMethod;
use App\Filament\App\Resources\PurchaseDocResource;
use App\Filament\Traits\HasBackToListButton;
use App\Models\PurchaseDoc;
use App\Repositories\PurchaseDocsRepository;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Arr;
use Livewire\Attributes\On;

class CreatePurchaseDoc extends CreateRecord
{
    use HasBackToListButton;

    protected static string $resource = PurchaseDocResource::class;
    public null|string $currentType = null;

    public static function getNavigationIcon(): string|Htmlable|null
    {
        return 'heroicon-o-document-text';
    }

    public static function getNavigationLabel(): string
    {
        return __('app.purchase_docs.navigation.create-purchase-label');
    }

    public function getHeading(): string|Htmlable
    {
        return __('app.purchase_docs.create.page.heading');
    }

    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return tenant()?->hasModule(SystemModules::PURCHASE_INVOICES) ?? false;
    }

    public function form(Form $form): Form
    {
        return PurchaseDocResource\Forms\PurchaseDocForm::createForm($form);
    }

    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction(),
            self::getBackToListFormAction(),
        ];
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['type'] = DocumentTypes::tryFrom($data['type']);
        $data['buyer_id'] = tenant()->id;
        $data['issuer_id'] = $data['seller_id'];
        return parent::mutateFormDataBeforeCreate($data);
    }

    public function handleRecordCreation(array $data): PurchaseDoc
    {
//        dd($data);
        $meta = $data['meta'] ?? [];
        unset($data['meta']);
        $prepaidInvoices = [];
        if ($data['is_final_invoice'] ?? false) {
            $prepaidInvoices = $data['prepaidInvoices'] ?? [];
        }
        unset($data['prepaidInvoices']);
        $record = new PurchaseDoc(Arr::except($data, 'sellerdata'));
        $record->installation = tenant()->id;
        $record->creator_id = auth()->user()->id;
        $record->issuer_id = $record->seller_id;
        if ($record->type === DocumentTypes::FZUP) {
            $record->vat_method = TradeDocVatMethod::BASE_ON_GROSS;
        }
        $record->save();
        PurchaseDocsRepository::createMetaOnModel($record, $meta, $data);
        if (filled($prepaidInvoices)) {
            PurchaseDocsRepository::finalInvoiceProcess($record, $prepaidInvoices);
        }
        return $record;
    }

    protected function getRedirectUrl(): string
    {
        return self::getResource()::getUrl('add-item', ['record' => $this->getRecord()->getKey()]);
    }

    #[On('type-updated')]
    public function onTypeUpdated($value)
    {
        $this->currentType = $value;
    }
}
