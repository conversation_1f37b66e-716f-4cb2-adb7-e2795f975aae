<?php

namespace App\Filament\App\Resources\PurchaseDocResource\Pages;

use App\Enums\SystemModules;
use App\Filament\App\Resources\PurchaseDocResource;
use App\Models\DTOBankData;
use App\Models\DTOTradeDocMeta;
use App\Models\PurchaseDoc;
use App\Repositories\PurchaseDocsRepository;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class EditPurchaseDoc extends EditRecord
{
    protected static string $resource = PurchaseDocResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->visible(fn($record) => PurchaseDocsRepository::canDocBeRemoved($record))
                ->action(function (PurchaseDoc $record, Action $action) {
                    if (PurchaseDocsRepository::deleteTradeDoc($record)) {
                        $action->success();
                        return true;
                    }
                    $action->failureNotificationTitle('Błąd kasowania: ' . PurchaseDocsRepository::$error);
                    $action->failure();
                    return false;
                })
        ];
    }

    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return sprintf('Edytuj %s', $this->record->full_doc_number);
    }

    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return tenant()?->hasModule(SystemModules::PURCHASE_INVOICES) ?? false;
    }

    public function mount(int|string $record): void
    {
        $this->record = $this->resolveRecord($record);

        $this->authorizeAccess();

        $meta = $this->record->getMeta();
        $this->record->sellerdata = Arr::only($meta->seller_address, ['name', 'address', 'postcode', 'city', 'vat_id']);
        $this->fillForm();

        $this->previousUrl = url()->previous();
    }

    public function form(Form $form): Form
    {
        return PurchaseDocResource\Forms\PurchaseDocForm::editForm($form);
    }

    protected function getFormActions(): array
    {
        return [
            $this->getSaveFormAction(),
            $this->getCancelFormAction()
                ->label('Lista')
                ->icon('heroicon-o-list-bullet')
                ->url(fn() => self::getResource()::getUrl('index')),
            Action::make('products')
                ->label('Produkty')
                ->icon('heroicon-o-queue-list')
                ->color('gray')
                ->url(fn() => self::getResource()::getUrl('add-item', ['record' => $this->record->getKey()]))
        ];
    }

    protected function handleRecordUpdate(Model|PurchaseDoc $record, array $data): Model
    {
        $meta = $data['meta'] ?? [];
        $options = $data['options'] ?? [];
        unset($data['meta'], $data['options']);
        $record = parent::handleRecordUpdate($record, Arr::except($data, 'sellerdata'));
        $metaData = DTOTradeDocMeta::make($record->meta);
        $metaData->bank_data = DTOBankData::make($meta)->toArray();
        $issuer = Arr::except($record->seller?->getAttributes() ?? [], ['id', 'installation']);
        $buyer = Arr::except(tenant()?->getAttributes() ?? [], ['id', 'config']);
        $metaData->setUpIssuer($issuer, $data['sellerdata'] ?? []);
        $metaData->setUpSeller($issuer, $data['sellerdata'] ?? []);
        $metaData->setUpBuyer($buyer, $data['buyerdata'] ?? []);
        $metaData->setOption('reverse_charge', $meta['options']['reverse_charge'] ?? false);
        $metaData->setOption('mpp', $meta['options']['mpp'] ?? false);
        $metaData->setNote($meta['note'] ?? null);
        $metaData->corrected_doc = $meta['corrected_doc'] ?? [];
        $record->meta->fill(['meta' => $metaData->toArray()]);
        $record->meta->save();
        PurchaseDocsRepository::finalInvoiceProcess($record, $data['prepaidInvoices'] ?? [], 'edit');
        $this->fill($record);
        return $record;
    }
}
