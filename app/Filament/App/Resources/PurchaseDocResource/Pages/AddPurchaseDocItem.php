<?php

namespace App\Filament\App\Resources\PurchaseDocResource\Pages;

use App\Enums\DocumentTypes as DocType;
use App\Enums\SystemModules;
use App\Filament\App\Resources\PurchaseDocResource;
use App\Filament\App\Resources\TradeDocResource;
use App\Models\PurchaseDoc;
use App\Models\TradeDoc;
use App\Models\WarehouseDoc;
use App\Repositories\DocumentSeriesRepository;
use App\Repositories\PurchaseDocsRepository;
use App\Repositories\TradeDocsRepository;
use App\Repositories\WarehouseDocsRepository;
use Barryvdh\DomPDF\Facade\Pdf;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Actions\EditAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Components\Fieldset;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Livewire\Attributes\On;

class AddPurchaseDocItem extends ViewRecord
{
    use InteractsWithInfolists;
    use InteractsWithFormActions;

    protected static string $resource = PurchaseDocResource::class;

    public string $transaction_id;
    public bool $accepted = false;

    public ?string $previousUrl = null;

    public array $items = [];


    protected function getForms(): array
    {
        return [
            'form',
        ];
    }

    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return tenant()?->hasModule(SystemModules::PURCHASE_INVOICES) ?? false;
    }


    public function getHeading(): string|Htmlable
    {
        $title = $this->record->type->label() . ' ' . $this->record->full_doc_number;

        if ($this->record->has_correction) {
            $title .= '  <small style="color: orangered;">(wystawiona korekta)</small>';
        }

        return new HtmlString($title);
    }

    protected function loadParentRecord($record)
    {
        $this->record = TradeDoc::where('transaction_id', $record)->first();
        if (empty($this->record)) {
            abort(404);
        }
        $this->accepted = (bool)$this->record->is_accepted;
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return parent::infolist($infolist)
            ->columns(3)
            ->record($this->record)
            ->schema([
                Grid::make($this->record->type === DocType::FZK ? 4 : 3)
                    ->columnSpanFull()
                    ->schema([
                        Fieldset::make('Sprzedawca')
                            ->extraAttributes(fn($record) => match ($record->type) {
                                DocType::FZK => ['class' => 'fi-fieldset-two-c'],
                                default => []
                            }, true)
                            ->columnSpan(1)
                            ->extraAttributes(['class' => 'h-full'])
                            ->columns(1)
                            ->schema([
                                TextEntry::make('meta.getSellerData')
                                    ->inlineLabel()
                                    ->state(
                                        new HtmlString($this->record->meta?->getSellerData('html') ?? 'Brak danych')
                                    )
                                    ->label(__('app.purchase_docs.create.seller_data') . ':'),
//                                TextEntry::make('related')
//                                    ->label(__('app.purchase_docs.create.related_doc'))
//                                    ->inlineLabel()
//                                    ->state(fn(Model $record) => $this->relatedDocumentState($record))
//                                    ->url(fn(Model $record) => $this->relatedDocumentUrl($record)),
                                TextEntry::make('has_correction')
                                    ->label('Korekta?')
                                    ->inlineLabel()
                                    ->hidden(fn($state) => $state === false)
                                    ->formatStateUsing(
                                        function ($state, Model $record) {
                                            if ($state) {
                                                return PurchaseDoc::where('source_id', $record->uuid)
                                                    ->first()->full_doc_number;
                                            }
                                            return 'Nie';
                                        }
                                    )
                                    ->url(
                                        fn(Model $record) => self::getResource()::getUrl(
                                            'add-item',
                                            [
                                                'record' => $record->uuid
                                            ]
                                        )
                                    ),
                            ]),
                        Fieldset::make(__('app.purchase_docs.create.payment_type'))
                            ->columnSpan(1)
                            ->extraAttributes(fn($record) => match ($record->type) {
                                DocType::FZK => ['class' => 'fi-fieldset-two-c h-full'],
                                default => ['class' => 'h-full']
                            }, true)
                            ->columns(1)
                            ->schema([
                                TextEntry::make('currency')
                                    ->inlineLabel()
                                    ->label(__('app.purchase_docs.create.currency') . ':')
                                    ->default('PLN'),
                                TextEntry::make('exchange_rate')
                                    ->inlineLabel()
                                    ->label(__('app.purchase_docs._.exchange_rate') . ':')
                                    ->visible(fn($record): bool => $record->currency !== 'PLN')
                                    ->formatStateUsing(
                                        function ($state, TradeDoc $record): string {
                                            return $state . ' (' . $record->currency_rate_date . ')';
                                        }
                                    )
                                    ->default('PLN'),
                                TextEntry::make('meta.bank_account')
                                    ->inlineLabel()
                                    ->tooltip(
                                        fn($state, $record) => $record->meta?->getBankDataItem('bank_name') ?? ''
                                    )
                                    ->state(
                                        $this->record->meta?->getBankDataItem('bank_account') ?? 'Brak danych'
                                    )
                                    ->label(__('app.purchase_docs.create.bank_account') . ':'),
                                TextEntry::make('payment_type')
                                    ->inlineLabel()
                                    ->formatStateUsing(fn($state) => $state->label())
                                    ->label(__('app.purchase_docs.create.payment_type') . ':'),
                                TextEntry::make('is_paid')
                                    ->inlineLabel()
                                    ->formatStateUsing(
                                        fn($state) => $state ? $this->record->payment_date->format('Y-m-d') : 'Nie'
                                    )
                                    ->label(__('app.purchase_docs.create.is_paid') . ':'),

                            ]),
                        Fieldset::make(__('app.purchase_docs.create.dates'))
                            ->extraAttributes(fn($record) => match ($record->type) {
                                DocType::FZK => ['class' => 'fi-fieldset-two-c h-full'],
                                default => ['class' => 'h-full']
                            }, true)
                            ->columnSpan(1)
                            ->columns(1)
                            ->schema([
                                TextEntry::make('issued_at')
                                    ->columnSpanFull()
                                    ->state(fn(Model $record) => match (filled($record->issued_at)) {
                                        false => null,
                                        default => $record->issued_at->format('Y-m-d')
                                    })
                                    ->inlineLabel()
                                    ->label(__('app.purchase_docs.create.doc_date')),
                                TextEntry::make('sells_date')
                                    ->dateTime('Y-m-d', 'Europe/Warsaw')
                                    ->inlineLabel()
                                    ->label(__('app.purchase_docs.create.sells_date')),
                                TextEntry::make('payment_due_date')
                                    ->dateTime('Y-m-d', 'Europe/Warsaw')
                                    ->inlineLabel()
                                    ->label(__('app.purchase_docs.create.payment_due_date'))
                                    ->default(''),
                                TextEntry::make('created_at')
                                    ->dateTime('Y-m-d H:i:s', 'Europe/Warsaw')
                                    ->default('')
                                    ->inlineLabel()
                                    ->label(__('app.purchase_docs.create.created_at')),
                                TextEntry::make('cancelled_by')
                                    ->state(fn(Model $record) => $record->cancelledby->email)
                                    ->default('')
                                    ->inlineLabel()
                                    ->visible(fn(Model $record) => $record->cancelled_at)
                                    ->label('Anulowany przez'),

                            ]),
                        Fieldset::make('Dokument korygowany')
                            ->extraAttributes(['class' => 'fi-fieldset-two-c h-full'], true)
                            ->columnSpan(1)
                            ->columns(1)
                            ->inlineLabel(true)
                            ->visible(fn($record) => $record->type === DocType::FZK)
                            ->schema([
                                TextEntry::make('meta.corrected_doc.full_doc_number')
                                    ->html(true)
                                    ->state(
                                        fn ($record) => $record->getMeta()->corrected_doc['full_doc_number'] ?? ''
                                    )
                                    ->columnSpanFull()
                                    ->columns(1)
                                    ->label('Numer'),
                                TextEntry::make('meta.corrected_doc.issued_at')
                                    ->html(true)
                                    ->state(
                                        fn ($record) => $record->getMeta()->corrected_doc['issued_at'] ?? ''
                                    )
                                    ->columnSpanFull()
                                    ->label('Data wystawienia'),
//                                TextEntry::make('meta.corrected_doc.sells_date')
//                                    ->html(true)
//                                    ->state(
//                                        fn ($record) => $record->getMeta()->corrected_doc['sells_date'] ?? ''
//                                    )
//                                    ->columnSpanFull()
//                                    ->label('Data wystawienia'),
                                TextEntry::make('meta.corrected_doc.notes')
                                    ->html(true)
                                    ->state(
                                        fn ($record) => $record->getMeta()->corrected_doc['notes'] ?? ''
                                    )
                                    ->columnSpanFull()
                                    ->label('Przyczyna'),
                            ]),
                    ]),
                Grid::make(9)
                    ->columnSpanFull()
                    ->schema([
                        Fieldset::make(__('app.purchase_docs.create.notes'))
                            ->columnSpan(
                                function ($record) {
                                    return (
                                        $record->type === DocType::FZV ||
                                        $record->type === DocType::FZK ||
                                        $record->type === DocType::FZP
                                    ) ? 2 : 5;
                                }
                            )
                            ->extraAttributes(['class' => 'h-full'])
                            ->schema([
                                TextEntry::make('meta.notes')
                                    ->html(true)
                                    ->state(
                                        fn($record) => $record->getMeta()->getNote()
                                    )
                                    ->columnSpanFull()
                                    ->hiddenLabel()
                                    ->label(__('app.purchase_docs.create.notes')),
                            ]),

                        Fieldset::make('options')
                            ->columnSpan(3)
                            ->extraAttributes(['class' => 'h-full'])
                            ->visible(
                                fn($record) => $this->isOptionsVisible($record)
                            )
                            ->schema([
                                ViewEntry::make('options')
                                    ->columnSpan(2)
                                    ->state(fn($record) => $record)
                                    ->view('filament.app.infolists.options_pd')
                                    ->label(__('app.purchase_docs.create.options')),
                            ])
                            ->label(__('app.purchase_docs.create.options')),
                        Fieldset::make(__('app.purchase_docs.create.vat_summary'))
                            ->columnSpan(4)
                            ->extraAttributes(['class' => 'h-full'])
                            ->visible(
                                fn($record) => self::isVatSummaryVisible($record)
                            )
                            ->columns(1)
                            ->schema([
                                ViewEntry::make('vatsummary')
                                    ->view(
                                        self::getVatSummaryView($this->record)
                                    )
                                    ->state(
                                        fn ($record) => self::getVatSummaryState($record)
                                    ),
                            ])
                    ])
            ]);
    }

    public static function getVatSummaryView(Model|TradeDoc $record): string
    {
        return match ($record->type) {
            DocType::FZK => 'filament.app.infolists.vatsummary_fvk',
            DocType::FZUP => 'filament.app.infolists.vatsummary_faup',
            default => 'filament.app.infolists.vatsummary',
        };
    }

    public static function getVatSummaryState(Model|TradeDoc $record): mixed
    {
        return match ($record->type) {
            DocType::FZK => $record->getMeta()->toArray(),
            DocType::FZUP => $record,
            default => $record->getMeta()->toArray()['vat'],
        };
    }

    public static function isVatSummaryVisible(Model|PurchaseDoc $record): bool
    {
        $visible = [
            DocType::FZV,
            DocType::FZK,
            DocType::FZP,
            DocType::FZUP,
        ];
        return in_array($record->type, $visible, true);
    }

    public function isOptionsVisible(Model|PurchaseDoc $record): bool
    {
        $visible = [
            DocType::FZV,
            DocType::FZK,
            DocType::FZP
        ];
        return in_array($record->type, $visible, true);
    }

    public function relatedDocumentState(PurchaseDoc|Model $record): ?string
    {
        return $record->getMeta()->corrected_doc['full_doc_number'] ?? 'brak';
    }

    public function relatedDocumentUrl(PurchaseDoc|Model $record): string
    {
        return '';
    }

    public function getHeaderActions(): array
    {
        return [
            Action::make('Lista')
                ->button()
                ->icon('heroicon-o-list-bullet')
                ->url(function () {
                    return $this->previousUrl ?? self::getResource()::getUrl('index');
                }),
            Action::make('print_doc')
                ->label('PDF')
                ->button()
                ->icon('heroicon-o-printer')
                ->url(
                    route('print', [
                        'doctype' => strtolower($this->record->type->getGeneralType()->value),
                        'doc' => $this->record->getKey(),
                        'output' => 'pdf'
                    ]),
                    true
                ),
            Action::make('print_hmtl')
                ->label('HTML')
                ->button()
                ->icon('heroicon-o-printer')
                ->url(
                    route('print', [
                        'doctype' => strtolower($this->record->type->getGeneralType()->value),
                        'doc' => $this->record->getKey(),
                        'output' => 'html'
                    ]),
                    true
                ),
            PurchaseDocResource\Forms\PurchaseDocForm::getHeaderPaymentAction(),
            ActionGroup::make([
                EditAction::make()
                    ->url(
                        fn(Model $record) => self::getResource()::getUrl('edit', ['record' => $record])
                    ),
            ]),
        ];
    }


    #[On('itemCreated')]
    public function onItemCreated()
    {
        $this->render();
    }

    #[On('itemRemoved')]
    public function onItemRemoved()
    {
        $this->render();
    }

    #[On('itemUpdated')]
    public function onItemUpdate()
    {
        $this->render();
    }


    public function getRelationManagers(): array
    {
        return [
            PurchaseDocResource\RelationManagers\ItemsRelationManager::class
        ];
    }
}
