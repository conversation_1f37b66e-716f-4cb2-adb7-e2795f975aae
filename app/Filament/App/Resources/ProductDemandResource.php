<?php

namespace App\Filament\App\Resources;

use App\Enums\ProductDemandsStatuses;
use App\Enums\SystemModules;
use App\Filament\App\Resources\ProductDemandResource\Pages;
use App\Models\ProductDemand;
use App\Models\User;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\HtmlString;

class ProductDemandResource extends Resource
{
    protected static ?string $model = ProductDemand::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationLabel(): string
    {
        return __('app.product_demands.page.listNavigationLabel');
    }

    public static function getNavigationGroup(): ?string
    {
        return match (auth()->user()->isTenantAdmin()) {
            true => __('app._.warehouse_state'),
            default => null
        };
    }

    public static function shouldRegisterNavigation(): bool
    {
        return match (auth()->user()->isTenantAdmin()) {
            true => true,
            default => auth()->user()->hasWarehouse()
        } && (tenant()?->hasModule(SystemModules::WAREHOUSE) ?? false);
    }


    public static function table(Table $table): Table
    {

        return match (auth()->user()->isTenantAdmin()) {
            default => self::AdminTable($table),
            false => self::EmployeeTable($table),
        };
    }


    public static function Employeetable(Table $table)
    {
        return $table
            ->columns([
                Tables\Columns\Layout\Split::make([
                    Tables\Columns\TextColumn::make('product.name')
                        ->label(__('app.product_demands.list.product'))
                        ->formatStateUsing(
                            fn($state) => new HtmlString('<strong>' . __('app.product_demands.list.product') . ': </strong>' . $state)
                        ),
                    Tables\Columns\TextColumn::make('amount')
                        ->label(__('app.product_demands.list.amount'))
                        ->formatStateUsing(
                            fn($state) => new HtmlString('<strong>' . __('app.product_demands.list.amount') . ': </strong>' . $state)
                        ),
                    Tables\Columns\TextColumn::make('demand_at')
                        ->label(__('app.product_demands.list.demand_at'))
                        ->formatStateUsing(
                            fn($state) => new HtmlString('<strong>' . __('app.product_demands.list.demand_at') . ': </strong>' . $state->format('Y-m-d'))
                        ),
                    Tables\Columns\TextColumn::make('status')
                        ->formatStateUsing(fn($state) => $state->label() ?? 'UNKNOWN')
                        ->label(__('app.product_demands.list.status')),
                    Tables\Columns\TextColumn::make('created_at')
                        ->formatStateUsing(
                            fn($state) => new HtmlString('<strong>' . __('app._.created_at') . ': </strong>' .
                                $state->setTimezone(new \DateTimeZone('Europe/Warsaw'))->format('Y-m-d H:i')
                            )
                        )
                        ->label(__('app._.created_at')),
                ])->from('md')
            ])
            ->defaultSort('demand_at', 'asc')
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(ProductDemandsStatuses::class)
                    ->default(1),
                Tables\Filters\Filter::make('demand_at')
                    ->form([
                        Forms\Components\DatePicker::make('demand_to')
                            ->minDate(now()->format('Y-m-d'))
                            ->displayFormat('Y-m-d')
                            ->native(false)
                            ->formatStateUsing(fn($state) => match ($state) {
                                null => null,
                                default => (new Carbon($state))->format('Y-m-d')
                            })
                            ->time(false)
                            ->seconds(false)
                            ->label('Data wymagania do')
                    ])
                    ->indicateUsing(fn($state) => match (filled($state['demand_to'])) {
                        false => null,
                        default => 'Data Wymagania do: ' . (new Carbon($state['demand_to']))->format('Y-m-d')
                    })
                    ->query(function (Builder $query, $state) {
                        return match (filled($state['demand_to'])) {
                            false => $query,
                            default => $query->whereDate('demand_at', '<=', (new Carbon($state['demand_to']))->format('Y-m-d'))
                        };
                    })
            ])
            ->filtersLayout(Tables\Enums\FiltersLayout::Modal)
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('')
                    ->icon(null),
                Tables\Actions\EditAction::make()
                    ->modalHeading(__('app.product_demands.page.edit_heading'))
                    ->using(function ($data, ProductDemand $record) {
                        self::updateDemand($record, $data);
                    }),
                Tables\Actions\DeleteAction::make()
                    ->modalHeading(__('app.product_demands.page.delete_heading')),
            ])
            ->actionsAlignment('right')
            ->modifyQueryUsing(fn(Builder $query) => match (auth()->user()->isTenantAdmin()) {
                true => $query,
                default => $query->where('user_id', auth()->user()->id)
            });
    }

    public static function AdminTable(Table $table)
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->formatStateUsing(fn(User|Model $record) => $record->user->fullName())
                    ->label(__('app.product_demands.list.user_name'))
                    ->hidden(fn() => !(auth()->user()?->isTenantAdmin() ?? false))
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.name')
                    ->label(__('app.product_demands.list.product'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->label(__('app.product_demands.list.amount'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('demand_at')
                    ->label(__('app.product_demands.list.demand_at'))
                    ->date('Y-m-d')
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->formatStateUsing(fn($state) => $state->label() ?? 'UNKNOWN')
                    ->label(__('app.product_demands.list.status'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('app._.created_at'))
                    ->dateTime('y-m-d H:i', 'Europe/Warsaw')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: false),
            ])
            ->defaultSort('demand_at', 'asc')
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(ProductDemandsStatuses::class)
                    ->default(1),
                Tables\Filters\Filter::make('demand_at')
                    ->form([
                        Forms\Components\DatePicker::make('demand_to')
                            ->minDate(now()->format('Y-m-d'))
                            ->displayFormat('Y-m-d')
                            ->native(false)
                            ->formatStateUsing(fn($state) => match ($state) {
                                null => null,
                                default => (new Carbon($state))->format('Y-m-d')
                            })
                            ->time(false)
                            ->seconds(false)
                            ->label('Data wymagania do')
                    ])
                    ->indicateUsing(fn($state) => match (filled($state['demand_to'])) {
                        false => null,
                        default => 'Data Wymagania do: ' . (new Carbon($state['demand_to']))->format('Y-m-d')
                    })
                    ->query(function (Builder $query, $state) {
                        return match (filled($state['demand_to'])) {
                            false => $query,
                            default => $query->whereDate('demand_at', '<=', (new Carbon($state['demand_to']))->format('Y-m-d'))
                        };
                    })
            ])
            ->filtersLayout(Tables\Enums\FiltersLayout::AboveContent)
            ->actions([
//                Tables\Actions\ViewAction::make()
//                    ->label('')
//                    ->icon(null),
                Tables\Actions\EditAction::make()
                    ->modalHeading(__('app.product_demands.page.edit_heading'))
                    ->using(function ($data, ProductDemand $record) {
                        self::updateDemand($record, $data);
                    }),
                Tables\Actions\DeleteAction::make()
                    ->modalHeading(__('app.product_demands.page.delete_heading')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('status')
                        ->label('Zmień status')
                        ->icon('heroicon-o-arrows-right-left')
                        ->form([
                            Forms\Components\Select::make('set_status')
                                ->label('Zmień status na:')
                                ->options(ProductDemandsStatuses::class)
                        ])
                        ->action(function ($data, EloquentCollection $records) {
                            if(!filled($data['set_status'])) {
                                return;
                            }
                            self::changeBulkStatus($data, $records);
                        })
                        ->modalWidth('2xl')
                ]),
            ])
            ->modifyQueryUsing(fn(Builder $query) => match (auth()->user()->isTenantAdmin()) {
                true => $query,
                default => $query->where('user_id', auth()->user()->id)
            });
    }


    /**
     * @param $data
     * @param EloquentCollection<ProductDemand> $records
     * @return void
     */
    public static function changeBulkStatus($data, EloquentCollection $records)
    {
        $records->each(fn($record) => $record->update(['status' => $data['set_status']]));
    }

    public static function updateDemand(ProductDemand $record, $data)
    {

        $record->update($data);
        return $record;
    }


    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageProductDemands::route('/')
        ];
    }
}
