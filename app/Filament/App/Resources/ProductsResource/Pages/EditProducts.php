<?php

namespace App\Filament\App\Resources\ProductsResource\Pages;

use App\Enums\SystemModules;
use App\Enums\VatRates;
use App\Enums\WarehouseItemTypes;
use App\Filament\App\Resources\ManufacturerResource;
use App\Filament\App\Resources\ProductsResource;
use App\Filament\Traits\HasBackToListButton;
use App\Models\Manufacturer;
use App\Models\Products;
use App\Repositories\ManufacturersRepository;
use App\Repositories\WarehouseRepository;
use App\Services\Filters;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class EditProducts extends EditRecord
{
    use HasBackToListButton;

    protected static string $resource = ProductsResource::class;

    protected ?string $heading = "Edycja produktu";


    public function mount(int|string $record): void
    {
        $this->record = Products::where('hash', $record)->where('installation', INSTALLATION)->first();
        $this->authorizeAccess();
        $this->fillForm();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(
                [
                    Forms\Components\Section::make()
                        ->columns(3)
                        ->schema([
                            Forms\Components\TextInput::make('name')
                                ->label(__('app._.name'))
                                ->required()
                                ->maxLength(255),
                            Forms\Components\TextInput::make('gtin')
                                ->label(__('app.products._.gtin'))
                                ->hint('Cyfry kodu paskowego')
                                ->maxLength(20),
                            Forms\Components\TextInput::make('extra_code')
                                ->label(__('app.products._.extra_code'))
                                ->hint(__('app.products._.extra_code_hint'))
                                ->maxLength(20),
                            Forms\Components\TextInput::make('basic_unit')
                                ->label(__('app.products._.basic_unit'))
                                ->default('szt'),
                            Forms\Components\TextInput::make('volume_ml')
                                ->numeric()
                                ->label(__('app.products._.volume_ml'))
                                ->hint(__('app.products._.volume_ml_hint'))
                                ->default('szt'),
                            Forms\Components\TextInput::make('weight_gr')
                                ->numeric()
                                ->hint(__('app.products._.weight_gr_hint'))
                                ->label(__('app.products._.weight_gr'))
                                ->default('szt'),
                            Forms\Components\TextInput::make('ext_link')
                                ->label(__('app.products.create.ext_link'))
                                ->url()
                                ->columnSpanFull()
                                ->maxLength(255),
                            Forms\Components\Textarea::make('description')
                                ->label(__('app.products.create.description'))
                                ->maxLength(65535)
                                ->columnSpanFull(),
                            Forms\Components\Textarea::make('grace_period')
                                ->label(__('app.products._.grace_period'))
                                ->maxLength(65535)
                                ->columnSpanFull(),
                        ]),
                    Forms\Components\Section::make('Ceny')
                        ->columns(2)
                        ->schema([
                            Forms\Components\TextInput::make('price_per_unit')
                                ->extraAlpineAttributes(
                                    Filters::getFilter(['price' => 'keypress.self'])
                                )
                                ->stripCharacters(' ')
                                ->default(0.0)
                                ->inputMode('decimal')
                                ->label('Cena jednostkowa')
                                ->live(onBlur: true)
                                ->afterStateUpdated(
                                    function (Forms\Set $set, Forms\Get $get, $state) {
                                        if ($state === '0') {
                                            return;
                                        }
                                        $this->data['price_per_unit'] = Str::of($state)->replace(',', '.')->toFloat();
                                    }
                                )
                                ->dehydratedWhenHidden(true),
                            Forms\Components\Select::make('is_net')
                                ->label('Rodzaj ceny')
                                ->options([
                                    1 => 'Cena netto',
                                    0 => 'Cena brutto',
                                ])
                                ->inlineLabel(false)
                                ->default(1),
                            Forms\Components\Select::make('vat_label')
                                ->options(fn() => VatRates::getRatesForSelect())
                                ->live()
                                ->afterStateUpdated(
                                    function (Forms\Set $set, Forms\Get $get, $state) {
                                        $set('vat_rate', VatRates::getRate($state));
                                    }
                                )
                                ->default(23)
                                ->label(__('app.trade_docs.add_item.vat_label')),
                            Forms\Components\TextInput::make('vat_rate')
                                ->hidden()
                                ->dehydratedWhenHidden(true)
                                ->numeric()
                                ->default(23)
                                ->label(__('app.trade_docs.add_item.vat_rate')),
                        ]),
                    Forms\Components\Section::make('Na magazynie')
                        ->visible(fn() => tenant()?->hasModule(SystemModules::WAREHOUSE) ?? false)
                        ->columns(2)
                        ->schema([
                            Forms\Components\Group::make([
                                Forms\Components\Select::make('item_type')
                                    ->options(WarehouseItemTypes::toArrayWithLabels())
                                    ->label(__('app.products._.item_type'))
                                    ->live()
                                    ->default(WarehouseItemTypes::PRODUCT->value)
                            ])
                                ->columns(3)
                                ->columnSpanFull(),
                            Forms\Components\Group::make([
                                Forms\Components\TextInput::make('minimum_stock')
                                    ->numeric()
                                    ->columnSpan(2)
                                    ->minValue(0)
                                    ->hintIcon(
                                        icon: 'heroicon-m-question-mark-circle',
                                        tooltip: 'Stan alarmowy'
                                    )
                                    ->label(__('app.products._.minimum_stock')),
                                Forms\Components\Toggle::make('minimum_stock_update_wh')
                                    ->label('Zaktualizuj magazyn')
                                    ->inline(false)

                            ])
                                ->hidden(
                                    fn(Forms\Get $get) => (int)$get('item_type') === WarehouseItemTypes::SERVICE->value
                                )
                                ->columns(3),
                            Forms\Components\Group::make([
                                Forms\Components\TextInput::make('minimum_exp_date')
                                    ->numeric()
                                    ->minValue(0)
                                    ->hintIcon(
                                        icon: 'heroicon-m-question-mark-circle',
                                        tooltip: 'Ile dni wcześniej ostrzegać o mijającym terminie przydatności'
                                    )
                                    ->label(__('app.products._.minimum_exp_date'))
                                    ->columnSpan(2),

                                Forms\Components\Toggle::make('minimum_exp_date_update_wh')
                                    ->label('Zaktualizuj magazyn')
                                    ->inline(false)
                            ])
                                ->hidden(
                                    fn(Forms\Get $get) => (int)$get('item_type') === WarehouseItemTypes::SERVICE->value
                                )
                                ->columns(3),
                        ]),
                    Forms\Components\Section::make('Producent')
                        ->columns(2)
                        ->schema([
                            Forms\Components\Select::make('manufacturer_id')
                                ->options(
                                    Manufacturer::query()
                                        ->orderBy('name', 'asc')
                                        ->get()
                                        ->pluck('name', 'id')
                                        ->toArray()
                                )
                                ->createOptionForm(ManufacturerResource::GetCreateModalForm())
                                ->createOptionUsing(function (array $data) {
                                    return ManufacturersRepository::CreateFromModalForm($data)?->id;
                                })
                                ->createOptionModalHeading('Utwórz producenta')
                                ->label(__('app.products._.manufacturer')),
                        ]),
                    Forms\Components\Section::make('Daty')
                        ->columns(2)
                        ->schema([
                            Forms\Components\TextInput::make('created_at')
                                ->formatStateUsing(function (Model $record) {
                                    return $record->created_at
                                        ->setTimezone(new \DateTimeZone('Europe/Warsaw'))
                                        ->format('Y-m-d H:i:s');
                                })
                                ->label(__('app._.created_at'))
                                ->disabled(),
                            Forms\Components\TextInput::make('updated_at')
                                ->formatStateUsing(function (Model $record) {
                                    return $record->updated_at
                                        ->setTimezone(new \DateTimeZone('Europe/Warsaw'))
                                        ->format('Y-m-d H:i:s');
                                })
                                ->label(__('app._.updated_at'))
                                ->disabled(),
                        ]),
                    Forms\Components\Section::make('Status')
                        ->schema([
                            Forms\Components\Toggle::make('is_active')
                                ->label(__('app._.is_active'))
                                ->required(),
                        ]),
                ]
            );
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        if (!tenant()?->hasModule(SystemModules::WAREHOUSE) ||
            (int)$data['item_type'] === WarehouseItemTypes::SERVICE->value
        ) {
            unset($data['minimum_stock'], $data['minimum_exp_date']);
            $model = parent::handleRecordUpdate($record, $data);
        } else {
            $minimum_stock = $data['minimum_stock'];
            $minimum_stock_update_wh = $data['minimum_stock_update_wh'];
            $minimum_exp_date = $data['minimum_exp_date'];
            $minimum_exp_date_update_wh = $data['minimum_exp_date_update_wh'];

            unset($data['minimum_stock_update_wh'], $data['minimum_exp_date_update_wh']);

            $model = parent::handleRecordUpdate($record, $data);
            WarehouseRepository::updateWarehouseItemsWarnings(
                product: $model,
                expu: $minimum_exp_date_update_wh,
                expv: $minimum_exp_date,
                lstocku: $minimum_stock_update_wh,
                lstockv: $minimum_stock
            );
        }

        return $model;
    }

    protected function getFormActions(): array
    {
        return [
            $this->getSubmitFormAction(),
            self::getBackToListFormAction(),
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
