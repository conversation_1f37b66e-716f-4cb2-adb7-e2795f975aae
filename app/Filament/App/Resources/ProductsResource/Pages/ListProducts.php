<?php

namespace App\Filament\App\Resources\ProductsResource\Pages;

use App\Enums\SystemModules;
use App\Filament\App\Resources\ProductsResource;
use App\Repositories\DrugsRepository;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use pxlrbt\FilamentExcel\Actions\Pages\ExportAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;

class ListProducts extends ListRecords
{
    protected static string $resource = ProductsResource::class;

    protected ?string $heading = "Produkty";

    public $page;

    protected function getHeaderActions(): array
    {
        return [
            DrugsRepository::getImportAction('header')
                ->visible(fn() => tenant()?->hasModule(SystemModules::WAREHOUSE) ?? false),
            Actions\CreateAction::make(),
            ExportAction::make()
                ->exports([
                    ExcelExport::make()
                        ->withFilename(fn() => 'lista_produktow_' . now('Europe/Warsaw')->format('Y-m-d_H:i'))
                        ->fromTable()
                ])
        ];
    }
}
