<?php

namespace App\Filament\App\Resources\ProductsResource\Pages;

use App\Enums\SystemModules;
use App\Enums\VatRates;
use App\Enums\WarehouseItemTypes;
use App\Filament\App\Resources\ManufacturerResource;
use App\Filament\App\Resources\ProductsResource;
use App\Filament\Traits\HasBackToListButton;
use App\Helpers\Identifiers;
use App\Models\Manufacturer;
use App\Repositories\ManufacturersRepository;
use App\Services\Filters;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Str;

class CreateProducts extends CreateRecord
{
    use HasBackToListButton;

    protected static string $resource = ProductsResource::class;

    protected ?string $heading = "Dodaj produkt";

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Dane podstawowe')
                    ->columns(3)
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(__('app._.name'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('gtin')
                            ->label(__('app.products._.gtin'))
                            ->hint(__('app.products._.gtin_hint'))
                            ->maxLength(20),
                        Forms\Components\TextInput::make('extra_code')
                            ->label(__('app.products._.extra_code'))
                            ->hint(__('app.products._.extra_code_hint'))
                            ->maxLength(20),
                        Forms\Components\TextInput::make('basic_unit')
                            ->label(__('app.products._.basic_unit'))
                            ->default('szt')
                            ->readOnly(),
                        Forms\Components\TextInput::make('volume_ml')
                            ->numeric()
                            ->default(0)
                            ->minValue(0)
                            ->label(__('app.products._.volume_ml'))
                            ->hint(__('app.products._.volume_ml_hint')),
                        Forms\Components\TextInput::make('weight_gr')
                            ->numeric()
                            ->default(0)
                            ->minValue(0)
                            ->hint(__('app.products._.weight_gr_hint'))
                            ->label(__('app.products._.weight_gr')),
                        Forms\Components\TextInput::make('ext_link')
                            ->label(__('app.products.create.ext_link'))
                            ->url()
                            ->columnSpanFull()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->label(__('app.products.create.description'))
                            ->maxLength(65535)
                            ->columnSpanFull(),
                        Forms\Components\Textarea::make('grace_period')
                            ->label(__('app.products._.grace_period'))
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Producent')
                    ->columns(2)
                    ->schema([
                        Forms\Components\Select::make('manufacturer_id')
                            ->options(
                                Manufacturer::query()
                                    ->orderBy('name', 'asc')
                                    ->get()
                                    ->pluck('name', 'id')
                                    ->toArray()
                            )
                            ->createOptionForm(ManufacturerResource::GetCreateModalForm())
                            ->createOptionUsing(function (array $data) {
                                return ManufacturersRepository::CreateFromModalForm($data)?->id;
                            })
                            ->createOptionModalHeading('Utwórz producenta')
                            ->label(__('app.products._.manufacturer')),
                    ]),

                Forms\Components\Section::make('Ceny')
                    ->columns(2)
                    ->schema([
                        Forms\Components\TextInput::make('price_per_unit')
                            ->extraAlpineAttributes(
                                Filters::getFilter(['price' => 'keypress.self'])
                            )
                            ->stripCharacters(' ')
                            ->default(0.0)
                            ->inputMode('decimal')
                            ->label(__('app.products._.price_per_unit'))
                            ->live(onBlur: true)
                            ->afterStateUpdated(
                                function (Forms\Set $set, Forms\Get $get, $state) {
                                    if ($state === '0') {
                                        return;
                                    }
                                    $this->data['price_per_unit'] = Str::of($state)->replace(',', '.')->toFloat();
                                }
                            )
                            ->dehydratedWhenHidden(true),
                        Forms\Components\Select::make('is_net')
                            ->label(__('app.products._.is_net_label'))
                            ->options([
                                1 => 'Cena netto',
                                0 => 'Cena brutto',
                            ])
                            ->inlineLabel(false)
                            ->default(1),
                        Forms\Components\Select::make('vat_label')
                            ->options(fn() => VatRates::getRatesForSelect())
                            ->live()
                            ->afterStateUpdated(
                                function (Forms\Set $set, Forms\Get $get, $state) {
                                    $set('vat_rate', VatRates::getRate($state));
                                }
                            )
                            ->default(23)
                            ->label(__('app.trade_docs.add_item.vat_label')),
                        Forms\Components\TextInput::make('vat_rate')
                            ->hidden()
                            ->dehydratedWhenHidden(true)
                            ->numeric()
                            ->default(23)
                            ->label(__('app.trade_docs.add_item.vat_rate')),
                    ]),
                Forms\Components\Section::make('Na magazynie')
                    ->visible(fn() => tenant()?->hasModule(SystemModules::WAREHOUSE) ?? false)
                    ->columns(4)
                    ->schema(
                        [
                            /**
                             * Forms\Components\Toggle::make('limited_stock')
                             * ->hint('Ograniczona liczba sztuk - produkty')
                             * ->label(__('app.products.create.limited_stock'))
                             * ->default(true),
                             * Forms\Components\Toggle::make('below_stock')
                             * ->hint('Czy można wydać produkt poniżej stanu 0')
                             * ->label(__('app.products.create.below_stock')),
                             * */
                            Forms\Components\Select::make('item_type')
                                ->options(WarehouseItemTypes::toArrayWithLabels())
                                ->label(__('app.products._.item_type'))
                                ->live()
                                ->default(WarehouseItemTypes::PRODUCT->value)
                            ,
                            Forms\Components\TextInput::make('minimum_stock')
                                ->numeric()
                                ->hidden(
                                    fn(Forms\Get $get) => (int)$get('item_type') === WarehouseItemTypes::SERVICE->value
                                )
                                ->default(5)
                                ->minValue(0)
                                ->helperText('Stan uruchamiający alarm')
                                ->label(__('app.products._.minimum_stock')),
                            Forms\Components\TextInput::make('minimum_exp_date')
                                ->numeric()
                                ->hidden(
                                    fn(Forms\Get $get) => (int)$get('item_type') === WarehouseItemTypes::SERVICE->value
                                )
                                ->minValue(0)
                                ->columnSpan(2)
                                ->default(0)
                                ->helperText('Ile dni wcześniej ostrzegać o mijającym terminie przydatności')
                                ->label(__('app.products._.minimum_exp_date')),
                        ]
                    ),
                Forms\Components\Section::make('Status')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label(__('app._.is_active'))
                            ->required(),
                    ]),
            ]);
    }

    protected function getRedirectUrl(): string
    {
        return self::getResource()::getUrl('edit', ['record' => $this->getRecord()->hash]);
    }

    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction(),
            $this->getCreateAnotherFormAction(),
            self::getBackToListFormAction(),
        ];
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['installation'] = auth()->user()?->installation() ?? -1;
        $data['hash'] = Identifiers::getRandomHash();
        return $data;
    }
}
