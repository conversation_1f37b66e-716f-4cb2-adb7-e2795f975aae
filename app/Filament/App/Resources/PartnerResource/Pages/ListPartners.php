<?php

namespace App\Filament\App\Resources\PartnerResource\Pages;

use App\Filament\App\Resources\PartnerResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Support\Enums\IconPosition;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Model;

class ListPartners extends ListRecords
{
    protected static string $resource = PartnerResource::class;

    protected ?string $heading = 'Partnerzy';

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function table(Table $table): Table
    {
        return match (auth()->user()?->isTenantAdmin()) {
            true => $this->adminTable($table),
            false => $this->employeeTable($table),
            default => $table,
        };
    }

    public function employeeTable(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\Layout\Split::make([
                    Tables\Columns\Layout\Stack::make([
                        TextColumn::make('name')
                            ->label(__('app.partners.list.name'))
                            ->icon(
                                fn(Model $record) => match ((bool)$record->is_active) {
                                    true => null,
                                    default => 'heroicon-m-exclamation-triangle'
                                }
                            )
                            ->iconPosition(IconPosition::After)
                            ->iconColor('warning')
                            ->searchable(),
                        TextColumn::make('vat_id')
                            ->label(__('app.partners.list.vat_id'))
                            ->searchable(),
                    ]),
                    Tables\Columns\Layout\Stack::make([
                        TextColumn::make('address')
                            ->label(__('app.partners.list.location'))
                            ->searchable(['postcode', 'city'])
                            ->state(function (Model $record) {
                                return $record->postcode . ' ' . $record->city;
                            }),
                        TextColumn::make('phone')
                            ->label(__('app.partners.list.phone'))
                            ->searchable(),
                        TextColumn::make('email')
                            ->label(__('app.partners.list.email'))
                            ->searchable(),
                        TextColumn::make('contact_name')
                            ->label(__('app.partners.list.contact_name'))
                            ->searchable(),
                    ])
                ]),
            ])
            ->filters([
                //
            ])
            ->recordAction(Tables\Actions\ViewAction::class)
            ->recordUrl(null)
            ->actions([
                Tables\Actions\EditAction::make()->label("Edycja")
                    ->url(
                        fn(Model $record): string => self::getResource()::getUrl('edit', ['record' => $record->hash])
                    ),
                Tables\Actions\ViewAction::make()->label("")->icon(null),
            ]);
    }

    public function adminTable(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('app.partners.list.name'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                TextColumn::make('short_name')
                    ->state(
                        fn($record) => filled($record->short_name) ? $record->short_name : $record->name
                    )
                    ->label(__('app.partners.create.short_name'))
                    ->searchable(),
                TextColumn::make('address')
                    ->label(__('app.partners.list.location'))
                    ->searchable(['postcode', 'city'])
                    ->state(function (Model $record) {
                        return $record->postcode . ' ' . $record->city;
                    }),
                TextColumn::make('phone')
                    ->label(__('app.partners.list.phone'))
                    ->searchable(),
                TextColumn::make('email')
                    ->label(__('app.partners.list.email'))
                    ->toggleable(isToggledHiddenByDefault: false)
                    ->searchable(),
                TextColumn::make('contact_name')
                    ->label(__('app.partners.list.contact_name'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                TextColumn::make('vat_id')
                    ->label(__('app.partners.list.vat_id'))
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label(__('app.partners.list.is_active'))
                    ->boolean()
                    ->tooltip(fn($state) => $state ? 'Aktywny' : 'Nieaktywny'),
                TextColumn::make('created_at')
                    ->label(__('app.partners.list.created_at'))
                    ->dateTime('Y-m-d H:i:s', 'Europe/Warsaw')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->label(__('app.partners.list.updated_at'))
                    ->dateTime('Y-m-d H:i:s', 'Europe/Warsaw')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()->label("")
                    ->url(
                        fn(Model $record): string => self::getResource()::getUrl('edit', ['record' => $record->hash])
                    ),
            ]);
    }
}
