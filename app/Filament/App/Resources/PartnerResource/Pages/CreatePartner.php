<?php

namespace App\Filament\App\Resources\PartnerResource\Pages;

use App\Enums\Countries;
use App\Enums\TaxResidencyCountries;
use App\Filament\App\Resources\PartnerResource;
use App\Filament\Traits\HasBackToListButton;
use App\Helpers\StringHelper;
use App\Models\Partner;
use App\Repositories\GUSRepository;
use App\Repositories\PartnersRepository;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\MaxWidth;

class CreatePartner extends CreateRecord
{
    use HasBackToListButton;

    protected static string $resource = PartnerResource::class;

    protected ?string $heading = 'Dodaj partnera';

    public function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                Forms\Components\Grid::make()
                    ->columns(2)
                    ->schema([
                        Forms\Components\Fieldset::make('Dane')
                            ->columnSpan(1)
                            ->columns(1)
                            ->extraAttributes(['class' => 'h-full'], true)
                            ->schema([
                                Forms\Components\Textarea::make('name')
                                    ->label(__('app.partners.create.name'))
                                    ->required()
                                    ->rows(3)
                                    ->hint('Maksymalnie 250 znaków')
                                    ->maxLength(250),
                                Forms\Components\TextInput::make('short_name')
                                    ->label(__('app.partners.create.short_name'))
                                    ->hint('Maksymalnie 120 znaków')
                                    ->maxLength(120),
                            ]),
                        Forms\Components\Fieldset::make('VAT')
                            ->extraAttributes(['class' => 'h-full'], true)
                            ->columns(1)
                            ->columnSpan(1)
                            ->schema([
                                Forms\Components\Select::make('tax_residency_country')
                                    ->label(__('app.partners.create.tax_residency_country') . ' (EU)')
                                    ->options(
                                        TaxResidencyCountries::toArrayWithLabels()
                                    ),
                                Forms\Components\TextInput::make('vat_id')
                                    ->label(__('app.partners.create.vat_id'))
                                    ->live(true)
                                    ->afterStateUpdated(
                                        function (Forms\Set $set, $state, $component) {
                                            if (blank($state)) {
                                                return;
                                            }
                                            $set('vat_id', StringHelper::extractDigits($state));
                                            $this->validateOnly(
                                                $component->getStatePath(),
                                                null,
                                                ['unique'=> 'Partner o podanym NIP już istnieje']
                                            );
                                        }
                                    )
                                    ->unique(
                                        table: (new Partner())->getTable(),
                                        column: 'vat_id',
                                        ignorable: null,
                                        ignoreRecord: false,
                                        modifyRuleUsing: fn($rule) => $rule->where('installation', tenant()->id)
                                    )
                                    ->validationMessages([
                                        'unique' => 'Partner o podanym NIP już istnieje',
                                    ])
                                    ->maxLength(255),
                            ]),
                    ]),
                Forms\Components\Fieldset::make('Dane adresowe')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('postcode')
                                    ->label(__('app.partners.create.postcode'))
                                    ->maxLength(100),
                                Forms\Components\TextInput::make('city')
                                    ->label(__('app.partners.create.city'))
                                    ->maxLength(100),
                                Forms\Components\Select::make('country_id')
                                    ->label(__('app.partners.create.country_id'))
                                    ->options(Countries::toArray())
                                    ->searchable()
                                    ->default(Countries::PL->name),
                            ]),
                        Forms\Components\Textarea::make('address')
                            ->label(__('app.partners.create.address'))
                            ->maxLength(65535)
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('phone')
                            ->label(__('app.partners.create.phone'))
                            ->tel()
                            ->maxLength(100),
                        Forms\Components\TextInput::make('email')
                            ->label(__('app.partners.create.email'))
                            ->email()
                            ->maxLength(60),
                        Forms\Components\TextInput::make('contact_name')
                            ->label(__('app.partners.create.contact_name'))
                            ->maxLength(255),
                        Forms\Components\TextInput::make('website')
                            ->label(__('app.partners.create.website'))
                            ->maxLength(255),
                    ]),
                Forms\Components\Fieldset::make('Bank')
                    ->schema([
                        Forms\Components\TextInput::make('bank_name')
                            ->label(__('app.partners.create.bank_name'))
                            ->maxLength(255),
                        Forms\Components\TextInput::make('bank_account')
                            ->label(__('app.partners.create.bank_account'))
                            ->maxLength(255),
                        Forms\Components\TextInput::make('bank_iban')
                            ->label(__('app.partners.create.bank_iban'))
                            ->maxLength(255),
                        Forms\Components\TextInput::make('bank_swift')
                            ->label(__('app.partners.create.bank_swift'))
                            ->maxLength(255),
                    ]),
                Forms\Components\Toggle::make('is_active')
                    ->label(__('app.partners.create.is_active'))
                    ->default(true)
                    ->required(),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('gus-import')
                ->label('Importuj z GUS')
                ->modalHeading('Importuj partnera z GUS')
                ->modalWidth(MaxWidth::Medium)
                ->form([
                    Forms\Components\TextInput::make('nip')
                        ->label('Wpisz NIP')
                        ->helperText('Tylko 10 cyfr!')
                        ->live(true)
                        ->afterStateUpdated(
                            function (Forms\Set $set, $state) {
                                $set('nip', StringHelper::extractDigits($state));
                            }
                        )
                        ->unique(
                            table: (new Partner())->getTable(),
                            column: 'vat_id',
                            ignorable: null,
                            ignoreRecord: false,
                            modifyRuleUsing: fn($rule) => $rule->where('installation', tenant()->id)
                        )
                        ->validationMessages([
                            'unique' => 'Partner o podanym NIP już istnieje',
                        ])
                        ->required()
                ])
                ->modalSubmitAction(fn (Actions\StaticAction $action) => $action->label('Importuj'))
                ->action(function (array $data) {
                    $nip = StringHelper::extractDigits($data['nip']);
                    if (blank($nip) || strlen($nip) !== 10) {
                        Notification::make()->title('Podaj prawidłowy NIP')
                        ->danger()
                        ->send();
                        return;
                    }

                    if (PartnersRepository::checkIfNipExists($nip)) {
                        Notification::make()->title('Partner o podanym NIP już istnieje')
                        ->danger()
                        ->send();
                        return;
                    }

                    $company = GUSRepository::findByNip($nip);
                    if (blank($company)) {
                        Notification::make()->title('Partner z podanego NIP nie został znaleziony')
                        ->danger()
                        ->send();
                        return;
                    }
                    $data = PartnerResource::mapGUSResponseToFormData($company);
                    $this->form->fill(array_merge($this->data, $data));
                }),
        ];
    }


    protected function mutateFormDataBeforeCreate(array $data): array
    {
        return PartnersRepository::mutateFormDataBeforeCreate($data);
    }

    protected function getRedirectUrl(): string
    {
        return self::getResource()::getUrl('edit', ['record' => $this->getRecord()->hash]);
    }

    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction(),
            $this->getCreateAnotherFormAction(),
            self::getBackToListFormAction(),
        ];
    }
}
