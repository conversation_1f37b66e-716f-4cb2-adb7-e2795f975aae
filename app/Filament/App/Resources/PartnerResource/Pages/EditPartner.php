<?php

namespace App\Filament\App\Resources\PartnerResource\Pages;

use App\Enums\Countries;
use App\Enums\PartnerBusinessTypes;
use App\Enums\PartnerVATTypes;
use App\Enums\TaxResidencyCountries;
use App\Filament\App\Resources\PartnerResource;
use App\Filament\Traits\HasBackToListButton;
use App\Helpers\StringHelper;
use App\Models\Partner;
use App\Repositories\GUSRepository;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\MaxWidth;

class EditPartner extends EditRecord
{
    use HasBackToListButton;

    protected static string $resource = PartnerResource::class;

    protected ?string $heading = 'Edycja partnera';

    protected $installation = null;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('gus-import')
                ->label('Aktualizuj z GUS')
                ->modalHeading('Aktualizuj partnera z GUS')
                ->requiresConfirmation()
                ->modalWidth(MaxWidth::Medium)
                ->disabled(
                    fn() => $this->form->getState(false)['tax_residency_country'] !== TaxResidencyCountries::PL->name
                        || blank($this->form->getState(false)['vat_id'])
                )
                ->modalSubmitAction(fn (Actions\StaticAction $action) => $action->label('Aktualizuj'))
                ->action(function () {
                    if (blank($this->form->getState(false)['vat_id'])) {
                        Notification::make()->title('Podaj prawidłowy NIP')
                            ->danger()
                            ->send();
                        return;
                    }
                    $nip = StringHelper::extractDigits($this->form->getState(false)['vat_id']);
                    $company = GUSRepository::findByNip($nip);
                    if (blank($company)) {
                        Notification::make()->title('Partner z podanego NIP nie został znaleziony')
                            ->danger()
                            ->send();
                        return;
                    }
                    $data = PartnerResource::mapGUSResponseToFormData($company);
                    $this->form->fill(array_merge($this->form->getState(false), $data));
                }),
            Actions\DeleteAction::make()
            ->modalHeading('Usuń partnera'),
        ];
    }

    public function mount(int | string $record): void
    {
        $this->record = Partner::where('hash', $record)->where('installation', INSTALLATION)->first();
        $this->installation = INSTALLATION;
        $this->authorizeAccess();
        $this->fillForm();
    }

    public function form(Form $form): Form
    {
        return $form
            ->columns(3)
            ->schema([
                Forms\Components\Grid::make()
                    ->columns(2)
                    ->schema([
                        Forms\Components\Fieldset::make('Dane')
                            ->columnSpan(1)
                            ->columns(1)
                            ->extraAttributes(['class' => 'h-full'], true)
                            ->schema([
                                Forms\Components\Textarea::make('name')
                                    ->label(__('app.partners.create.name'))
                                    ->required()
                                    ->rows(3)
                                    ->hint('Maksymalnie 250 znaków')
                                    ->maxLength(250),
                                Forms\Components\TextInput::make('short_name')
                                    ->label(__('app.partners.create.short_name'))
                                    ->hint('Maksymalnie 120 znaków')
                                    ->maxLength(120),
                            ]),
                        Forms\Components\Fieldset::make('VAT')
                            ->extraAttributes(['class' => 'h-full'], true)
                            ->columns(1)
                            ->columnSpan(1)
                            ->schema([
                                Forms\Components\Select::make('tax_residency_country')
                                    ->label(__('app.partners.create.tax_residency_country') . ' (EU)')
                                    ->live()
                                    ->options(
                                        TaxResidencyCountries::toArrayWithLabels()
                                    ),
                                Forms\Components\TextInput::make('vat_id')
                                    ->label(__('app.partners.create.vat_id'))
                                    ->live(true)
                                    ->maxLength(255),
                            ]),
                    ]),
                Forms\Components\Fieldset::make('Dane adresowe')
                    ->schema([
                        Forms\Components\TextInput::make('postcode')
                            ->label(__('app.partners.create.postcode'))
                            ->maxLength(100),
                        Forms\Components\TextInput::make('city')
                            ->label(__('app.partners.create.city'))
                            ->maxLength(100),
                        Forms\Components\Textarea::make('address')
                            ->label(__('app.partners.create.address'))
                            ->maxLength(65535)
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('phone')
                            ->label(__('app.partners.create.phone'))
                            ->tel()
                            ->maxLength(100),
                        Forms\Components\TextInput::make('email')
                            ->label(__('app.partners.create.email'))
                            ->email()
                            ->maxLength(60),
                        Forms\Components\TextInput::make('contact_name')
                            ->label(__('app.partners.create.contact_name'))
                            ->maxLength(255),
                        Forms\Components\TextInput::make('website')
                            ->label(__('app.partners.create.website'))
                            ->maxLength(255),
                    ]),
                Forms\Components\Fieldset::make('Bank')
                    ->schema([
                        Forms\Components\TextInput::make('bank_name')
                            ->label(__('app.partners.create.bank_name'))
                            ->maxLength(255),
                        Forms\Components\TextInput::make('bank_account')
                            ->label(__('app.partners.create.bank_account'))
                            ->maxLength(255),
                        Forms\Components\TextInput::make('bank_iban')
                            ->label(__('app.partners.create.bank_iban'))
                            ->maxLength(255),
                        Forms\Components\TextInput::make('bank_swift')
                            ->label(__('app.partners.create.bank_swift'))
                            ->maxLength(255),
                    ]),
                Forms\Components\Toggle::make('is_active')
                    ->label(__('app.partners.create.is_active'))
                    ->default(true)
                    ->required(),
            ]);
    }

    protected function getFormActions(): array
    {
        return [
            $this->getSaveFormAction(),
            self::getBackToListFormAction(),
        ];
    }
}
