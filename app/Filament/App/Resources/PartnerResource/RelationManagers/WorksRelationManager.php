<?php

namespace App\Filament\App\Resources\PartnerResource\RelationManagers;

use App\Enums\SystemModules;
use Filament\Forms;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WorksRelationManager extends RelationManager
{
    protected static string $relationship = 'workshops';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('app.partners.works.title');
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return tenant()?->hasModule(SystemModules::JOB_TASKS) ?? false;
    }


    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label(__('app.partners.works.name'))
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('short_name')
                    ->label(__('app.partners.works.short_name'))
                    ->maxLength(255),
                Textarea::make('address')
                    ->label(__('app.partners.works.address'))
                    ->columnSpan(2),
//                Forms\Components\Repeater::make('metadata')
//                    ->label(__('app.partners.works.metadata'))
//                    ->columns(2)
//                    ->columnSpan(2)
//                    ->grid(2)
//                    ->addActionLabel(__('app.partners.works.metadata_action_label'))
//                    ->reorderable(false)
//                    ->schema([
//                        Forms\Components\TextInput::make('name')
//                            ->label(__('app.partners.works.metadata_name'))
//                            ->columnSpan(1),
//                        Forms\Components\TextInput::make('value')
//                            ->label(__('app.partners.works.metadata_value'))
//                            ->columnSpan(1),
//                    ])
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                ->label(__('app.partners.works.name')),
                Tables\Columns\TextColumn::make('short_name')
                    ->label(__('app.partners.works.short_name')),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                ->modalHeading(__('app.partners.works.modal.heading')),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
