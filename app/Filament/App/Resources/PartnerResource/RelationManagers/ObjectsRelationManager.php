<?php

namespace App\Filament\App\Resources\PartnerResource\RelationManagers;

use App\Enums\SystemModules;
use App\Models\PartnerObject;
use App\Models\PartnerWork;
use Filament\Forms;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ObjectsRelationManager extends RelationManager
{
    protected static string $relationship = 'objects';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('app.partners.objects.title');
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return tenant()?->hasModule(SystemModules::JOB_TASKS) ?? false;
    }


    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label(__('app.partners.objects.name'))
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('short_name')
                    ->label(__('app.partners.objects.short_name'))
                    ->maxLength(255),
                Forms\Components\Group::make([
                    Forms\Components\Select::make('belongsTo')
                        ->label(__('app.partners.objects.belongs_to'))
                        ->options(['partner' => 'Partner', 'works' => 'Zakład'])
                        ->formatStateUsing(
                            fn(null|Model|PartnerObject $record) => blank($record?->partner_work_id) ?
                                'partner' :
                                'works'
                        )
                        ->live()
                        ->required(),
                    Forms\Components\Select::make('partner_work_id')
                        ->label(__('app.partners.objects.select_title'))
                        ->options(PartnerWork::pluck('name', 'id'))
                        ->disabled(fn(Forms\Get $get) => $get('belongsTo') !== 'works')
                        ->required(fn(Forms\Get $get) => $get('belongsTo') === 'works')
                        ->mutateDehydratedStateUsing(
                            fn(Forms\Get $get, $state) => $get('belongsTo') === 'partner' ? null : $state
                        ),
                ])
                    ->columns(2)
                    ->columnSpanFull(),
//                Textarea::make('address')
//                    ->label(__('app.partners.objects.address'))
//                    ->columnSpan(2),
//                Forms\Components\Fieldset::make(__('app.partners.objects.gps_location'))
//                    ->schema([
//                        Forms\Components\TextInput::make('lat')
//                            ->label(__('app.partners.objects.gps_lat'))
//                            ->numeric(),
//                        Forms\Components\TextInput::make('lon')
//                            ->label(__('app.partners.objects.gps_lon'))
//                            ->numeric(),
//                    ]),
                Forms\Components\Repeater::make('metadata')
                    ->label(__('app.partners.objects.metadata'))
                    ->columns(2)
                    ->columnSpan(2)
                    ->grid(2)
                    ->addActionLabel(__('app.partners.objects.metadata_action_label'))
                    ->reorderable(false)
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(__('app.partners.objects.metadata_name'))
                            ->columnSpan(1),
                        Forms\Components\TextInput::make('value')
                            ->label(__('app.partners.objects.metadata_value'))
                            ->columnSpan(1),
                    ])
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('app.partners.objects.name')),
                Tables\Columns\TextColumn::make('short_name')
                    ->label(__('app.partners.objects.short_name')),
                Tables\Columns\TextColumn::make('partner_work_id')
                    ->default('-')
                    ->formatStateUsing(fn(?Model $record, $state) => blank($state) ? '-' : $record->work?->name ?? '-')
                    ->label(__('app.partners.objects.workname')),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->modalHeading(__('app.partners.objects.modal.heading'))
                    ->mutateFormDataUsing(
                        function ($data) {
                            if ($data['belongsTo'] === 'partner') {
                                $data['partner_work_id'] = null;
                            }
                            unset($data['belongsTo']);
                            return $data;
                        }
                    ),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateFormDataUsing(
                        function ($data) {
                            if ($data['belongsTo'] === 'partner') {
                                $data['partner_work_id'] = null;
                            }
                            unset($data['belongsTo']);
                            return $data;
                        }
                    ),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
