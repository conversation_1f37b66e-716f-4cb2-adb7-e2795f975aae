<?php

namespace App\Filament\App\Resources\TradeDocResource\RelationManagers;

use App\Enums\DocumentTypes;
use App\Enums\TradeDocDiscountTypes;
use App\Enums\TradeDocVatMethod;
use App\Enums\VatRates;
use App\Filament\App\Resources\ProductsResource;
use App\Models\Products;
use App\Models\TradeDocItem;
use App\Repositories\ProductsRepository;
use App\Repositories\TradeDocsRepository;
use App\Services\Filters;
use App\Traits\CalculateItemsTrait;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Support\RawJs;
use Filament\Tables;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class ItemsRelationManager extends RelationManager
{
    use CalculateItemsTrait;

    protected static string $relationship = 'items';
    public array $docItem = [];

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('app.trade_docs.add_item.products');
    }

    public function isReadOnly(): bool
    {
        return false;
    }

    public function form(Form $form): Form
    {
        return match ($this->getOwnerRecord()->type) {
            DocumentTypes::FVS => $this->FVS($form),
            DocumentTypes::FAUP => $this->FAUP($form),
            default => $this->FVS($form),
        };
    }


    public function FVS(Form $form): Form
    {
        $schema = $this->addProductsSource($form, []);
        $schema = $this->addStandardFields($form, $schema);
        $schema = $this->addTagsInput($form, $schema);
        return $form
            ->columns(3)
            ->statePath('docItem')
            ->schema($schema);
    }

    public function FAUP(Form $form): Form
    {
        $schema = $this->addProductsSource($form, []);
        $schema = $this->addStandardFAUPFields($form, $schema);
        $schema = $this->addTagsInput($form, $schema);
        return $form
            ->columns(3)
            ->statePath('docItem')
            ->schema($schema);
    }


    protected function addProductsSource(Form $form, array $schema): array
    {
        $select = Forms\Components\Select::make('product')
            ->dehydrated(false)
            ->searchable()
            ->preload()
            ->live()
            ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get, $state) {
                if (blank($state)) {
                    return;
                }
                /**
                 * @var Products $product
                 */
                $product = Products::find($state);
                $set('label', $product->name);
                $set('unit_type', $product->basic_unit);
                if (blank($product->price_per_unit)) {
                    return;
                }

                if ($this->getOwnerRecord()->type === DocumentTypes::FAUP) {
                    $set('gross_unit_price', $product->getGrossPriceInt() / 100);
                    $this->calculate($set);
                    return;
                }

                switch ($this->getOwnerRecord()->vat_method) {
                    case TradeDocVatMethod::BASE_ON_NET:
                        $set('net_unit_price', $product->getNetPriceInt() / 100);
                        $set('vat_label', $product->vat_label);
                        break;
                    case TradeDocVatMethod::BASE_ON_GROSS:
                        $set('gross_unit_price', $product->getGrossPriceInt() / 100);
                        $set('vat_label', $product->vat_label);
                        break;
                }
                $this->calculate($set);
            })
            ->options(Products::pluck('name', 'id')->toArray())
            ->createOptionForm(ProductsResource::getCreateModalFormSchema())
            ->createOptionUsing(function (array $data) {
                return ProductsRepository::createFromModalForm($data)?->id;
            })
            ->inlineLabel(true)
            ->label(__('app.trade_docs.add_item.product_list'));
        $schema[] = Forms\Components\Grid::make(1)
            ->schema([
                $select
            ]);

        return $schema;
    }

    protected function addStandardFields(Form $form, array $schema): array
    {
        $default = [
            Forms\Components\Textarea::make('label')
                ->label(__('app.trade_docs.add_item.label'))
                ->required()
                ->columnSpanFull()
                ->maxLength(512),
            Forms\Components\Grid::make(4)
                ->schema([
                    Forms\Components\TextInput::make('net_unit_price')
                        ->extraAlpineAttributes(
                            Filters::getFilter(['price' => 'keypress.self'])
                        )
                        ->stripCharacters(' ')
                        ->default(0.0)
                        ->inputMode('decimal')
                        ->label(__('app.trade_docs.add_item.net_unit_price'))
                        ->live(onBlur: true)
                        ->afterStateUpdated(
                            function (Forms\Set $set, $state) {
                                if ($state === '0') {
                                    return;
                                }
                                $this->docItem['net_unit_price'] = Str::of($state)->replace(',', '.')->toFloat();
                                $this->calculate($set);
                            }
                        )
                        ->visible(fn() => $this->getOwnerRecord()->vat_method === TradeDocVatMethod::BASE_ON_NET)
                        ->dehydratedWhenHidden(true)
                        ->required(),
                    Forms\Components\TextInput::make('gross_unit_price')
                        ->default(0.0)
                        ->required()
                        ->stripCharacters(' ')
                        ->extraAlpineAttributes(
                            Filters::getFilter(['price' => 'keypress.self'])
                        )
                        ->live(onBlur: true)
                        ->afterStateUpdated(
                            function (Forms\Set $set, $state, Forms\Components\Component $component) {
                                $this->validateOnly('gross_unit_price');
                                if (blank($state)) {
                                    $component->state(0);
                                }
                                $this->docItem['gross_unit_price'] = Str::of($state)->replace(',', '.')->toFloat();
                                $this->calculate($set);
                            }
                        )
                        ->dehydratedWhenHidden(true)
                        ->visible(fn() => $this->getOwnerRecord()->vat_method === TradeDocVatMethod::BASE_ON_GROSS)
                        ->label(__('app.trade_docs.add_item.gross_unit_price')),
                    Forms\Components\Select::make('discount_type')
                        ->label(__('app.trade_docs.add_item.discount_type'))
                        ->options(TradeDocDiscountTypes::toArrayWithLabels())
                        ->live()
                        ->selectablePlaceholder(false)
                        ->afterStateUpdated(
                            function (Forms\Set $set, $state) {
                                if ((int)$state === 0) {
                                    $set('discount_value', 0);
                                }
                                $this->calculate($set);
                            }
                        )
                        ->default(0)
                        ->required(),
                    Forms\Components\TextInput::make('discount_value')
                        ->disabled(fn(Forms\Get $get) => (int)$get('discount_type') === 0)
                        ->minValue(0)
                        ->live()
                        ->afterStateUpdated(
                            function (Forms\Set $set, $state) {
                                if (null === $state) {
                                    $set('discount_value', 0);
                                    return;
                                }
                                $this->calculate($set);
                            }
                        )
                        ->default(0)
                        ->numeric()
                        ->label(__('app.trade_docs.add_item.discount_value')),
                    Forms\Components\TextInput::make('discounted_unit_price')
                        ->default(0.0)
                        ->label(__('app.trade_docs.add_item.discounted_unit_price')),
                ]),
            Forms\Components\Grid::make(6)
                ->schema([
                    Forms\Components\TextInput::make('amount')
                        ->default(1)
                        ->minValue(0.01)
                        ->required()
                        ->live(onBlur: true)
                        ->afterStateUpdated(
                            function (Forms\Set $set, $state, Forms\Components\Component $component) {
                                if ($state === null) {
                                    $component->state(0);
                                }
                                $this->calculate($set);
                            }
                        )
                        ->numeric()
                        ->label(__('app.trade_docs.add_item.amount')),
                    Forms\Components\TextInput::make('unit_type')
                        ->default('szt.')
                        ->required()
                        ->label(__('app.trade_docs.add_item.unit_type')),
                    Forms\Components\TextInput::make('net_value')
                        ->default(0.0)
                        ->label(__('app.trade_docs.add_item.net_value')),
                    Forms\Components\Select::make('vat_label')
                        ->options(fn() => VatRates::getRatesForSelect())
                        ->live()
                        ->afterStateUpdated(
                            function (Forms\Set $set, $state) {
                                $set('vat_rate', VatRates::getRate($state));
                                $this->calculate($set);
                            }
                        )
                        ->default(23)
                        ->label(__('app.trade_docs.add_item.vat_label')),
                    Forms\Components\TextInput::make('vat_rate')
                        ->hidden()
                        ->dehydratedWhenHidden(true)
                        ->numeric()
                        ->default(23)
                        ->label(__('app.trade_docs.add_item.vat_rate')),
                    Forms\Components\TextInput::make('vat_value')
                        ->numeric()
                        ->default(0.0)
                        ->mask(
                            RawJs::make('$money($input, \'.\', \'\')')
                        )
                        ->label(__('app.trade_docs.add_item.vat_value')),
                    Forms\Components\TextInput::make('gross_value')
                        ->numeric()
                        ->default(0.0)
                        ->mask(
                            RawJs::make('$money($input, \'.\', \'\')')
                        )
                        ->label(__('app.trade_docs.add_item.gross_value')),
                ])
        ];
        return array_merge($schema, $default);
    }

    protected function addStandardFAUPFields(Form $form, array $schema): array
    {
        $default = [
            Forms\Components\Textarea::make('label')
                ->label(__('app.trade_docs.add_item.label'))
                ->required()
                ->columnSpanFull()
                ->maxLength(512),
            Forms\Components\Grid::make(4)
                ->schema([
                    Forms\Components\TextInput::make('gross_unit_price')
                        ->default(0.0)
                        ->required()
                        ->stripCharacters(' ')
                        ->extraAlpineAttributes(
                            Filters::getFilter(['price' => 'keypress.self'])
                        )
                        ->live(onBlur: true)
                        ->afterStateUpdated(
                            function (Forms\Set $set, $state, Forms\Components\Component $component) {
                                $this->validateOnly('gross_unit_price');
                                if (blank($state)) {
                                    $component->state(0);
                                }
                                $this->docItem['gross_unit_price'] = Str::of($state)->replace(',', '.')->toFloat();
                                $this->calculate($set);
                            }
                        )
                        ->dehydratedWhenHidden(true)
                        ->label(__('app.trade_docs.add_item.gross_unit_price')),
                    Forms\Components\Select::make('discount_type')
                        ->label(__('app.trade_docs.add_item.discount_type'))
                        ->options(TradeDocDiscountTypes::toArrayWithLabels())
                        ->live()
                        ->selectablePlaceholder(false)
                        ->afterStateUpdated(
                            function (Forms\Set $set, $state) {
                                if ((int)$state === 0) {
                                    $set('discount_value', 0);
                                }
                                $this->calculate($set);
                            }
                        )
                        ->default(0)
                        ->required(),
                    Forms\Components\TextInput::make('discount_value')
                        ->disabled(fn(Forms\Get $get) => (int)$get('discount_type') === 0)
                        ->minValue(0)
                        ->live()
                        ->afterStateUpdated(
                            function (Forms\Set $set, $state) {
                                if (null === $state) {
                                    $set('discount_value', 0);
                                    return;
                                }
                                $this->calculate($set);
                            }
                        )
                        ->default(0)
                        ->numeric()
                        ->label(__('app.trade_docs.add_item.discount_value')),
                    Forms\Components\TextInput::make('discounted_unit_price')
                        ->default(0.0)
                        ->label(__('app.trade_docs.add_item.discounted_unit_price')),
                ]),
            Forms\Components\Grid::make(3)
                ->schema([
                    Forms\Components\TextInput::make('amount')
                        ->default(1)
                        ->minValue(0.01)
                        ->required()
                        ->live(onBlur: true)
                        ->afterStateUpdated(
                            function (Forms\Set $set, $state, Forms\Components\Component $component) {
                                if ($state === null) {
                                    $component->state(0);
                                }
                                $this->calculate($set);
                            }
                        )
                        ->numeric()
                        ->label(__('app.trade_docs.add_item.amount')),
                    Forms\Components\TextInput::make('unit_type')
                        ->default('szt.')
                        ->label(__('app.trade_docs.add_item.unit_type')),
                    Forms\Components\TextInput::make('gross_value')
                        ->numeric()
                        ->default(0.0)
                        ->mask(
                            RawJs::make('$money($input, \'.\', \'\')')
                        )
                        ->label(__('app.trade_docs.add_item.gross_value')),
                ])
        ];
        return array_merge($schema, $default);
    }

    protected function addTagsInput(Form $form, array $schema): array
    {
        $tags = Forms\Components\TagsInput::make('tags')
            ->live()
            ->afterStateUpdated(function ($state, $set) {
                $modifiedTags = array_map(function ($tag) {
                    return strtolower(trim($tag));
                }, $state);

                $set('tags', $modifiedTags);
            })
            ->suggestions([
                'tag1',
                'tag2',
                'tag3',
            ])
            ->label(__('app.trade_docs.add_item.tags'));
        return array_merge($schema, [$tags]);
    }


    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('label')
            ->columns([
                Tables\Columns\TextColumn::make('label')
                    ->wrap(true)
                    ->label(__('app.trade_docs.add_item.label')),
                Tables\Columns\TextColumn::make('net_unit_price')
                    ->label(__('app.trade_docs.add_item.net_unit_price'))
                    ->alignRight()
                    ->visible(
                        fn() => $this->getOwnerRecord()->vat_method === TradeDocVatMethod::BASE_ON_NET
                    ),
                Tables\Columns\TextColumn::make('gross_unit_price')
                    ->label(__('app.trade_docs.add_item.gross_unit_price'))
                    ->alignRight()
                    ->visible(
                        fn() => $this->getOwnerRecord()->vat_method === TradeDocVatMethod::BASE_ON_GROSS
                    ),
                Tables\Columns\TextColumn::make('discount_value')
                    ->toggleable(true)
                    ->toggledHiddenByDefault(true)
                    ->alignRight()
                    ->label(__('app.trade_docs.add_item.discount_value')),
                Tables\Columns\TextColumn::make('discounted_unit_price')
                    ->alignRight()
                    ->label(__('app.trade_docs.add_item.discounted_unit_price')),
                Tables\Columns\TextColumn::make('amount')
                    ->alignRight()
                    ->label(__('app.trade_docs.add_item.amount'))
                    ->formatStateUsing(
                        fn($state) => number_format($state, 2, '.', '')
                    ),
                Tables\Columns\TextColumn::make('unit_type')
                    ->alignCenter()
                    ->label(__('app.trade_docs.add_item.unit_type')),
                Tables\Columns\TextColumn::make('vat_label')
                    ->alignCenter()
                    ->hidden(fn() => $this->getOwnerRecord()->type === DocumentTypes::FAUP)
                    ->label(__('app.trade_docs.add_item.vat_label')),
                Tables\Columns\TextColumn::make('net_value')
                    ->alignRight()
                    ->label(__('app.trade_docs.add_item.net_value'))
                    ->visible(
                        fn() => $this->getOwnerRecord()->vat_method === TradeDocVatMethod::BASE_ON_NET
                    ),
                Tables\Columns\TextColumn::make('gross_value')
                    ->alignRight()
                    ->label(__('app.trade_docs.add_item.gross_value'))
                    ->visible(
                        fn() => $this->getOwnerRecord()->vat_method === TradeDocVatMethod::BASE_ON_GROSS
                    ),
                Tables\Columns\TextColumn::make('tags')
                    ->toggleable(true)
                    ->toggledHiddenByDefault(true)
                    ->searchable()

            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->modalHeading('Dodaj produkt do dokumentu')
                    ->modalDescription('Dodaj lub edytuj produkt')
                    ->using(function (array $data, HasTable $livewire) {
                        return TradeDocsRepository::createDocItem($this->getOwnerRecord(), $data);
                    })
                    ->after(fn() => $this->dispatch('itemCreated')),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->modalHeading('Edytuj produkt')
                    ->iconButton()
                    ->modalDescription('Dodaj lub edytuj produkt')
                    ->using(function (array $data, TradeDocItem $record) {
                        $docRecord = $this->getOwnerRecord();
                        TradeDocsRepository::updateDocItem($docRecord, $record, $data);
                        $this->dispatch('itemUpdated');
                    }),
                Tables\Actions\DeleteAction::make()
                    ->iconButton()
                    ->after(function (TradeDocItem $record) {
                        $docRecord = $this->getOwnerRecord();
                        TradeDocsRepository::updateDocSummary($docRecord);
                        $this->dispatch('itemRemoved');
                    }),
            ])
            ->searchable(true)
            ->bulkActions([]);
    }

    public function getUnitPrice(TradeDocItem $record): string
    {
        return match ($this->getOwnerRecord()->vat_method) {
            TradeDocVatMethod::BASE_ON_NET => $record->net_unit_price,
            default => $record->gross_unit_price,
        };
    }

    public function getFilter(string $key): array
    {
        return match ($key) {
            'net_unit_price' =>
            [
                'x-on:keypress.self' => '(event) => !filterInput.price(event) && event.preventDefault()',
            ],
            default => []
        };
    }
}
