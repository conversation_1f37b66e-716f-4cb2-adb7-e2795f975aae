<?php

namespace App\Filament\App\Resources\TradeDocResource\Forms;

use App\Enums\DocumentTypes;
use App\Enums\DocumentTypes as DocType;
use App\Filament\App\Resources\TradeDocResource\Pages\AddTradeDocItem;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Infolist;
use Filament\Support\Enums\Alignment;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;

class TradeDocView
{
    public static function getDocPreview($record)
    {
        $infolist = Infolist::make();
        $infolist->schema([
            TextEntry::make('full_doc_number')
        ])
            ->record($record);

        return $infolist;
    }

    public static function getFooterActions(Infolist $infolist, $record)
    {
    }

    public static function getInfolistSchema($record): array
    {
        return [
            Grid::make([
                'default' => 2,
                'md' => 2,
                'lg' => 2,
                'xl' => 2,
                '2xl' => 2,
            ])
                ->schema([
                    TextEntry::make('meta.getSellerData')
                        ->state(new HtmlString($record->meta?->getSellerData('html') ?? 'Brak danych'))
                        ->columnSpan(1)
                        ->size(TextEntry\TextEntrySize::Small)
                        ->label(__('app.trade_docs.create.seller') . ':'),
                    TextEntry::make('meta.getBuyerData')
                        ->columnSpan(1)
                        ->size(TextEntry\TextEntrySize::Small)
                        ->state(new HtmlString($record->meta?->getBuyerData('html') ?? 'Brak danych'))
                        ->label(__('app.trade_docs.create.buyer_data') . ':'),
                ]),
            ViewEntry::make('line')
                ->columnSpanFull()
                ->hiddenLabel(true)
                ->view('filament.app.infolists.separator'),
            Grid::make([
                'default' => 2,
                'md' => 4,
                'lg' => 4,
                'xl' => 4,
                '2xl' => 4,
            ])
                ->schema([
                    TextEntry::make('meta.bank_account')
                        ->size(TextEntry\TextEntrySize::ExtraSmall)
                        ->inlineLabel(false)
                        ->columnSpan([
                            'sm' => 2,
                            'md' => 2,
                            'default' => 2,
                        ])
                        ->state(
                            function ($record) {
                                $bankData = $record->meta?->getBankData();
                                if (blank($bankData?->bank_account)) {
                                    return 'Brak danych';
                                }
                                return $bankData->bank_account . ' (' . $bankData->bank_name . ')';
                            }
                        )
                        ->label(__('app.trade_docs.create.bank_account') . ':'),
                    TextEntry::make('currency')
                        ->inlineLabel(false)
                        ->size(TextEntry\TextEntrySize::ExtraSmall)
                        ->columnSpan(1)
                        ->label(__('app.trade_docs.create.currency') . ':')
                        ->formatStateUsing(function ($record) {
                            return match ($record->currency) {
                                'PLN' => 'PLN',
                                default => $record->currency . ' (' . $record->exchange_rate . ')',
                            };
                        })
                        ->default('PLN'),
                    TextEntry::make('payment_type')
                        ->size(TextEntry\TextEntrySize::ExtraSmall)
                        ->inlineLabel(false)
                        ->columnSpan(1)
                        ->formatStateUsing(fn($state) => $state->label())
                        ->label(__('app.trade_docs.create.payment_type') . ':'),
                ]),
            ViewEntry::make('line')
                ->columnSpanFull()
                ->hiddenLabel(true)
                ->view('filament.app.infolists.separator'),
            Grid::make([
                'default' => 2,
                'md' => 2,
                'lg' => 4,
                'xl' => 4,
                '2xl' => 4,
            ])
                ->schema([
                    TextEntry::make('issued_at')
                        ->size(TextEntry\TextEntrySize::ExtraSmall)
                        ->columnSpan(1)
                        ->state(fn(Model $record) => match (filled($record->issued_at)) {
                            false => null,
                            default => $record->issued_at->format('Y-m-d')
                        })
                        ->inlineLabel(false)
                        ->label(__('app.trade_docs.create.doc_date')),
                    TextEntry::make('sells_date')
                        ->columnSpan(1)
                        ->size(TextEntry\TextEntrySize::ExtraSmall)
                        ->dateTime('Y-m-d', 'Europe/Warsaw')
                        ->inlineLabel(false)
                        ->label(__('app.trade_docs.create.sells_date')),
                    TextEntry::make('payment_due_date')
                        ->columnSpan(1)
                        ->size(TextEntry\TextEntrySize::ExtraSmall)
                        ->dateTime('Y-m-d', 'Europe/Warsaw')
                        ->inlineLabel(false)
                        ->label(__('app.trade_docs.create.payment_due_date'))
                        ->default(''),
                    TextEntry::make('is_paid')
                        ->columnSpan(1)
                        ->size(TextEntry\TextEntrySize::ExtraSmall)
                        ->inlineLabel(false)
                        ->formatStateUsing(
                            fn($state) => $state ? $record->payment_date->format('Y-m-d') : 'Nie'
                        )
                        ->label(__('app.trade_docs.create.is_paid') . ':'),
                ]),
            ViewEntry::make('line')
                ->columnSpanFull()
                ->hiddenLabel(true)
                ->view('filament.app.infolists.separator'),
            Grid::make(2)
                ->schema([
                    ViewEntry::make('options')
                        ->state(fn($record) => $record)
                        ->visible(fn($record) => $record->type !== DocType::FAUP)
                        ->view('filament.app.infolists.options')
                        ->extraAttributes(['text_size' => 'text-xs'])
                        ->label(__('app.trade_docs.create.options')),
                    TextEntry::make('meta.notes')
                        ->html(true)
                        ->alignment(Alignment::Justify)
                        ->extraAttributes(['text_size' => 'text-xs'])
                        ->state(
                            fn($record) => $record->type === DocType::FVK ?
                                'Przyczyna korekty: ' . $record->notes :
                                match (filled($note = $record->getMeta()->getNote())) {
                                    true => 'Notka: '. $record->getMeta()->getNote(),
                                    default => ''
                                }
                        )
                        ->hiddenLabel(true)
                        ->label(__('app.trade_docs.create.notes')),
                ]),
            ViewEntry::make('line')
                ->columnSpanFull()
                ->hiddenLabel(true)
                ->view('filament.app.infolists.separator'),

            ViewEntry::make('items')
                ->columnSpanFull()
                ->view(
                    self::getItemsView($record),
                    ['items' => $record->items, 'record' => $record]
                ),

            ViewEntry::make('vatsummary')
                ->extraAttributes(['text_size' => 'text-xs'])
                ->visible(
                    fn($record) => AddTradeDocItem::isVatSummaryVisible($record)
                )
                ->view(
                    AddTradeDocItem::getVatSummaryView($record)
                )
                ->state(
                    fn($record) => AddTradeDocItem::getVatSummaryState($record)
                ),
        ];
    }
    protected static function getItemsView($record): string
    {
        return match ($record->type) {
            DocumentTypes::FVS => 'filament.app.infolists.items',
            DocumentTypes::FAUP => 'filament.app.infolists.items',
            DocumentTypes::FVK => 'filament.app.infolists.items_fvk',
            default => 'filament.app.infolists.items',
        };
    }
}
