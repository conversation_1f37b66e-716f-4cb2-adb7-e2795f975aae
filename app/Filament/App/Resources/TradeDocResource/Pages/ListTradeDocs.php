<?php

namespace App\Filament\App\Resources\TradeDocResource\Pages;

use App\Filament\App\Resources\TradeDocResource;
use App\Models\TradeDoc;
use App\Services\ExportJPK;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use pxlrbt\FilamentExcel\Actions\Pages\ExportAction;
use pxlrbt\FilamentExcel\Columns\Column;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use function Livewire\invade;

class ListTradeDocs extends ListRecords
{
    protected static string $resource = TradeDocResource::class;

    public $currentRecord = TradeDoc::class;

    public function getPluralModelLabel(): ?string
    {
        return strtolower(__('app.trade_docs._.heading'));
    }

    public function getHeading(): string|Htmlable
    {
        return __('app.trade_docs._.heading');
    }

    /**
     * @TODO: refactor!
     */
    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Actions\Action::make('exportJPK')
                ->label('Export JPK')
                ->icon('heroicon-o-arrow-down-tray')
                ->action(function () {
                    $data = $this->getTableQueryForExport()->get();
                    $tenant = auth()->user()?->getTenant();
                    if (blank($tenant)) {
                        Notification::make()
                            ->title('Błąd eksportu JPK - Błędne dane firmy')
                            ->danger()
                            ->send();
                        $this->halt();
                    }
                    $date = Carbon::now();
                    $user = clone(auth()->user());
                    $user->surname = 'Test';
                    $user->birthdate = '1970-01-01';
                    $export = new ExportJPK();
                    try {
                        $stream = $export->exportJPKV7M($data, $tenant, $date, $user);
                        return response()->streamDownload(
                            function () use ($stream) {
                                echo $stream;
                            },
                            'export_vat7m_' . $tenant->vat_id . '_' . $date->format('YmdHis') . '.xml',
                            ['application/xml']
                        );
                    } catch (\Exception $e) {
                        Log::error($e->getMessage());
                        Notification::make()
                            ->title('Błąd eksportu JPK')
                            ->danger()
                            ->send();
                        return redirect()->back();
                    }
                }),
            ExportAction::make('tableHeader')
                ->exports([
                    ExcelExport::make()
                        ->askForWriterType()
                        ->withFilename(function () {
                            return 'faktury_' . now('Europe/Warsaw')->format('Y-m-d_H-i');
                        })
                        ->withColumns(function () {
                            return $this->createFieldMappingFromTable()
                                ->map(
                                    fn(Column $column) => in_array(
                                        $column->getName(),
                                        ['gross', 'vat_amount', 'net_value']
                                    ) ?
                                        $column
                                            ->format(NumberFormat::FORMAT_NUMBER_00)
                                            ->formatStateUsing(fn($state) => number_format($state, 2, '.', '')) :
                                        $column
                                )
                                ->toArray();
                        })
                        ->fromTable()
                ])
        ];
    }

    public function triggerAction(TradeDoc $tradeDoc): void
    {
        $this->currentRecord = $tradeDoc;
        $this->mountAction('viewMyRecordAction');
    }

    public function viewMyRecordAction()
    {
        $action = Actions\Action::make('viewMyRecord')
            ->record($this->currentRecord)
            ->slideOver(true)
            ->modalHeading(fn(TradeDoc $record) => $record->type->label() . ': ' . $record->full_doc_number)
            ->infolist(TradeDocResource\Forms\TradeDocView::getInfolistSchema($this->currentRecord));
        $action->modalFooterActions([
            $action->getModalCancelAction()->label('Zamknij'),
            Tables\Actions\Action::make('additem')
                ->url(
                    function (TradeDoc $record) {
                        return self::getResource()::getUrl('add-item', ['record' => $record->transaction_id]);
                    }
                )
                ->label('Edycja')
                ->icon('heroicon-o-queue-list'),
            Tables\Actions\Action::make('print_doc')
                ->label('PDF')
                ->icon('heroicon-o-printer')
                ->url(
                    fn(TradeDoc $record) => route('print', [
                        'doctype' => strtolower($record->type->getGeneralType()->value),
                        'doc' => $record->transaction_id,
                        'output' => 'pdf'
                    ]),
                    true
                ),
        ]);
        return $action;
    }

    protected function createFieldMappingFromTable(): Collection
    {
        $livewire = $this;

        if ($livewire instanceof HasTable) {
            $columns = collect($livewire->getTable()->getColumns());
        } else {
            $table = $this->getResourceClass()::table(new Table);
            $columns = collect($table->getColumns());
        }

        return $columns
            ->when(
                $livewire->getTable()->hasToggleableColumns(),
                fn($collection) => $collection->reject(
                    fn(Tables\Columns\Column $column) => $livewire->isTableColumnToggledHidden($column->getName())
                )
            )
            ->mapWithKeys(function (Tables\Columns\Column $column) {
                $clonedCol = clone $column;

                $invadedColumn = invade($clonedCol);

                $exportColumn = Column::make($column->getName())
                    ->heading($column->getLabel())
                    ->getStateUsing($invadedColumn->getStateUsing)
                    ->tableColumn($clonedCol);

                rescue(fn() => $exportColumn->formatStateUsing($invadedColumn->formatStateUsing), report: false);

                return [
                    $column->getName() => $exportColumn,
                ];
            });
    }
}
