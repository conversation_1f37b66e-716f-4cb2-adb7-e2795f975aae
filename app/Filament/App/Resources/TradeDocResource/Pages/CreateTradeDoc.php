<?php

namespace App\Filament\App\Resources\TradeDocResource\Pages;

use App\Enums\DocumentTypes;
use App\Enums\PaymentTypes;
use App\Enums\SystemModules;
use App\Enums\TradeDocVatMethod;
use App\Filament\App\Resources\TradeDocResource;
use App\Filament\Traits\HasBackToListButton;
use App\Models\DocumentSeriesPattern;
use App\Models\TradeDoc;
use App\Repositories\CurrenciesRepository;
use App\Repositories\CurrencyRatesExchangeRepository;
use App\Repositories\DocumentSeriesRepository;
use App\Repositories\TradeDocsRepository;
use App\Services\Filters;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\RawJs;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

class CreateTradeDoc extends CreateRecord
{

    use HasBackToListButton;

    protected static string $resource = TradeDocResource::class;

    public $ex_rate_date = null;

    public static function getNavigationLabel(): string
    {
        return __('app.trade_docs.navigation.create-sell-label');
    }

    public static function getNavigationIcon(): string|Htmlable|null
    {
        return 'heroicon-o-document-text';
    }

    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return tenant()?->hasModule(SystemModules::INVOICES) ?? false;
    }

    /**
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return __('app.trade_docs.create.page.heading');
    }

    public function form(Form $form): Form
    {
        return TradeDocResource\Forms\TradeDocForm::createForm($form);
    }

    public function getDefaultPDD(Get $get): string
    {
        return Carbon::make($get('issued_at'))
            ?->addDays(7)
            ->format('Y-m-d');
    }

    public function setPaymentDueDate(Get $get, $state): string
    {
        return Carbon::make($get('issued_at'))
            ?->endOfDay()
            ->addDays($state)->format('Y-m-d');
    }

    public function setPaymentCreditDays(Get $get, $state): string
    {
        return Carbon::make($state)
            ?->diffInDays(Carbon::make($get('issued_at'))->startOfDay());
    }

    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction(),
            self::getBackToListFormAction(),
        ];
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['type'] = DocumentTypes::tryFrom($data['type']);
        true !== ($data['meta']['options']['different_seller'] ?? false) && $data['seller_id'] = null;
        return parent::mutateFormDataBeforeCreate($data);
    }

    public function handleRecordCreation(array $data): TradeDoc
    {
        $meta = $data['meta'] ?? [];
        unset($data['meta']);
        $prepaidInvoices = [];
        if ($data['is_final_invoice'] ?? false) {
            $prepaidInvoices = $data['prepaidInvoices'] ?? [];
        }
        unset($data['prepaidInvoices']);
        $record = new TradeDoc(Arr::except($data, 'buyerdata'));
        $record->installation = auth()->user()?->installation();
        $record->creator_id = auth()->user()->id;
        $record->issuer_id = tenant()->id;
        if ($record->type === DocumentTypes::FAUP) {
            $record->vat_method = TradeDocVatMethod::BASE_ON_GROSS;
        }
        TradeDocsRepository::createDocNumberOnModel($record);
        TradeDocsRepository::createTransactionOnModel($record);
        $record->save();
        TradeDocsRepository::createMetaOnModel($record, $meta, $data);
        if (filled($prepaidInvoices)) {
            TradeDocsRepository::finalInvoiceProcess($record, $prepaidInvoices);
        }
        return $record;
    }

    protected function getRedirectUrl(): string
    {
        return self::getResource()::getUrl('add-item', ['record' => $this->getRecord()->transaction_id]);
    }
}
