<?php

namespace App\Filament\App\Resources\TradeDocResource\Pages;

use App\Enums\DocumentTypes as DocType;
use App\Filament\App\Resources\TradeDocResource;
use App\Models\TradeDoc;
use App\Models\WarehouseDoc;
use App\Repositories\DocumentSeriesRepository;
use App\Repositories\TradeDocsRepository;
use App\Repositories\WarehouseDocsRepository;
use Barryvdh\DomPDF\Facade\Pdf;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Actions\EditAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Components\Fieldset;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Livewire\Attributes\On;

class AddTradeDocItem extends ViewRecord
{
    use InteractsWithInfolists;
    use InteractsWithFormActions;

    protected static string $resource = TradeDocResource::class;

    public string $transaction_id;
    public bool $accepted = false;

    public ?string $previousUrl = null;

    public array $items = [];

    public function mount($record): void
    {
        $this->transaction_id = $record;
        $this->loadParentRecord($this->transaction_id);
    }

    protected function getForms(): array
    {
        return [
            'form',
        ];
    }


    public function getHeading(): string|Htmlable
    {
        $title = $this->record->type->label() . ' ' . $this->record->full_doc_number;

        if ($this->record->has_correction) {
            $title .= '  <small style="color: orangered;">(wystawiona korekta)</small>';
        }

        return new HtmlString($title);
    }

    protected function loadParentRecord($record)
    {
        $this->record = TradeDoc::where('transaction_id', $record)->first();
        if (empty($this->record)) {
            abort(404);
        }
        $this->accepted = (bool)$this->record->is_accepted;
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return parent::infolist($infolist)
            ->columns(3)
            ->record($this->record)
            ->schema([
                Grid::make(3)
                    ->columnSpanFull()
                    ->schema([
                        Fieldset::make('Nabywca')
                            ->columnSpan(1)
                            ->extraAttributes(['class' => 'h-full'])
                            ->columns(1)
                            ->schema([
                                TextEntry::make('meta.getBuyerData')
                                    ->inlineLabel()
                                    ->state(new HtmlString($this->record->meta?->getBuyerData('html') ?? 'Brak danych'))
                                    ->label(__('app.trade_docs.create.buyer_data') . ':'),
                                TextEntry::make('related')
                                    ->label(__('app.trade_docs.create.related_doc'))
                                    ->inlineLabel()
                                    ->hidden(fn(Model $record) => $this->relatedDocumentState($record) === null)
                                    ->state(fn(Model $record) => $this->relatedDocumentState($record))
                                    ->url(fn(Model $record) => $this->relatedDocumentUrl($record)),
                                TextEntry::make('has_correction')
                                    ->label('Korekta?')
                                    ->inlineLabel()
                                    ->hidden(fn($state) => $state === false)
                                    ->formatStateUsing(
                                        function ($state, Model $record) {
                                            if ($state) {
                                                return TradeDoc::where('source_id', $record->uuid)
                                                    ->first()->transaction_id;
                                            }
                                            return 'Nie';
                                        }
                                    )
                                    ->url(
                                        fn(Model $record) => self::getResource()::getUrl(
                                            'add-item',
                                            [
                                                'record' => TradeDoc::where('source_id', $record->uuid)
                                                    ->first()->transaction_id
                                            ]
                                        )
                                    ),
                            ]),
                        Fieldset::make(__('app.trade_docs.create.payment_type'))
                            ->columnSpan(1)
                            ->columns(1)
                            ->extraAttributes(['class' => 'h-full'])
                            ->schema([
                                TextEntry::make('currency')
                                    ->inlineLabel()
                                    ->label(__('app.trade_docs.create.currency') . ':')
                                    ->default('PLN'),
                                TextEntry::make('exchange_rate')
                                    ->inlineLabel()
                                    ->label(__('app.trade_docs._.exchange_rate') . ':')
                                    ->visible(fn($record): bool => $record->currency !== 'PLN')
                                    ->formatStateUsing(
                                        function ($state, TradeDoc $record): string {
                                            return $state . ' (' . $record->currency_rate_date . ')';
                                        }
                                    )
                                    ->default('PLN'),
                                TextEntry::make('meta.bank_account')
                                    ->inlineLabel()
                                    ->tooltip(
                                        fn($state, $record) => $record->meta?->getBankDataItem('bank_name') ?? ''
                                    )
                                    ->state(
                                        $this->record->meta?->getBankDataItem('bank_account') ?? 'Brak danych'
                                    )
                                    ->label(__('app.trade_docs.create.bank_account') . ':'),
                                TextEntry::make('payment_type')
                                    ->inlineLabel()
                                    ->formatStateUsing(fn($state) => $state->label())
                                    ->label(__('app.trade_docs.create.payment_type') . ':'),
                                TextEntry::make('is_paid')
                                    ->inlineLabel()
                                    ->formatStateUsing(
                                        fn($state) => $state ? $this->record->payment_date->format('Y-m-d') : 'Nie'
                                    )
                                    ->label(__('app.trade_docs.create.is_paid') . ':'),

                            ]),
                        Fieldset::make(__('app.trade_docs.create.dates'))
                            ->columnSpan(1)
                            ->columns(1)
                            ->extraAttributes(['class' => 'h-full'])
                            ->schema([
                                TextEntry::make('issued_at')
                                    ->columnSpanFull()
                                    ->state(fn(Model $record) => match (filled($record->issued_at)) {
                                        false => null,
                                        default => $record->issued_at->format('Y-m-d')
                                    })
                                    ->inlineLabel()
                                    ->label(__('app.trade_docs.create.doc_date')),
                                TextEntry::make('sells_date')
                                    ->dateTime('Y-m-d', 'Europe/Warsaw')
                                    ->inlineLabel()
                                    ->label(__('app.trade_docs.create.sells_date')),
                                TextEntry::make('payment_due_date')
                                    ->dateTime('Y-m-d', 'Europe/Warsaw')
                                    ->inlineLabel()
                                    ->label(__('app.trade_docs.create.payment_due_date'))
                                    ->default(''),
                                TextEntry::make('created_at')
                                    ->dateTime('Y-m-d H:i:s', 'Europe/Warsaw')
                                    ->default('')
                                    ->inlineLabel()
                                    ->label(__('app.trade_docs.create.created_at')),
                                TextEntry::make('cancelled_by')
                                    ->state(fn(Model $record) => $record->cancelledby->email)
                                    ->default('')
                                    ->inlineLabel()
                                    ->visible(fn(Model $record) => $record->cancelled_at)
                                    ->label('Anulowany przez'),

                            ]),
                    ]),
                Grid::make(9)
                    ->columnSpanFull()
                    ->schema([
                        Fieldset::make(__('app.trade_docs.create.notes'))
                            ->extraAttributes(['class' => 'h-full'])
                            ->columnSpan(
                                function ($record) {
                                    return (
                                        $record->type === DocType::FVS ||
                                        $record->type === DocType::FVK ||
                                        $record->type === DocType::FVP
                                    ) ? 2 : 5;
                                }
                            )
                            ->schema([
                                TextEntry::make('meta.notes')
                                    ->html(true)
                                    ->state(
                                        fn($record) => $record->type === DocType::FVK ?
                                            'Przyczyna: ' . $record->notes :
                                            $record->getMeta()->getNote()
                                    )
                                    ->columnSpanFull()
                                    ->hiddenLabel()
                                    ->label(__('app.trade_docs.create.notes')),
                            ]),

                        Fieldset::make('options')
                            ->columnSpan(3)
                            ->extraAttributes(['class' => 'h-full'])
                            ->visible(
                                fn($record) => $this->isOptionsVisible($record)
                            )
                            ->schema([
                                ViewEntry::make('options')
                                    ->columnSpan(2)
                                    ->state(fn($record) => $record)
                                    ->view('filament.app.infolists.options')
                                    ->label(__('app.trade_docs.create.options')),
                            ])
                            ->label(__('app.trade_docs.create.options')),
                        Fieldset::make($this->getFieldsetTitle())
                            ->columnSpan(4)
                            ->extraAttributes(['class' => 'h-full'])
                            ->visible(
                                fn($record) => self::isVatSummaryVisible($record)
                            )
                            ->columns(1)
                            ->schema([
                                ViewEntry::make('vatsummary')
                                    ->view(
                                        self::getVatSummaryView($this->record)
                                    )
                                    ->state(
                                        fn ($record) => self::getVatSummaryState($record)
                                    ),
                            ])
                    ])
            ]);
    }

    public static function getVatSummaryView(Model|TradeDoc $record): string
    {
        return match ($record->type) {
            DocType::FVK => 'filament.app.infolists.vatsummary_fvk',
            DocType::FAUP => 'filament.app.infolists.vatsummary_faup',
            default => 'filament.app.infolists.vatsummary',
        };
    }

    public static function getVatSummaryState(Model|TradeDoc $record): mixed
    {
        return match ($record->type) {
            DocType::FVK => $record->getMeta()->toArray(),
            DocType::FAUP => $record,
            default => $record->getMeta()->toArray()['vat'],
        };
    }

    public static function isVatSummaryVisible(Model|TradeDoc $record): bool
    {
        $visible = [
            DocType::FVS,
            DocType::FVK,
            DocType::FVP,
            DocType::FAUP,
        ];
        return in_array($record->type, $visible, true);
    }

    public function isOptionsVisible(Model|TradeDoc $record): bool
    {
        $visible = [
            DocType::FVS,
            DocType::FVK,
            DocType::FVP
        ];
        return in_array($record->type, $visible, true);
    }

    public function relatedDocumentState(TradeDoc|Model $record): ?string
    {
        return $record->getSourceDoc()?->full_doc_number ?? 'brak';
    }

    public function relatedDocumentUrl(TradeDoc|Model $record): string
    {
        return match ($record->type) {
            default => '',
            DocType::FVK => self::getResource()::getUrl(
                'add-item',
                ['record' => TradeDoc::find($record->source_id)->transaction_id]
            ),
        };
    }

    public function relatedDocumentVisible(WarehouseDoc|Model $record): bool
    {
        return $record->accepted === 1 &&
            ($record->type === DocType::MW || $record->type === DocType::MP);
    }


    public function getHeaderActions(): array
    {
        return [
            Action::make('Lista')
                ->button()
                ->icon('heroicon-o-list-bullet')
                ->url(function () {
                    return $this->previousUrl ?? self::getResource()::getUrl('index');
                }),
            Action::make('print_doc')
                ->label('PDF')
                ->button()
                ->icon('heroicon-o-printer')
                ->url(
                    route('print', [
                        'doctype' => strtolower($this->record->type->getGeneralType()->value),
                        'doc' => $this->record->transaction_id,
                        'output' => 'pdf'
                    ]),
                    true
                ),
            Action::make('print_hmtl')
                ->label('HTML')
                ->button()
                ->icon('heroicon-o-printer')
                ->url(
                    route('print', [
                        'doctype' => strtolower($this->record->type->getGeneralType()->value),
                        'doc' => $this->record->transaction_id,
                        'output' => 'html'
                    ]),
                    true
                ),
            TradeDocResource\Forms\TradeDocForm::getHeaderPaymentAction(),
            ActionGroup::make([
                EditAction::make()
                    ->url(
                        fn(Model $record) => self::getResource()::getUrl('edit', ['record' => $record])
                    ),
                Action::make('issue_duplicate')
                    ->modalHeading('Wystaw duplikat')
                    ->icon('heroicon-o-document-duplicate')
                    ->modalWidth('xs')
                    ->label('Wystaw duplikat')
                    ->form([
                        DatePicker::make('issued_at')
                            ->label('Data duplikatu')
                            ->native(false)
                            ->format('Y-m-d')
                            ->displayFormat('Y-m-d')
                            ->default(now('Europe/Warsaw'))
                            ->required(),
                    ])
                    ->action(function (TradeDoc $record, array $data) {
                        $variant = 'l1';
                        $record->addDuplicate($data['issued_at']);
                        $meta = $record->getMeta();
                        $meta->setOption('duplicate', $data['issued_at']);
                        $template = strtolower($record->type->name) . '_' . $variant;
                        $pdf = Pdf::loadView('print.trade_docs.' . $template, [
                            'record' => $record,
                            'document' => $record->getMeta(),
                        ]);
                        $name = Str::of($record->full_doc_number)->replace('/', '_')->title()->snake() . '_duplikat';
                        return response()
                            ->streamDownload(
                                function () use ($pdf) {
                                    echo $pdf->stream();
                                },
                                $name . '.pdf',
                                [
                                    'Content-Type' => 'application/pdf'
                                ],
                            );
                    }),
                Action::make('issue_correction')
                    ->modalHeading('Wystaw korektę')
                    ->icon('heroicon-o-document-check')
                    ->modalWidth(MaxWidth::ExtraLarge)
                    ->label('Wystaw korektę')
                    ->form([
                        \Filament\Forms\Components\Grid::make(2)
                            ->schema([
                                DatePicker::make('issued_at')
                                    ->label('Data korekty')
                                    ->native(false)
                                    ->format('Y-m-d')
                                    ->displayFormat('Y-m-d')
                                    ->default(now('Europe/Warsaw'))
                                    ->required(),
                                Select::make('document_series_id')
                                    ->label('Seria numeracji')
                                    ->options(
                                        fn() =>
                                        DocumentSeriesRepository::getSeriesForDocType(DocType::FVK, true)
                                            ->pluck('name', 'id')
                                    )
                                    ->native(false)
                                    ->selectablePlaceholder(false)
                                    ->default(
                                        fn() =>
                                        DocumentSeriesRepository::getDefaultSeriesForDocType(DocType::FVK)->id
                                    )
                                    ->required(),
                                Textarea::make('reason')
                                    ->label('Powód korekty')
                                    ->columnSpanFull()
                                    ->maxLength(255)
                                    ->required(),
                            ]),

                    ])
                    ->action(function (TradeDoc $record, array $data) {
                        $newRecord = TradeDocsRepository::createInvoiceCorrection($record, $data);
                        return redirect()
                            ->to(self::getResource()::getUrl('add-item', ['record' => $newRecord->transaction_id]));
                    })
                    ->hidden(fn($record) => $record->type !== DocType::FVS || $record->has_correction),
            ]),
        ];
    }


    #[On('itemCreated')]
    public function onItemCreated()
    {
        $this->render();
    }

    #[On('itemRemoved')]
    public function onItemRemoved()
    {
        $this->render();
    }

    #[On('itemUpdated')]
    public function onItemUpdate()
    {
        $this->render();
    }

    public function acceptDoc($data, WarehouseDoc|Model $record): Model
    {
        $record->items_issue_date = $data['items_issue_date'];
        if (null === WarehouseDocsRepository::acceptDocProcess($record)) {
            Notification::make()
                ->title('Error while creating document')
                ->body(WarehouseDocsRepository::$error)
                ->danger()
                ->send();
            $this->halt();
        }

        return $record;
    }

    public function deleteActionProcess(WarehouseDoc $record): bool
    {
        return WarehouseDocsRepository::deleteWarehouseDoc($record);
    }

    public function cancelActionProcess(WarehouseDoc $record, $data): bool
    {
        return WarehouseDocsRepository::cancelWarehouseDoc($record, $data['note'] ?? null);
    }

    /**
     * Get the title for the Fieldset based on document type
     *
     * @return string
     */
    public function getFieldsetTitle(): string
    {
        if ($this->record->type === DocType::FAUP) {
            return "Podsumowanie";
        }

        return __('app.trade_docs.create.vat_summary');
    }

    public function getRelationManagers(): array
    {
        if ($this->record->type === DocType::FVK) {
            return [
                TradeDocResource\RelationManagers\FVKItemsRelationManager::class
            ];
        }
        return [
            TradeDocResource\RelationManagers\ItemsRelationManager::class
        ];
    }
}
