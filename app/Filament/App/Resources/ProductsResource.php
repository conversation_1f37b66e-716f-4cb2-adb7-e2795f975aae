<?php

namespace App\Filament\App\Resources;

use App\Enums\SystemModules;
use App\Enums\VatRates;
use App\Enums\WarehouseItemTypes;
use App\Filament\App\Resources\ProductsResource\Pages;
use App\Models\Products;
use App\Services\Filters;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class ProductsResource extends Resource
{
    protected static ?string $model = Products::class;

    protected static ?string $navigationIcon = 'heroicon-o-gift';


    public static function getNavigationLabel(): string
    {
        return __('app.products.navigation.label');
    }

    public static function getBreadcrumb(): string
    {
        return __('app.products.navigation.breadcrumb');
    }


    public static function getNavigationGroup(): ?string
    {
        return __('app._.settings');
    }


    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('app._.name'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('manufacturer.name')
                    ->label(__('app.products._.manufacturer'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('price_per_unit')
                    ->label(__('app.products._.price_per_unit'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('basic_unit')
                    ->label(__('app.products._.basic_unit'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('volume_ml')
                    ->label(__('app.products._.volume_ml'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('weight_gr')
                    ->label(__('app.products._.weight_gr'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('minimum_stock')
                    ->label(__('app.products._.minimum_stock'))
                    ->extraHeaderAttributes(
                        ['title' => 'Ostrzegaj o stanie minimalnym kiedy ilość jest równa lub mniejsza niż']
                    )
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('minimum_exp_date')
                    ->label('EXPW')
                    ->extraHeaderAttributes(
                        ['title' => 'Ostrzegaj o dacie przydatności na tyle dni przed']
                    )
                    ->wrapHeader()
                    ->numeric()
                    ->default('--')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->sortable(),
                Tables\Columns\TextColumn::make('gtin')
                    ->label(__('app.products._.gtin'))
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label(__('app._.is_active'))
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('app._.created_at'))
                    ->dateTime('Y-m-d', 'Europe/Warsaw')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('app._.updated_at'))
                    ->dateTime('Y-m-d', 'Europe/Warsaw')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('manufacturer')
                    ->relationship('manufacturer', 'name')
                    ->label('Producenci')
            ])
            ->filtersLayout(Tables\Enums\FiltersLayout::Modal)
            ->actions([
                Tables\Actions\EditAction::make()->label("")
                    ->url(fn(Model $record): string => self::getUrl('edit', ['record' => $record->hash])),
                Tables\Actions\ViewAction::make()->label('')
            ])
            ->defaultPaginationPageOption(25)
            ->defaultSort('name')
            ->headerActions([]);
    }

    public static function getCreateModalFormSchema(): array
    {
        return [
            Fieldset::make('Dane')
                ->columns(3)
                ->schema([
                    TextInput::make('name')
                        ->label(__('app._.name'))
                        ->required()
                        ->columnSpanFull()
                        ->maxLength(255),
                    Textarea::make('description')
                        ->label(__('app.products.create.description'))
                        ->maxLength(1024)
                        ->columnSpanFull(),
                    TextInput::make('basic_unit')
                        ->label(__('app.products._.basic_unit'))
                        ->default('szt'),
                    TextInput::make('gtin')
                        ->label(__('app.products._.gtin'))
                        ->hint(__('app.products._.gtin_hint'))
                        ->maxLength(20),
                    TextInput::make('extra_code')
                        ->label(__('app.products._.extra_code'))
                        ->hint(__('app.products._.extra_code_hint'))
                        ->maxLength(20),
                    TextInput::make('price_per_unit')
                        ->extraAlpineAttributes(
                            Filters::getFilter(['price' => 'keypress.self'])
                        )
                        ->stripCharacters(' ')
                        ->default(0.0)
                        ->inputMode('decimal')
                        ->label(__('app.products._.price_per_unit'))
                        ->live(onBlur: true)
                        ->afterStateUpdated(
                            function ($state, Component $component) {
                                if ($state === '0') {
                                    return;
                                }
                                $component->state(Str::of($state)->replace(',', '.')->toString());
                            }
                        )
                        ->dehydratedWhenHidden(true),
                    Select::make('is_net')
                        ->label(__('app.products._.is_net_label'))
                        ->options([
                            1 => 'Cena netto',
                            0 => 'Cena brutto',
                        ])
                        ->inlineLabel(false)
                        ->visible(fn() => tenant()?->isActiveVATEntity() ?? false)
                        ->formatStateUsing(fn($state) => tenant()?->isActiveVATEntity() ?? false ? $state : 0)
                        ->dehydratedWhenHidden(true)
                        ->default(1),
                    Select::make('vat_label')
                        ->options(fn() => VatRates::getRatesForSelect())
                        ->live()
                        ->visible(fn() => tenant()?->isActiveVATEntity() ?? false)
                        ->afterStateUpdated(
                            function (Set $set, Get $get, $state) {
                                $set('vat_rate', VatRates::getRate($state));
                            }
                        )
                        ->default(23)
                        ->dehydratedWhenHidden(true)
                        ->label(__('app.trade_docs.add_item.vat_label')),
                    TextInput::make('vat_rate')
                        ->hidden()
                        ->dehydratedWhenHidden(true)
                        ->numeric()
                        ->default(23)
                        ->label(__('app.trade_docs.add_item.vat_rate')),
                ]),
            Fieldset::make('Na magazynie')
                ->visible(fn() => tenant()?->hasModule(SystemModules::WAREHOUSE) ?? false)
                ->columns(3)
                ->schema(
                    [
                        Select::make('item_type')
                            ->options(WarehouseItemTypes::toArrayWithLabels())
                            ->label(__('app.products._.item_type'))
                            ->live()
                            ->default(WarehouseItemTypes::PRODUCT->value)
                        ,
                        TextInput::make('minimum_stock')
                            ->numeric()
                            ->hidden(
                                fn(Get $get) => (int)$get('item_type') === WarehouseItemTypes::SERVICE->value
                            )
                            ->default(5)
                            ->minValue(0)
                            ->helperText('Stan uruchamiający alarm')
                            ->label(__('app.products._.minimum_stock')),
                        TextInput::make('minimum_exp_date')
                            ->numeric()
                            ->hidden(
                                fn(Get $get) => (int)$get('item_type') === WarehouseItemTypes::SERVICE->value
                            )
                            ->minValue(0)
                            ->default(0)
                            ->helperText('Ile dni wcześniej ostrzegać o mijającym terminie przydatności')
                            ->label(__('app.products._.minimum_exp_date')),
                    ]
                )
        ];
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProducts::route('/create'),
            'edit' => Pages\EditProducts::route('/{record}/edit'),
        ];
    }
}
