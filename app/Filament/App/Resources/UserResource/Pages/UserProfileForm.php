<?php

namespace App\Filament\App\Resources\UserResource\Pages;

use App\Enums\Langs;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;

class UserProfileForm
{
    public static function getUserProfileForm()
    {
        return Section::make(__('app.users.profile.section_heading'))
            ->description(__('app.users.profile.section_description'))
            ->columns(2)
            ->schema([
                TextInput::make('name')
                    ->label(__('app.users.profile.name'))
                    ->maxLength(255),
                TextInput::make('surname')
                    ->label(__('app.users.profile.surname'))
                    ->maxLength(255),
                TextInput::make('adress')
                    ->label(__('app.users.profile.address'))
                    ->maxLength(255),
                TextInput::make('number')
                    ->label(__('app.users.profile.number'))
                    ->maxLength(255)
                    ->numeric(),
//                Select::make('lang')
//                    ->label(__('app.users.profile.lang'))
//                    ->options(Langs::toArray())
//                    ->default('pl')
            ])
            ->relationship('profile');
    }
}
