<?php

namespace App\Filament\App\Resources\UserResource\Pages;

use App\Enums\Roles;
use App\Enums\SystemModules;
use App\Filament\App\Resources\UserResource;
use App\Filament\Traits\HasBackToListButton;
use App\Models\User;
use App\Repositories\UsersRepository;
use App\Repositories\WarehouseRepository;
use Filament\Actions;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;

class EditUser extends EditRecord
{
    use HasBackToListButton;

    protected static string $resource = UserResource::class;

    protected ?string $heading = "Edycja pracownika";

    public function mount(int | string $record): void
    {
        $this->record = User::where('hash', $record)->first();
        $this->authorizeAccess();
        $this->fillForm();
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->modalHeading('Usuwanie użytkownika')
                ->modalDescription('Ta procedura jest nieodwracalna!')
                ->using(fn(Model $record) => $this->deleteTenantUser($record))
        ];
    }


    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Dane podstawowe')
                    ->schema([
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->unique(fn(Model $record) => $record->getTable(), 'email', null, true)
                            ->maxLength(255),
                        Forms\Components\Select::make('roles')
                            ->label(__('app._.role'))
                            ->required()
                            ->relationship('roles', 'name', fn($query) => $query->where('name', '!=', 'Super Admin'))
                            ->getOptionLabelFromRecordUsing(
                                fn(Model $record) => (Roles::tryFrom($record->id)?->label() ?? 'unknown')
                            ),
                    ])->columns(3),
                Section::make('Zmień hasło')
                    ->description('Wprowadź nowe hasło jeżeli chcesz je zmienić')
                    ->schema([
                        Forms\Components\Checkbox::make('set_password')
                            ->id('set_password')
                            ->dehydrated(false)
                            ->columnSpanFull()
                            ->default(fn() => false)
                            ->live()
                            ->afterStateUpdated(function (Forms\Set $set, $state) {
                                if (!$state) {
                                    $set('password', '');
                                    $set('password_confirmation', '');
                                }
                            })
                            ->label('Zmień hasło'),
                        TextInput::make('password')
                            ->label(__('app._.password'))
                            ->password()
                            ->minLength(6)
                            ->confirmed()
                            ->disabled(fn(Forms\Get $get) => $get('set_password') === false)
                            ->required(fn(Forms\Get $get) => $get('set_password') === true)
                            ->formatStateUsing(fn() => null)
                            ->dehydrated(fn($state) => $state !== null)
                            ->maxLength(255),
                        TextInput::make('password_confirmation')
                            ->label(__('app._.password_confirmation'))
                            ->password()
                            ->disabled(fn(Forms\Get $get) => $get('set_password') === false)
                            ->dehydrated(fn($state) => $state !== null)
                            ->requiredWith('password')
                            ->maxLength(255),
                    ])->columns(2),
                UserProfileForm::getUserProfileForm(),
                Forms\Components\Section::make()->schema([
                    Forms\Components\Toggle::make('active')
                        ->label('Użytkownik aktywny?')
                        ->onIcon('heroicon-m-user')
                        ->offIcon('heroicon-m-power'),
                ]),
                Section::make('Magazyn')
                    ->visible(
                        fn(Model $user) => tenant(true)?->hasModule(SystemModules::WAREHOUSE) && !$user->isTenantAdmin()
                    )
                    ->schema([
                        Forms\Components\Toggle::make('__')
                            ->key('hasWarehouse')
                            ->label(fn(User $record) => $record->warehouse?->name ?? 'brak')
                            ->dehydrated(false)
                            ->disabled(true)
                            ->formatStateUsing(fn($state, User $record) => $record->warehouse()->count() > 0),
                        Forms\Components\Actions::make([
                            Forms\Components\Actions\Action::make('Dodaj')
                                ->form([
                                    TextInput::make('name')
                                        ->label(__('app._.name'))
                                        ->hint('Nazwa magazynu')
                                ])
                                ->action(function ($data, Model $record) {
                                    $this->addWarehouseToUser($data);
                                    $record->refresh();
                                })
                                ->after(fn() => $this->fillForm())
                                ->modalHeading('Dodaj magazyn do użytkownika')
                                ->button()
                                ->hidden(fn($state, User $record) => $record->warehouse()->count() > 0)
                        ])
                    ])->columns(2),
            ]);
    }

    protected function getFormActions(): array
    {
        return [
            $this->getSubmitFormAction(),
            self::getBackToListFormAction(),
        ];
    }

    public function addWarehouseToUser($data): bool
    {
        $wh = WarehouseRepository::createUserWarehouse($this->getRecord(), $data);
        return null !== $wh;
    }

    protected function handleRecordUpdate(Model|User $record, array $data): Model
    {
        if (!empty($data['password'])) {
            if ($data['password'] === $data['password_confirmation']) {
                $data['password'] = Hash::make($data['password']);
                unset($data['password_confirmation']);
            } else {
                Notification::make()
                    ->title('Hasła nie są identyczne !')
                    ->danger()
                    ->send();
                $this->halt();
            }
        }
        $data['active'] = $data['active'] ? 1 : 0;
        $data['lang'] = 'pl';
        $record->update($data);
        return $record->fresh();
    }

    protected function deleteTenantUser(Model|User $user): bool
    {

        if (auth()->user()->id === $user->id) {
            Notification::make()
                ->title('Delete user error')
                ->body('You can\'t remove yourselves!')
                ->danger()
                ->send();
            $this->halt();
        }

        if ($user->isTenantAdmin()) {
            $anyOthers = User::query()->whereHas('roles', function ($query) {
                return $query->where('id', Roles::TENANT_ADMIN->value);
            })->whereNot('id', $user->id)->count();

            if ($anyOthers < 1) {
                Notification::make()
                    ->title('Delete user error')
                    ->body('There is no other admin users. Create one first!!!')
                    ->danger()
                    ->send();
                $this->halt();
            }

            return UsersRepository::deleteTenantUser($user);
        }

        if (!$user->hasWarehouse()) {
            return UsersRepository::deleteTenantUser($user);
        }

        $wh = $user->warehouse;
        if ($wh->items()->count() > 0) {
            Notification::make()
                ->title('Delete user error')
                ->body('User\'s warehouse has products and can\'t be deleted!')
                ->warning()
                ->send();
            $this->halt();
        }

        WarehouseRepository::removeUserWarehouse($user->warehouse);
        UsersRepository::deleteTenantUser($user);
        return true;
    }
}
