<?php

namespace App\Filament\App\Resources\UserResource\Pages;

use App\Filament\App\Resources\UserResource;
use App\Filament\Traits\HasBackToListButton;
use App\Helpers\UserHelper;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateUser extends CreateRecord
{
    use HasBackToListButton;

    protected static string $resource = UserResource::class;

    protected ?string $heading = "Dodaj pracownika";

    protected function handleRecordCreation(array $data): Model
    {
        $data['name'] = $data['email'];
        $data['lang'] = 'pl';
        $savedUser = $this->getModel()::create($data);
        UserHelper::synchronizeInstallationByModel(modelToSynchronize: $savedUser);

        return $savedUser;
    }

    protected function getRedirectUrl(): string
    {
        return self::getResource()::getUrl('edit', ['record' => $this->getRecord()->hash]);
    }

    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction(),
            $this->getCreateAnotherFormAction(),
            self::getBackToListFormAction(),
        ];
    }
}
