<?php

namespace App\Filament\Filters;

use Filament\Forms\Components\Select;
use Filament\Tables\Filters\BaseFilter;
use Filament\Tables\Filters\Concerns\HasOptions;
use Filament\Tables\Filters\Concerns\HasPlaceholder;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Eloquent\Builder;

class ExpDateWarningFilter extends BaseFilter
{
    use HasOptions, HasPlaceholder;

    protected bool $isNative = false;

    protected function setUp(): void
    {
        parent::setUp();

        $this->placeholder(
            fn() => __('filament-tables::table.filters.select.placeholder')
        );

        $this->indicateUsing(function ($state) {
            return match (empty($state['value'])) {
                true => null,
                default => match ($state['value']) {
                    default => 'Termin mija za: ' . $state['value'] . ' dni',
                    '-1' => 'Termin mija wg konfiguracji',
                }
            };
        });

        $this->resetState(['value' => null]);
    }


    public function apply(Builder $query, array $data = []): Builder
    {
        if (empty($data['value'])) {
            return $query;
        }
        return match ($data['value']) {
            '-1' => $query->whereRaw('(exp_date <= DATE_ADD(NOW(), INTERVAL minimum_exp_date DAY)) AND minimum_exp_date <> 0 AND minimum_exp_date IS NOT NULL'),
            default => $query->where('exp_date', '<=', now(new \DateTimeZone('Europe/Warsaw'))->addDays((int)$data['value']))
        };
    }

    public function getFormField(): Select
    {
        $field = Select::make('value')
            ->label($this->getLabel())
            ->placeholder($this->getPlaceholder())
            ->native($this->isNative());


        $field->options($this->getOptions());

        if ($this->getOptionLabelUsing) {
            $field->getOptionLabelUsing($this->getOptionLabelUsing);
        }

        if ($this->getOptionLabelsUsing) {
            $field->getOptionLabelsUsing($this->getOptionLabelsUsing);
        }


        if ($this->getSearchResultsUsing) {
            $field->getSearchResultsUsing($this->getSearchResultsUsing);
        }

        if (filled($defaultState = $this->getDefaultState())) {
            $field->default($defaultState);
        }

        return $field;
    }


    public function getOptions(): array|\Closure|string|Arrayable|null
    {
        return match (empty($this->options)) {
            false => $this->options,
            default => [
                -1 => 'wg konfiguracji',
                7 => 'mija za 7 dni',
                14 => 'mija za 14 dni',
                21 => 'mija za 21 dni',
            ]
        };
    }


    public function native(bool $isNative = true): self
    {
        $this->isNative = $isNative;
        return $this;
    }

    public function isNative(): bool
    {
        return $this->isNative;
    }
}
