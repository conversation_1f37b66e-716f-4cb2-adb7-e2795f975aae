<?php

namespace App\Filament\Filters;

use App\Models\Warehouse;
use Closure;
use Filament\Forms\Components\Select;
use Filament\Tables\Filters\BaseFilter;
use Filament\Tables\Filters\Indicator;
use Filament\Tables\Filters\Concerns;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class PartnerSelectFilter extends BaseFilter
{
    use Concerns\HasOptions;
    use Concerns\HasPlaceholder;

//    public string | Closure | null $placeholder
    protected function setUp(): void
    {
        parent::setUp();

        $this->placeholder(
            fn(PartnerSelectFilter $filter): string => __('filament-tables::table.filters.select.placeholder'),
        );

        $this->indicateUsing(function (PartnerSelectFilter $filter, array $state): array {

            if (blank($state['value'] ?? null)) {
                return [];
            }

            $label = $filter->getOptions()[$state['value']] ?? null;
            if (blank($label)) {
                return [];
            }

            $indicator = $filter->getIndicator();

            if (!$indicator instanceof Indicator) {
                $indicator = Indicator::make("{$indicator}: {$label}");
            }

            return [$indicator];
        });

        $this->resetState(['value' => null]);
    }

    public function apply(Builder $query, array $data = []): Builder
    {
        $values = $data['value'] ?? null;

        if (blank(Arr::first(
            Arr::wrap($values),
            fn($value) => filled($value),
        ))) {
            return $query;
        }
        try {
            $id = self::deconstructCompoundIndex($values)['id']; // explode('-',$values)[1];
            return $query->where('warehouse_id', $id);
        } catch (\Throwable $e) {
            return $query;
        }
    }

    public function isNative()
    {
        return true;
    }

    public function getFormField(): Select
    {
        $field = Select::make('value')
            ->label($this->getLabel())
            ->placeholder($this->getPlaceholder())
            ->native($this->isNative());


        $field->options($this->getOptions());

        if ($this->getOptionLabelUsing) {
            $field->getOptionLabelUsing($this->getOptionLabelUsing);
        }

        if ($this->getOptionLabelsUsing) {
            $field->getOptionLabelsUsing($this->getOptionLabelsUsing);
        }


        if ($this->getSearchResultsUsing) {
            $field->getSearchResultsUsing($this->getSearchResultsUsing);
        }

        if (filled($defaultState = $this->getDefaultState())) {
            $field->default($defaultState);
        }

        return $field;
    }

    public static function buildCompoundIndex(Warehouse $row): string
    {
        return $row['hash'] . '.' . $row['id'];
    }

    public static function deconstructCompoundIndex($index): array
    {
        $raw = explode('.', $index);
        return [
            'hash' => $raw[0],
            'id' => $raw[1],
        ];
    }

    public static function getOptionsForLoggedInEmployee()
    {
        $options = [];
        $cwh = auth()->user()->warehouse->id;
        foreach (Warehouse::select('id', 'name', 'hash')->get() as $row) {
            $options[PartnerSelectFilter::buildCompoundIndex($row)] =
                (int)$cwh === (int)$row['id'] ? 'Mój magazyn' : $row['name'];
        }
        return $options;
    }
}
