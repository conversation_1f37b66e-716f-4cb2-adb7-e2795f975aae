<?php

namespace App\Filament\Filters;

use Filament\Forms\Components\Field;
use Filament\Forms\Components\Select;
use Filament\Tables\Filters\BaseFilter;
use Filament\Tables\Filters\Concerns\HasOptions;
use Filament\Tables\Filters\Concerns\HasPlaceholder;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Eloquent\Builder;

class LowStockWarningFilter extends BaseFilter
{
    use HasOptions, HasPlaceholder;

    protected bool $isNative = false;

    protected function setUp(): void
    {
        parent::setUp();


        $this->placeholder(
            fn(LowStockWarningFilter $filter) => __('filament-tables::table.filters.select.placeholder')
        );

        $this->indicateUsing(function ($state) {
            return match (empty($state['value'])) {
                true => null,
                default => match ($state['value']) {
                    default => 'Stan: ' . $state['value'] . ' lub mniej',
                    '-1' => 'Stan: wg konfiguracji lub mniej',
                }
            };
        });


        $this->resetState(['value' => null]);

    }

    public function apply(Builder $query, array $data = []): Builder
    {
        return $query;
    }

    public function applyToBaseQuery(Builder $query, array $data = []): Builder
    {
        if (empty($data['value'])) {
            return $query;
        }
        return match ($data['value']) {
            '-1' => $query->havingRaw('SUM(amount) <= minimum_stock'),
            default => $query->havingRaw('SUM(amount) <= ?', [(int)$data['value']])
        };
    }

    public function native(bool $isNative = true): self
    {
        $this->isNative = $isNative;
        return $this;
    }

    public function isNative(): bool
    {
        return $this->isNative;
    }

    public function getFormField(): Select
    {
        $field = Select::make('value')
            ->label($this->getLabel())
            ->placeholder($this->getPlaceholder())
            ->native($this->isNative());


        $field->options($this->getOptions());

        if ($this->getOptionLabelUsing) {
            $field->getOptionLabelUsing($this->getOptionLabelUsing);
        }

        if ($this->getOptionLabelsUsing) {
            $field->getOptionLabelsUsing($this->getOptionLabelsUsing);
        }


        if ($this->getSearchResultsUsing) {
            $field->getSearchResultsUsing($this->getSearchResultsUsing);
        }

        if (filled($defaultState = $this->getDefaultState())) {
            $field->default($defaultState);
        }

        return $field;
    }

    /**
     * @return array|Closure|Arrayable|string|null
     */
    public function getOptions(): array|\Closure|string|Arrayable|null
    {
        return match (empty($this->options)) {
            false => $this->options,
            default => [
                -1 => 'wg konfiguracji',
                5 => 'mniej niż 5 szt',
                10 => 'mniej niż 10 szt',
                15 => 'mniej niż 15 szt',
            ]
        };
    }

}
