<?php

namespace App\Filament\Filters;

use App\Models\Warehouse;
use Closure;
use Filament\Forms\Components\Select;
use Filament\Tables\Filters\BaseFilter;
use Filament\Tables\Filters\Indicator;
use Filament\Tables\Filters\Concerns;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class WHSelectFilter extends BaseFilter
{
    use Concerns\HasOptions;
    use Concerns\HasPlaceholder;

    public bool $activeOnly = false;

    protected function setUp(): void
    {
        parent::setUp();

        $this->placeholder(
            fn (WHSelectFilter $filter): string =>
                __('filament-tables::table.filters.select.placeholder'),
        );

        $this->indicateUsing(function (WHSelectFilter $filter, array $state): array {

            if (blank($state['value'] ?? null)) {
                return [];
            }


            $label = $filter->getOptions()[$state['value']] ?? null;
            if (blank($label)) {
                return [];
            }

            $indicator = $filter->getIndicator();

            if (! $indicator instanceof Indicator) {
                $indicator = Indicator::make("{$indicator}: {$label}");
            }

            return [$indicator];
        });

        $this->resetState(['value' => null]);
    }

    public function apply(Builder $query, array $data = []): Builder
    {
        $values =  $data['value'] ?? null;

        if (blank(Arr::first(
            Arr::wrap($values),
            fn ($value) => filled($value),
        ))) {
            return $query;
        }
        try {
            $id = self::deconstructCompoundIndex($values)['id'];
            return $query->where('warehouse_id', $id);
        } catch (\Throwable $e) {
            return $query;
        }
    }

    public function isNative()
    {
        return true;
    }

    public function activeOnly($state = false): self
    {
        $this->activeOnly = $state;
        return $this;
    }

    public function getFormField(): Select
    {
        $field = Select::make('value')
            ->label($this->getLabel())
            ->placeholder($this->getPlaceholder())
            ->native($this->isNative());


        $field->options($this->getOptions());

        if ($this->getOptionLabelUsing) {
            $field->getOptionLabelUsing($this->getOptionLabelUsing);
        }

        if ($this->getOptionLabelsUsing) {
            $field->getOptionLabelsUsing($this->getOptionLabelsUsing);
        }


        if ($this->getSearchResultsUsing) {
            $field->getSearchResultsUsing($this->getSearchResultsUsing);
        }

        if (filled($defaultState = $this->getDefaultState())) {
            $field->default($defaultState);
        }

        return $field;
    }

    public static function buildCompoundIndex(Model $row): string
    {
        return $row['hash'] . '.' . $row['id'];
    }

    public static function deconstructCompoundIndex($index): array
    {
        $raw = explode('.', $index);
        return [
            'hash' => $raw[0] ?? '',
            'id' => $raw[1] ?? '',
        ];
    }

    public static function getOptionsForLoggedInEmployee($activeOnly = false, $exceptWarehouseId = null): array
    {
        $options = [];
        $qry = Warehouse::query();
        if ($activeOnly) {
            $qry->active();
        }
        $qry->select('id', 'name', 'hash', 'is_active');
        if (filled($exceptWarehouseId)) {
            $qry->where('id', '!=', self::deconstructCompoundIndex($exceptWarehouseId)['id']);
        }
        switch ((bool) auth()->user()?->isTenantAdmin()) {
            case true:
                foreach ($qry->get() as $row) {
                    $name = match ($activeOnly) {
                        true => $row['name'],
                        default => $row['is_active'] ? $row['name'] : $row['name'] . ' - inactive',
                    };
                    $options[self::buildCompoundIndex($row)] = $name;
                }
                break;
            default:
            case false:
                $cwh = auth()->user()?->warehouse?->id;
                foreach ($qry->get() as $row) {
                    $name = (int) $cwh === (int) $row['id'] ?
                        'Mój magazyn' :
                        $row['name'];

                    $name .= match ($activeOnly) {
                        true => '',
                        default => $row['is_active'] ? '' : ' - inactive',
                    };
                    $options[self::buildCompoundIndex($row)] = $name;
                }
                break;
        }
        return $options;
    }
}
