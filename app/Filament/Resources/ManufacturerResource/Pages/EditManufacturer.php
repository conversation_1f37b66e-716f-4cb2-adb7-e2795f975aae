<?php

namespace App\Filament\Resources\ManufacturerResource\Pages;

use App\Filament\Resources\ManufacturerResource;
use App\Models\Tenant;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Resources\Pages\EditRecord;
use Webbingbrasil\FilamentCopyActions\Forms\Actions\CopyAction;

class EditManufacturer extends EditRecord
{
    protected static string $resource = ManufacturerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function getFormActions(): array
    {
        $old = parent::getFormActions();
        $old[1]->url(self::getResource()::getUrl('index'));
        return $old;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Dane podstawowe')
                    ->columns(2)
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Nazwa')
                            ->required()
                            ->maxLength(120),
                        Forms\Components\TextInput::make('hash')
                            ->suffixAction(CopyAction::make())
                            ->readOnly(),
                        Forms\Components\TextInput::make('phone')
                            ->label('Telefon')
                            ->maxLength(100),
                        Forms\Components\TextInput::make('email')
                            ->label('Email')
                            ->email()
                            ->maxLength(60),
                        Forms\Components\TextInput::make('contact_name')
                            ->label('Osoba konaktowa'),
                        Forms\Components\TextInput::make('website')
                            ->url()
                            ->label('Strona www'),
                        Forms\Components\Textarea::make('address')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ]),
                Forms\Components\Toggle::make('is_active')
                    ->required(),
                Forms\Components\Select::make('installation')
                    ->options(Tenant::all()->pluck('name', 'id'))
                    ->required()
            ]);
    }
}
