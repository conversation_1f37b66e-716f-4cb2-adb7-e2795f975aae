<?php

namespace App\Filament\Resources\ManufacturerResource\Pages;

use App\Filament\Resources\ManufacturerResource;
use App\Models\Tenant;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateManufacturer extends CreateRecord
{
    protected static string $resource = ManufacturerResource::class;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Dane podstawowe')
                    ->columns(2)
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Nazwa')
                            ->required()
                            ->columnSpanFull()
                            ->maxLength(120),
                        Forms\Components\TextInput::make('phone')
                            ->label('Telefon')
                            ->maxLength(100),
                        Forms\Components\TextInput::make('email')
                            ->label('Email')
                            ->email()
                            ->maxLength(60),
                        Forms\Components\TextInput::make('contact_name')
                            ->label('Osoba konaktowa'),
                        Forms\Components\TextInput::make('website')
                            ->url()
                            ->label('Strona www'),
                        Forms\Components\Textarea::make('address')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ]),
                Forms\Components\Toggle::make('is_active')
                    ->required(),
                Forms\Components\Select::make('installation')
                    ->options(Tenant::all()->pluck('name', 'id'))
                    ->required()
            ]);
    }

    protected function handleRecordCreation(array $data): Model
    {
        if (empty($data['hash'])) {
            $data['hash'] = bin2hex(random_bytes(16));
        }
        return parent::handleRecordCreation($data);
    }
}
