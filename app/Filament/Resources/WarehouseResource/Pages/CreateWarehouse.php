<?php

namespace App\Filament\Resources\WarehouseResource\Pages;

use App\Filament\Resources\WarehouseResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateWarehouse extends CreateRecord
{
    protected static string $resource = WarehouseResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        if (empty($data['hash'])) {
            $data['hash'] = bin2hex(random_bytes(16));
        }
        return parent::handleRecordCreation($data);
    }
}
