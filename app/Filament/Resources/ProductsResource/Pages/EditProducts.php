<?php

namespace App\Filament\Resources\ProductsResource\Pages;

use App\Filament\Resources\ProductsResource;
use App\Models\Manufacturer;
use App\Models\Tenant;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Resources\Pages\EditRecord;
use Webbingbrasil\FilamentCopyActions\Forms\Actions\CopyAction;

class EditProducts extends EditRecord
{
    protected static string $resource = ProductsResource::class;


    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('toList')->label('Lista')->button()->url(self::getResource()::getUrl('index')),
            Actions\DeleteAction::make(),
        ];
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(
                [
                    Forms\Components\TextInput::make('hash')
                        ->suffixAction(CopyAction::make())
                        ->readOnly(),
                    Forms\Components\Section::make()
                        ->columns(3)
                        ->schema([
                            Forms\Components\TextInput::make('name')
                                ->label('Nazwa produktu')
                                ->required()
                                ->maxLength(255),
                            Forms\Components\TextInput::make('gtin')
                                ->label('Numer GTIN (EAN)')
                                ->hint('Cyfry kodu paskowego')
                                ->maxLength(20),
                            Forms\Components\TextInput::make('price_per_unit')
                                ->label('Cena jednostkowa')
                                ->numeric()
                                ->minValue(0)
                                ->step(0.01)
                                ->inputMode('decimal'),
                            Forms\Components\TextInput::make('basic_unit')
                                ->label('Jednostka')->default('szt')->readOnly(),
                            Forms\Components\TextInput::make('volume_ml')
                                ->numeric()
                                ->label('Objętość produktu')->hint('Wielkość w mililitrach 1/1000 litra')
                                ->default('szt'),
                            Forms\Components\TextInput::make('weight_gr')
                                ->numeric()
                                ->hint('Wielkość w gramach')
                                ->label('Waga produktu')->default('szt'),
                            Forms\Components\TextInput::make('ext_link')
                                ->label('Link do strony produktu')
                                ->url()
                                ->columnSpanFull()
                                ->maxLength(255),
                            Forms\Components\Textarea::make('description')
                                ->label('Opis')
                                ->maxLength(65535)
                                ->columnSpanFull(),
                        ]),
                    Forms\Components\Section::make('Na magazynie')
                        ->columns(2)
                        ->schema([
                                Forms\Components\Toggle::make('limited_stock')
                                    ->hint('Ograniczona liczba sztuk - produkty')
                                    ->label('Ograniczona liczba')
                                    ->default(true),
                                Forms\Components\Toggle::make('below_stock')
                                    ->hint('Czy można wydać produkt poniżej stanu 0')
                                    ->label('Stan ujemny'),
                            ]
                        ),
                    Forms\Components\Section::make('Producent')
                        ->columns(2)
                        ->schema([
                            Forms\Components\Select::make('manufacturer_id')
                                ->options(Manufacturer::all()->pluck('name', 'id'))
                                ->label('Producent')
                                ->required(),
                            Forms\Components\Select::make('installation')
                                ->options(Tenant::all()->pluck('name', 'id'))
                                ->required()
                                ->label('Tenant'),
                        ]),
                    Forms\Components\Section::make('Dates')
                        ->columns(2)
                        ->schema([
                            Forms\Components\TextInput::make('created_at')
                                ->label('Created at')->readOnly(),
                            Forms\Components\TextInput::make('updated_at')
                                ->label('Updated at')->readOnly(),
                        ]),
                    Forms\Components\Section::make('Status')
                        ->schema([
                            Forms\Components\Toggle::make('is_active')
                                ->label('Aktywny')
                                ->required(),
                        ]),
                ]
            );
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['created_at'] = (new \DateTime($data['created_at']))->format('Y-m-d H:i:s');

        $data['updated_at'] === null ?: $data['updated_at'] = (new \DateTime($data['updated_at']))->format('Y-m-d H:i:s');
        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if (empty($data['hash'])) {
            $data['hash'] = bin2hex(random_bytes(16));
        }
        if (null === $data['description']) {
            $data['description'] = '';
        }
        return $data;
    }


    protected function afterSave()
    {
        $this->record->refresh();
        $this->fillForm();
    }
}
