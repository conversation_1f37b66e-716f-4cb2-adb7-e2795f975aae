<?php

namespace App\Filament\Resources;

use App\Enums\Langs;
use App\Filament\Resources\UserResource\Pages;
use App\Models\Tenant;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Base data')
                    ->columns(2)
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Nazwa')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('password')
                            ->password()
                            ->confirmed()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('password_confirmation')
                            ->password()
                            ->requiredWith('password')
                            ->maxLength(255),
                        Forms\Components\Select::make('tenant')
                            ->relationship('tenant', 'name'),
                        Forms\Components\Select::make('roles')->required()->relationship('roles', 'name'),
                        Forms\Components\Toggle::make('active')
                            ->label('Użytkownik aktywny?')
                            ->onIcon('heroicon-m-user')
                            ->offIcon('heroicon-m-power'),
                    ]),

                Section::make('Additional data')
                    ->description('Enter additional data for your profile')
                    ->columns(2)
                    ->schema([
                        TextInput::make('name')
                            ->label('Imie')
                            ->maxLength(255),
                        TextInput::make('surname')
                            ->label('Nazwisko')
                            ->maxLength(255),
                        TextInput::make('adress')
                            ->label('Adres')
                            ->maxLength(255),
                        TextInput::make('number')
                            ->label('Telefon')
                            ->maxLength(255)
                            ->numeric(),
                        Select::make('lang')
                            ->label('Select lang')
                            ->options(Langs::toArray())
                            ->default('pl')
                    ])->relationship('profile'),
            ]);
    }

    public static function table(Table $table): Table
    {
        $h = $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->label('Nazwa')
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('roles.name')->label('Rola')
                    ->searchable(),
                Tables\Columns\TextColumn::make('tenant.name')->label('Tenant')
                    ->default('--'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable(),
                Tables\Columns\TextColumn::make('email_verified_at')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('Tenant')
                    ->relationship('tenant', 'name')
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
            ]);
        return $h;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
