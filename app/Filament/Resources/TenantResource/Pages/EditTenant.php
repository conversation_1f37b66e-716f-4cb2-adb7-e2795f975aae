<?php

namespace App\Filament\Resources\TenantResource\Pages;

use App\Enums\AccountingTypesPL;
use App\Enums\DocumentGeneralTypes;
use App\Enums\DocumentTypes;
use App\Enums\PartnerBusinessTypes;
use App\Enums\PartnerVATTypes;
use App\Enums\SystemModules;
use App\Enums\TaxResidencyCountries;
use App\Enums\TaxTypePL;
use App\Filament\Resources\TenantResource;
use App\Models\Tenant;
use App\Repositories\DocumentSeriesRepository;
use App\Repositories\TenantRepository;
use Filament\Actions;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use Webbingbrasil\FilamentCopyActions\Forms\Actions\CopyAction;

class EditTenant extends EditRecord
{
    protected static string $resource = TenantResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->using(function (Tenant $record) {
                    return (new TenantRepository())->removeTenant($record);
                })
                ->form([
                    Forms\Components\Checkbox::make('confirm')
                        ->required()
                        ->label('Tak, chcę usunąć partnera!')
                ])
                ->modalDescription(
                    new HtmlString('Czy na pewno chcesz usunąć partnera?<br>Ta czynność jest nieodwracalna!!!')
                )
                ->modalHeading('USUŃ PARTNERA BIZNESOWEGO')
        ];
    }

    public function form(Form $form): Form
    {
        $mf = $form
            ->schema([
                Forms\Components\Tabs::make('Dane podstawowe')
                    ->columnSpanFull()
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('Dane podstawowe')
                            ->columns(3)
                            ->schema([
                                Forms\Components\TextArea::make('name')
                                    ->label('Nazwa')
                                    ->required()
                                    ->columnSpan(2)
                                    ->maxLength(120),
                                Forms\Components\TextInput::make('hash')
                                    ->label('Hash')
                                    ->suffixAction(CopyAction::make())
                                    ->readOnly(),
                                Forms\Components\TextInput::make('postcode')
                                    ->label('Kod pocztowy')
                                    ->maxLength(100),
                                Forms\Components\TextInput::make('city')
                                    ->label('Miasto')
                                    ->maxLength(100),
                                Forms\Components\TextInput::make('phone')
                                    ->label('Telefon')
                                    ->maxLength(100),
                                Forms\Components\TextInput::make('email')
                                    ->label('Email')
                                    ->email()
                                    ->maxLength(60),
                                Forms\Components\TextInput::make('contact_name')
                                    ->label('Osoba konaktowa'),
                                Forms\Components\TextInput::make('website')
                                    ->url()
                                    ->label('Strona www'),
                                Forms\Components\Textarea::make('address')
                                    ->maxLength(65535)
                                    ->columnSpanFull(),
                            ]),
                        Forms\Components\Tabs\Tab::make('Dane księgowe')
                            ->columns(2)
                            ->schema([
                                Select::make('tax_residency_country')
                                    ->label(__('app.partners.create.tax_residency_country'))
                                    ->options(
                                        TaxResidencyCountries::toArrayWithLabels()
                                    )
                                    ->formatStateUsing(fn($state) => $state ?? TaxResidencyCountries::PL->name),
                                Select::make('business_type')
                                    ->label(__('app.partners.create.business_type'))
                                    ->options(PartnerBusinessTypes::toArrayWithLabels())
                                    ->default(PartnerBusinessTypes::INDIVIDUAL->value),
                                Select::make('vat_type')
                                    ->label(__('app.partners.create.vat_type'))
                                    ->options(PartnerVATTypes::toArrayWithLabels())
                                    ->default(PartnerVATTypes::LOCAL->value),
                                Select::make('tax_type')
                                    ->label(__('app.partners.create.tax_type'))
                                    ->options(TaxTypePL::toArrayWithLabels())
                                    ->default(TaxTypePL::LINEAR->value),
                                Select::make('accounting_type')
                                    ->label(__('app.partners.create.accounting_type'))
                                    ->options(AccountingTypesPL::toArrayWithLabels())
                                    ->default(AccountingTypesPL::FULL->value),
                                Forms\Components\TextInput::make('vat_id')
                                    ->label('NIP')
                                    ->columnSpan(1),
                            ]),
                        Forms\Components\Tabs\Tab::make('Dane systemowe')
                            ->badge(function (Model $record) {
                                return match ((bool)$record->is_active) {
                                    true => 'OK',
                                    default => 'Inactive!',
                                };
                            })
                            ->badgeColor(function (Model $record) {
                                return match ((bool)$record->is_active) {
                                    true => 'success',
                                    default => 'warning',
                                };
                            })
                            ->columns(2)
                            ->schema([
                                Forms\Components\Toggle::make('is_active')
                                    ->label('Aktywny')
                                    ->required(),
                                Forms\Components\TextInput::make('system_domain')
                                    ->hint('Domena w systemie bez protokołu')
                                    ->unique(ignoreRecord: true)
                                    ->label('Domena w systemie'),
                            ]),
                    ]),
                Forms\Components\Section::make('Modules')
                    ->schema([
                        Forms\Components\CheckboxList::make('config.modules')
                            ->label('Select modules')
                            ->options(SystemModules::enabledToArrayWithLabels())
                            ->name('modules'),
                    ]),

            ]);

        return $mf;
    }

    protected function handleRecordUpdate(Model|Tenant $record, array $data): Model
    {
        if (empty($data['config']['modules'])) {
            $data['is_active'] = false;
        }

        $setUpWHDefaultSeries = in_array(SystemModules::WAREHOUSE->value, $data['config']['modules']);

        $setUpTDDefaultSeries = in_array(SystemModules::INVOICES->value, $data['config']['modules']);

        $record = parent::handleRecordUpdate($record, $data);

        $whDefaultSeries = DocumentTypes::getDocTypesOf(DocumentGeneralTypes::WAREHOUSE);
        foreach ($whDefaultSeries as $dsp) {
            DocumentSeriesRepository::blockTenantDocSeries($record, $dsp);
        }

        if ($setUpWHDefaultSeries) {
            $defaultSeries = DocumentTypes::getDocTypesOf(DocumentGeneralTypes::WAREHOUSE);
            $currentSeries = DocumentSeriesRepository::getSeriesForTenant(
                $record,
                array_column($defaultSeries, 'value')
            );
            if (blank($currentSeries)) {
                DocumentSeriesRepository::seedDefaultSeries($record, 'warehouse');
            } else {
                $cids = $currentSeries->pluck('doc_type')->pluck('value')->toArray();
                foreach ($defaultSeries as $dsp) {
                    if (!in_array($dsp->value, $cids)) {
                        DocumentSeriesRepository::createDefaultWHDocSeries($record, $dsp);
                    } else {
                        DocumentSeriesRepository::unblockTenantDocSeries($record, $dsp);
                    }
                }
            }
        }

        $sellsDefaultSeries = DocumentTypes::getSellsTypeDocs();
        foreach ($sellsDefaultSeries as $dsp) {
            DocumentSeriesRepository::blockTenantDocSeries($record, $dsp);
        }

        if ($setUpTDDefaultSeries) {
            $defaultSeries = DocumentSeriesRepository::getDefaultTDocTypesForTenant($record);
            $currentSeries = DocumentSeriesRepository::getSeriesForTenant(
                $record,
                array_column($defaultSeries, 'value')
            );

            if (blank($currentSeries)) {
                DocumentSeriesRepository::seedDefaultSeries($record, 'trade');
            } else {
                $cids = $currentSeries->pluck('doc_type')->pluck('value')->toArray();
                foreach ($defaultSeries as $dsp) {
                    if (!in_array($dsp->value, $cids)) {
                        DocumentSeriesRepository::createDefaultTradeDocSeries($record, $dsp);
                    } else {
                        DocumentSeriesRepository::unblockTenantDocSeries($record, $dsp);
                    }
                }
            }
        }

        $this->fillForm();
        return $record;
    }
}
