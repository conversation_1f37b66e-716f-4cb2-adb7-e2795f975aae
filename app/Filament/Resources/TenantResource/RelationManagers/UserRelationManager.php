<?php

namespace App\Filament\Resources\TenantResource\RelationManagers;

use App\Enums\Roles;
use App\Models\ProfileData;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;

class UserRelationManager extends RelationManager
{
    protected static string $relationship = 'user';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('profile.name')
                    ->label('Imię')
                    ->formatStateUsing(function (?Model $record, $state) {
                        return $record?->profile->name ?? $state;
                    })
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('profile.surname')
                    ->label('<PERSON><PERSON><PERSON><PERSON>')
                    ->formatStateUsing(function (?Model $record, $state) {
                        return $record?->profile->surname ?? $state;
                    })
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('email')
                    ->email()
                    ->unique(ignoreRecord: true)
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('email')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->formatStateUsing(function (?Model $record, $state) {
                        return $record?->fullName() ?? $state;
                    })
                    ->label('Nazwa'),
                Tables\Columns\TextColumn::make('email')->label('Email'),
                Tables\Columns\TextColumn::make('roles.name')->label('Rola'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                ->using(function (array $data, string $model): Model {
                    $data['password'] = Hash::make(random_bytes(8));
                    try {
                        /**
                         * @var User $user
                         */

                        $profileData = $data['profile'];
                        unset($data['profile']);
                        $data['name'] = $data['email'];
                        $user = User::create($data);
                        $profile = new ProfileData($profileData);
                        $user->profile()->save($profile);
                        $user->assignRole([Roles::TENANT_ADMIN->value]);
                        $this->getOwnerRecord()->user()->attach($user->id);
                    } catch (\Throwable $e) {
                        abort(400, $e->getMessage());
                    }
                    return $user;
                }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                ->using(function (array $data, Model | User $record): Model {
                    $profile = $data['profile'];
                    unset($data['profile']);
                    $record->update($data);
                    $record->profile->update($profile);
                    return $record;
                }),
                Tables\Actions\DeleteAction::make(),
            ])->modifyQueryUsing(function (Builder $query) {
                return $query->whereHas('roles', function ($q) {
                    $q->where('role_id', Roles::TENANT_ADMIN->value);
                });
            });
    }
}
