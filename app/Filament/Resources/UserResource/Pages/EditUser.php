<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Enums\Langs;
use App\Filament\Resources\UserResource;
use App\Models\User;
use App\Repositories\UsersRepository;
use Filament\Actions;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditUser extends EditRecord
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
            ->using(fn(User $record) => $this->handleRecordDelete($record)),
        ];
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Base Data')
                    ->columns(2)
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Nazwa')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Select::make('tenant')
                            ->relationship('tenant', 'name'),
                        Forms\Components\Select::make('roles')->required()->relationship('roles', 'name'),
                        Forms\Components\Toggle::make('active')
                            ->label('Użytkownik aktywny?')
                            ->onIcon('heroicon-m-user')
                            ->offIcon('heroicon-m-power'),
                    ]),

                Forms\Components\Section::make([
                    Forms\Components\Checkbox::make('chnpwd')
                        ->label('Zmiana hasła')
                        ->hiddenOn('create')
                        ->columnSpanFull()
                        ->live(),
                    Forms\Components\TextInput::make('password')
                        ->label('Hasło')
                        ->password()
                        ->confirmed()
                        ->dehydrated(fn($state) => filled($state))
                        ->required(fn(Forms\Get $get): bool => $get('chnpwd'))
                        ->hidden(function (Forms\Get $get) {
                            return !$get('chnpwd');
                        })
                        ->maxLength(255),
                    Forms\Components\TextInput::make('password_confirmation')
                        ->label('Potwierdź hasło')
                        ->password()
                        ->dehydrated(fn($state) => filled($state))
                        ->required(fn(Forms\Get $get): bool => $get('chnpwd'))
                        ->hidden(function (Forms\Get $get) {
                            return !$get('chnpwd');
                        })
                ])->columns(2),
                Section::make('Additional data')
                    ->description('Enter additional data for your profile')
                    ->columns(2)
                    ->schema([
                        TextInput::make('name')
                            ->label('Imie')
                            ->maxLength(255),
                        TextInput::make('surname')
                            ->label('Nazwisko')
                            ->maxLength(255),
                        TextInput::make('adress')
                            ->label('Adres')
                            ->maxLength(255),
                        TextInput::make('number')
                            ->label('Telefon')
                            ->maxLength(255)
                            ->numeric(),
                        Select::make('lang')
                            ->label('Select lang')
                            ->options(Langs::toArray())
                            ->default('pl')
                    ])->relationship('profile'),

            ]);
    }

    public function handleRecordDelete(User $record): bool
    {
        return UsersRepository::deleteUserWithRelations($record);
    }
}
