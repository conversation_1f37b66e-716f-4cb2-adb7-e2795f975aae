<?php

namespace App\Filament\Resources;

use App\Enums\WarehouseTypes;
use App\Filament\Resources\WarehouseResource\Pages;
use App\Models\Tenant;
use App\Models\Warehouse;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WarehouseResource extends Resource
{
    protected static ?string $model = Warehouse::class;

    protected static ?string $navigationIcon = 'heroicon-o-square-3-stack-3d';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('hash')
                    ->readOnly(),
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(100),
                Forms\Components\Textarea::make('address')
                    ->maxLength(65535)
                    ->columnSpanFull(),
                Forms\Components\Select::make('owner_type')
                    ->label('Typ')
                    ->required()
                    ->options(WarehouseTypes::toArray())
                    ->default(1),
                Forms\Components\Select::make('owner_identifier')
                    ->label('Owner')
                    ->options(Tenant::all()->pluck('name', 'id'))
                    ->required(),
                Forms\Components\Select::make('installation')
                    ->label('Tenant')
                    ->options(Tenant::all()->pluck('name', 'id'))
                    ->required()
                    ->columnSpan(2),
                Forms\Components\Toggle::make('is_active')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('hash')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('ownertype')
                    ->label('Owner type')
                    ->state(function (Warehouse $record) {
                        return $record->owner_type->name;
                    }),
                Tables\Columns\TextColumn::make('owner_identifier')
                    ->state(function (Warehouse $record) {
                        return $record->owner->email ?? $record->owner->name;
                    })
                    ->label('Owner')
                    ->sortable(),
                Tables\Columns\TextColumn::make('tenant.name')
                    ->label('Tenant')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active'),
                Tables\Filters\SelectFilter::make('tenant')
                    ->relationship('tenant', 'name'),
                Tables\Filters\SelectFilter::make('owner_type')
                    ->options(WarehouseTypes::toArray())
                    ->default(WarehouseTypes::COMPANY->value)
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWarehouses::route('/'),
            'create' => Pages\CreateWarehouse::route('/create'),
            'edit' => Pages\EditWarehouse::route('/{record}/edit'),
        ];
    }
}
