<?php

namespace App\Services;

use App\Models\Tenant;
use App\Models\TradeDoc;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use XSoft\JPK\Objects\JPKV7M;
use XSoft\JPK\Objects\V7M\Ewidencja;
use XSoft\JPK\Objects\V7M\Naglowek;
use XSoft\JPK\Objects\V7M\PodmiotOsobaFizyczna;
use XSoft\JPK\Objects\V7M\SprzedazWiersz;

class ExportJPK
{

    public string $exportName = '';

    public function exportJPKV7M(Collection $data, Tenant $tenant, Carbon $date, User $user): string
    {
        $jpk = new JPKV7M();
        $jpk->is_ewidencja = true;
        $jpk->is_deklaracja = false;

        $naglowek = new Naglowek();
        $naglowek->setRok($date->format('Y'));
        $naglowek->setMiesiac($date->format('m'));
        $naglowek->setNazwaSystemu(config('app.name'));
        $naglowek->setKodUrzedu(0);
        $jpk->setNaglowek($naglowek);

        $podatnik = new PodmiotOsobaFizyczna();
        $podatnik->setEmail($tenant->email ?? '<EMAIL>');
        $podatnik->setNIP($tenant->vat_id);
        $podatnik->setNazwisko($user->surname);
        $podatnik->setImiePierwsze($user->name);
        $podatnik->setDataUrodzenia(\DateTimeImmutable::createFromFormat('Y-m-d', $user->birthdate));
        $jpk->setPodatnik($podatnik);

        $ewidencja = new Ewidencja();

        /**
         * @var TradeDoc $tradeDoc
         */
        foreach ($data as $tradeDoc) {
            $wiersz = new SprzedazWiersz();
            $meta = $tradeDoc->getMeta();
            $wiersz->setDataSprzedazy($tradeDoc->sells_date->format('Y-m-d'));
            $wiersz->setDataWystawienia($tradeDoc->issued_at->format('Y-m-d'));
            $wiersz->setDowodSprzedazy($tradeDoc->full_doc_number);
            $wiersz->setNumerKontrahenta($meta->buyer_address['vat_id'] ?? '');
            $wiersz->setNazwaKontrahenta($meta->buyer_address['name'] ?? '');
            $rate = 1;
            if ($tradeDoc->currency !== 'PLN') {
                $rate = $tradeDoc->exchange_rate ?? $rate;
            }

            $vat = $meta->vat;
            if ($vat['0'] ?? false) {
                $wiersz->setWPOVat0K13(
                    $vat['0']['net_amount'] / $rate
                );
            }

            if ($vat['zw.'] ?? false) {
                $wiersz->setVatZwolnionyK10(
                    $vat['zw.']['net_amount'] / $rate
                );
            }

            if ($vat['5'] ?? false) {
                $wiersz->setVat5procK15(
                    $vat['5']['net_amount'],
                    $vat['5']['vat_amount'] / $rate
                );
            }

            if ($vat['7'] ?? false) {
                $wiersz->setVat7procK17(
                    $vat['7']['net_amount'],
                    $vat['7']['vat_amount'] / $rate
                );
            }

            if ($vat['8'] ?? false) {
                $wiersz->setVat7procK17(
                    $vat['8']['net_amount'],
                    $vat['8']['vat_amount'] / $rate
                );
            }

            if ($vat['22'] ?? false) {
                $wiersz->setVat23procK19(
                    $vat['22']['net_amount'],
                    $vat['22']['vat_amount'] / $rate
                );
            }

            if ($vat['23'] ?? false) {
                $wiersz->setVat23procK19(
                    $vat['23']['net_amount'],
                    $vat['23']['vat_amount'] / $rate
                );
            }

            $ewidencja->addSprzedaz($wiersz);
        }

        $jpk->setEwidencja($ewidencja);
        $tmp_name = tempnam(sys_get_temp_dir(), 'jpk');
        $this->exportName = 'jpk_vat7m_' . $tenant->vat_id . '_' . $date->format('Ym') . '.xml';
        return $jpk->getXml();
    }
}
