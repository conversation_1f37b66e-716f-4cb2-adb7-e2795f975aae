<?php

namespace App\Services;

use App\Enums\TradeDocVatMethod;
use App\Enums\VatRates;

class CalculateTradeDoc
{

    protected $input = [
        'net_unit_price' => 0,
        'vat_rate' => 0,
        'vat_label' => '0',
        'vat_value' => 0,
        'amount' => 0,
        'gross_unit_price' => 0,
        'discount_type' => 0,
        'discount_value' => 0,
        'discounted_unit_price' => 0,
        'net_value' => 0,
        'gross_value' => 0,
        'base_unit' => 'cents',
        'vat_calculate_based_on' => TradeDocVatMethod::BASE_ON_NET,
    ];

    protected static array $inCents = [
        'net_unit_price',
        'vat_value',
        'gross_unit_price',
        'discount_value',
        'discounted_unit_price',
        'net_value',
        'gross_value',
    ];

    protected $output = [
        'net_unit_price' => 0,
        'vat_rate' => 0,
        'vat_value' => 0,
        'amount' => 0,
        'gross_unit_price' => 0,
        'discount_type' => 0,
        'discount_value' => 0,
        'discounted_unit_price' => 0,
        'net_value' => 0,
        'gross_value' => 0,
    ];

    public function __construct(array $data = [])
    {
        $this->input = array_merge($this->input, $data);
        $this->input['vat_rate'] = VatRates::getRate($this->input['vat_label'] ?? 0);
        $this->input['discount_value'] ??= 0;
    }

    public static function make(array $data = []): CalculateTradeDoc
    {
        return new CalculateTradeDoc($data);
    }

    public function basedOnNetVAT()
    {
        $this->output = $this->input;
        $this->output['discounted_unit_price'] = match ((int)$this->get('discount_type')) {
            1 => $this->valueDiscount($this->get('net_unit_price'), $this->get('discount_value')),
            2 => match ($this->get('discount_value') > 0) {
                true => $this->percentDiscount($this->get('net_unit_price'), $this->get('discount_value')),
                default => $this->get('net_unit_price'),
            },
            default => $this->get('net_unit_price')
        };

        $this->output['vat_value'] = $this->vatValue(
            $this->output['discounted_unit_price'],
            $this->output['vat_rate'],
            $this->output['amount']
        );

        $gup = $this->output['discounted_unit_price'] +
            $this->vatValue(
                $this->output['discounted_unit_price'],
                $this->output['vat_rate'],
                1
            );

        $this->output['gross_unit_price'] = round($gup, 2);
        $this->output['net_value'] = round($this->output['discounted_unit_price'] * $this->output['amount'], 2);
        $this->output['gross_value'] = round($this->output['net_value'] + $this->output['vat_value'], 2);
//        $this->output['gross_value'] = round($this->output['gross_unit_price'] * $this->output['amount'], 2);
    }


    public function basedOnGrossVAT()
    {
        $this->output = $this->input;
        $this->output['discounted_unit_price'] = match ((int)$this->get('discount_type')) {
            1 => $this->valueDiscount($this->get('gross_unit_price'), $this->get('discount_value')),
            2 => $this->percentDiscount($this->get('gross_unit_price'), $this->get('discount_value')),
            default => $this->get('gross_unit_price'),
        };

        $this->output['vat_value'] = $this->vatValue(
            $this->output['discounted_unit_price'],
            $this->output['vat_rate'],
            $this->output['amount']
        );

        $nup = $this->output['discounted_unit_price'] -
            $this->vatValue(
                $this->output['discounted_unit_price'],
                $this->output['vat_rate'],
                1
            );

        $this->output['net_unit_price'] = $nup;
        $this->output['gross_value'] = round($this->output['discounted_unit_price'] * $this->output['amount'], 2);
        $this->output['net_value'] = round($this->output['gross_value'] - $this->output['vat_value'], 2);
    }


    protected function percentDiscount($baseValue, $discountValue): float
    {
        return round($baseValue - ($baseValue * $discountValue / 100), 2);
    }

    protected function valueDiscount($baseValue, $discountValue): float
    {
        return number_format($baseValue - $discountValue, 2, '.', '');
    }

    protected function vatValue($discountedUnitPrice, $vatRate, $amount): float
    {
        $total = $discountedUnitPrice * $amount;
        return match ($this->get('vat_calculate_based_on')) {
            TradeDocVatMethod::BASE_ON_NET => round(($total * $vatRate) / 100, 2),
            TradeDocVatMethod::BASE_ON_GROSS => round(($total * $vatRate) / (100 + $vatRate), 2),
        };
    }

    public function setDiscount($type, $value): CalculateTradeDoc
    {
        $this->input['discount_type'] = $type;
        $this->input['discount_value'] = $value;
        return $this;
    }

    public function setVatMethod(TradeDocVatMethod $method): CalculateTradeDoc
    {
        $this->input['vat_calculate_based_on'] = $method;
        return $this;
    }


    public function calculate(): void
    {
        $calculationMethod = $this->get('vat_calculate_based_on');

        switch ($calculationMethod) {
            case TradeDocVatMethod::BASE_ON_GROSS:
                $this->basedOnGrossVAT();
                break;

            case TradeDocVatMethod::BASE_ON_NET:
                $this->basedOnNetVAT();
                break;

            default:
                throw new \InvalidArgumentException(
                    sprintf("Invalid VAT calculation method: %s", $calculationMethod)
                );
        }
    }


    public function getResults(): array
    {
        return $this->output;
    }

    public function getResultValue($key): ?string
    {
        return $this->output[$key] ?? null;
    }

    public function getResultInCents($key): ?string
    {
        return match (in_array($key, self::$inCents)) {
            true => (int)$this->output[$key] * 100,
            default => $this->output[$key]
        };
    }

    public function getResultsInCents(): array
    {
        $results = [];
        collect($this->output)->map(function ($value, $key) use (&$results) {
            $results[$key] = $this->getResultInCents($key);
        });

        return $results;
    }

    public static function getInCents(array $data): array
    {
        $results = [];
        collect($data)->map(function ($value, $key) use (&$results) {
            $results[$key] = match (in_array($key, self::$inCents)) {
                true => (int)($value * 100),
                default => $value
            };
        });

        return $results;
    }

    protected function get($key)
    {
        return $this->input[$key];
    }

    public function setInput($key, $value)
    {
        return $this->input[$key] = $value;
    }
}
