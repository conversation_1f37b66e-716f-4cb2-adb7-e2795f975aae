<?php

namespace App\Services\CurrencyRatesExchange;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;

class ECB implements RatesExchangeProviderClass
{

    private string $url = 'https://data-api.ecb.europa.eu/service/data/EXR/';

    private string $format = 'jsondata';
    private string $details = 'dataonly';
    private string $dataVariation = 'A'; // A- average
    private string $dataFrequency = 'D'; // D- Daily
    private string $dataCurrencies = ''; // empty - all, other values: USD, GBP...
    private string $dataTableType = 'SP00'; //reference rates

    protected $baseCurrency = 'EUR';

    public function __construct(public Http $transport)
    {
    }

    public function getRates(\DateTimeInterface|string|null $date = null): ?CurrencyRateResponse
    {
        return $this->makeResponse($this->fetchRates($date));
    }

    public function setBaseCurrency(string $currencyIsoCode): self
    {
        $this->baseCurrency = $currencyIsoCode;
        return $this;
    }

    protected function fetchRates(\DateTimeInterface|string|null $date): ?array
    {
        $formatted = match ($date) {
            null => Carbon::yesterday()->format('Y-m-d'),
            default => match (is_string($date)) {
                true => Carbon::parse($date)->format('Y-m-d'),
                default => $date->format('Y-m-d'),
            }
        };
        $fullUrl = $this->buildUrl($formatted, $formatted);
        $response = $this->transport::get($fullUrl);
        if (!$response) {
            return null;
        }
        return json_decode($response, true);
    }

    protected function buildUrl(string $startDate, string $endDate): string
    {
        return $this->url .
            $this->dataFrequency .'.'.
            $this->dataCurrencies .'.'.
            $this->baseCurrency .'.'.
            $this->dataTableType .'.'.
            $this->dataVariation .
            '?format=' . $this->format .
            '&detail=' . $this->details .
            '&startPeriod=' . $startDate .
            '&endPeriod=' . $endDate;
    }

    protected function makeResponse(?array $fetchResponse): ?CurrencyRateResponse
    {
        if (null === $fetchResponse) {
            return null;
        }
        $response = new CurrencyRateResponse();
        $response->provider = 'ECB';
        $response->responseIdentifier = $fetchResponse['header']['id'];
        $response->baseCurrencyCode = $this->baseCurrency;
        $response->effectiveDate = Carbon::parse(
            $fetchResponse['structure']
            ['dimensions']
            ['observation'][0]
            ['values'][0]['id']
        );
        $response->rates = $this->parseRates($fetchResponse);
        return $response;
    }

    protected function parseRates(array $fetchResponse)
    {
        $currencies = [];
        $rates = [];
        foreach ($fetchResponse['structure']['dimensions']['series'] as $series) {
            if ($series['id'] === 'CURRENCY') {
                $currencies = $series['values'];
            }
        }

        foreach ($currencies as $k => $currency) {
            $rates[$currency['id']] = $fetchResponse['dataSets'][0]
                                                    ['series']
                                                    ['0:' . $k . ':0:0:0']
                                                    ['observations'][0][0];
        }
        return $rates;
    }
}
