<?php

namespace App\Services\CurrencyRatesExchange;

class CurrencyRateResponse implements \JsonSerializable
{
    public string $provider;
    public string $responseIdentifier;
    public \DateTimeInterface $effectiveDate;
    public string $baseCurrencyCode;
    /**
     * @var array['CurrencyCode' => 'Rate'] $rates
     */
    public array $rates = [];


    public function getRateFor($currencyIsoCode)
    {
        return $this->rates[$currencyIsoCode] ?? null;
    }

    public function jsonSerialize(): mixed
    {
        return [
            'provider' => $this->provider,
            'responseIdentifier' => $this->responseIdentifier,
            'baseCurrencyCode' => $this->baseCurrencyCode,
            'rates' => $this->rates,
            'effectiveDate' => $this->effectiveDate->format('Y-m-d'),
        ];
    }
}
