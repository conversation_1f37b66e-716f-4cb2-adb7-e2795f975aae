<?php

namespace App\Services\CurrencyRatesExchange;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;

class NBP implements RatesExchangeProviderClass
{

    private string $url = 'https://api.nbp.pl/api/exchangerates/tables/a';
    private string $format = 'json';

    protected $baseCurrency = 'PLN';

    public function __construct(public Http $transport)
    {
    }

    public function getRates(\DateTimeInterface|string|null $date = null): ?CurrencyRateResponse
    {
        return $this->makeResponse($this->fetchRates($date));
    }

    public function setBaseCurrency(string $currencyIsoCode): self
    {
        $this->baseCurrency = $currencyIsoCode;
        return $this;
    }

    protected function fetchRates(\DateTimeInterface|string|null $date): ?array
    {
        $formatted = match ($date) {
            null => Carbon::yesterday()->format('Y-m-d'),
            default => match (is_string($date)) {
                true => Carbon::parse($date)->format('Y-m-d'),
                default => $date->format('Y-m-d'),
            }
        };
        $fullUrl = $this->url . '/' . $formatted . '?format=' . $this->format;
        $response = $this->transport::get($fullUrl);
//        dd($response->json());
        if (!$response || $response->json() === null) {
            return null;
        }
        return json_decode($response, true)[0];
    }

    protected function makeResponse(?array $fetchResponse): ?CurrencyRateResponse
    {
        if (null === $fetchResponse) {
            return null;
        }
        $response = new CurrencyRateResponse();
        $response->provider = 'NBP';
        $response->responseIdentifier = $fetchResponse['no'];
        $response->baseCurrencyCode = $this->baseCurrency;
        $response->effectiveDate = Carbon::parse($fetchResponse['effectiveDate']);
        $response->rates = $this->parseRates($fetchResponse);
        return $response;
    }

    protected function parseRates(array $fetchResponse): array
    {
        $rates = array_column($fetchResponse['rates'], 'mid', 'code');
        foreach ($rates as &$rate) {
            $rate = round(1/$rate, 8);
        }

        return $rates;
    }

    public static function convertTo($amount, string $currencyISOCode)
    {
    }

    public static function howMuchIGetFor($amount, string $currencyISOCode)
    {
    }
}
