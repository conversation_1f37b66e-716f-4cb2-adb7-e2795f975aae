<?php

namespace App\Services;

class Filters
{
    public static function getFilter(array $eventFilter): array
    {
        $filters = [];
        foreach ($eventFilter as $key => $value) {
            $filters['x-on:'.$value] = match ($key) {
                'price' => '(event) => !filterInput.price(event) && event.preventDefault()',
                'int' => '(event) => !filterInput.int(event) && event.preventDefault()',
                default => '(event) => true',
            };
        }

        return $filters;
    }
}
