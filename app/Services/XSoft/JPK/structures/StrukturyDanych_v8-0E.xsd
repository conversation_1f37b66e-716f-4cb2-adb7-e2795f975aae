<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:etd="http://crd.gov.pl/xml/schematy/dziedzinowe/mf/2021/06/08/eD/DefinicjeTypy/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://crd.gov.pl/xml/schematy/dziedzinowe/mf/2021/06/08/eD/DefinicjeTypy/" elementFormDefault="qualified" attributeFormDefault="unqualified" xml:lang="pl">
	<xsd:include schemaLocation="http://crd.gov.pl/xml/schematy/dziedzinowe/mf/2021/06/08/eD/DefinicjeTypy/ElementarneTypyDanych_v8-0E.xsd"/>
	<!--Adres-->
	<xsd:complexType name="TAdres">
		<xsd:annotation>
			<xsd:documentation><PERSON></xsd:documentation>
		</xsd:annotation>
		<xsd:choice>
			<xsd:sequence>
				<xsd:element name="AdresPol" type="etd:TAdresPolski"/>
			</xsd:sequence>
			<xsd:sequence>
				<xsd:element name="AdresZagr" type="etd:TAdresZagraniczny"/>
			</xsd:sequence>
		</xsd:choice>
	</xsd:complexType>
	<xsd:complexType name="TAdres1">
		<xsd:annotation>
			<xsd:documentation>Dane określające adres - bez elementu Poczta w adresie polskim</xsd:documentation>
		</xsd:annotation>
		<xsd:choice>
			<xsd:sequence>
				<xsd:element name="AdresPol" type="etd:TAdresPolski1"/>
			</xsd:sequence>
			<xsd:sequence>
				<xsd:element name="AdresZagr" type="etd:TAdresZagraniczny"/>
			</xsd:sequence>
		</xsd:choice>
	</xsd:complexType>
	<xsd:complexType name="TAdresPolski">
		<xsd:annotation>
			<xsd:documentation>Informacje opisujące adres polski</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="KodKraju" type="etd:TKodKraju" fixed="PL">
				<xsd:annotation>
					<xsd:documentation>Kraj</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Wojewodztwo" type="etd:TJednAdmin">
				<xsd:annotation>
					<xsd:documentation>Województwo</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Powiat" type="etd:TJednAdmin">
				<xsd:annotation>
					<xsd:documentation>Powiat</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Gmina" type="etd:TJednAdmin">
				<xsd:annotation>
					<xsd:documentation>Gmina</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Ulica" type="etd:TUlica" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Nazwa ulicy</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="NrDomu" type="etd:TNrBudynku">
				<xsd:annotation>
					<xsd:documentation>Numer budynku</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="NrLokalu" type="etd:TNrLokalu" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Numer lokalu</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Miejscowosc" type="etd:TMiejscowosc">
				<xsd:annotation>
					<xsd:documentation>Nazwa miejscowości</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="KodPocztowy" type="etd:TKodPocztowy">
				<xsd:annotation>
					<xsd:documentation>Kod pocztowy</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Poczta" type="etd:TMiejscowosc">
				<xsd:annotation>
					<xsd:documentation>Nazwa urzędu pocztowego</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TAdresPolski1">
		<xsd:annotation>
			<xsd:documentation>Informacje opisujące adres polski - bez elementu Poczta</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="KodKraju" type="etd:TKodKraju" fixed="PL">
				<xsd:annotation>
					<xsd:documentation>Kraj</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Wojewodztwo" type="etd:TJednAdmin">
				<xsd:annotation>
					<xsd:documentation>Województwo</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Powiat" type="etd:TJednAdmin">
				<xsd:annotation>
					<xsd:documentation>Powiat</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Gmina" type="etd:TJednAdmin">
				<xsd:annotation>
					<xsd:documentation>Gmina</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Ulica" type="etd:TUlica" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Nazwa ulicy</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="NrDomu" type="etd:TNrBudynku">
				<xsd:annotation>
					<xsd:documentation>Numer budynku</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="NrLokalu" type="etd:TNrLokalu" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Numer lokalu</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Miejscowosc" type="etd:TMiejscowosc">
				<xsd:annotation>
					<xsd:documentation>Nazwa miejscowości</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="KodPocztowy" type="etd:TKodPocztowy">
				<xsd:annotation>
					<xsd:documentation>Kod pocztowy</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TAdresZagraniczny">
		<xsd:annotation>
			<xsd:documentation>Informacje opisujące adres zagraniczny</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="KodKraju">
				<xsd:annotation>
					<xsd:documentation>Kod Kraju [Country Code]</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="etd:TKodKraju">
						<xsd:pattern value="P[A-KM-Z]"/>
						<xsd:pattern value="[A-OQ-Z][A-Z]"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="KodPocztowy" type="etd:TKodPocztowy" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Kod pocztowy [Postal code]</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Miejscowosc" type="etd:TMiejscowosc">
				<xsd:annotation>
					<xsd:documentation>Nazwa miejscowości [City]</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Ulica" type="etd:TUlica" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Nazwa ulicy [Street]</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="NrDomu" type="etd:TNrBudynku" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Numer budynku [Building number]</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="NrLokalu" type="etd:TNrLokalu" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Numer lokalu [Flat number]</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!--Identyfikatory-->
	<xsd:complexType name="TIdentyfikatorOsobyFizycznej">
		<xsd:annotation>
			<xsd:documentation>Podstawowy zestaw danych identyfikacyjnych o osobie fizycznej</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="NIP" type="etd:TNrNIP">
				<xsd:annotation>
					<xsd:documentation>Identyfikator podatkowy NIP</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ImiePierwsze" type="etd:TImie">
				<xsd:annotation>
					<xsd:documentation>Pierwsze imię</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Nazwisko" type="etd:TNazwisko">
				<xsd:annotation>
					<xsd:documentation>Nazwisko</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="DataUrodzenia" type="etd:TData">
				<xsd:annotation>
					<xsd:documentation>Data urodzenia</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="PESEL" type="etd:TNrPESEL" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Identyfikator podatkowy numer PESEL</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TIdentyfikatorOsobyFizycznej1">
		<xsd:annotation>
			<xsd:documentation>Podstawowy zestaw danych identyfikacyjnych o osobie fizycznej z identyfikatorem NIP albo PESEL</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:choice>
				<xsd:element name="NIP" type="etd:TNrNIP">
					<xsd:annotation>
						<xsd:documentation>Identyfikator podatkowy NIP</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="PESEL" type="etd:TNrPESEL">
					<xsd:annotation>
						<xsd:documentation>Identyfikator podatkowy numer PESEL</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:choice>
			<xsd:element name="ImiePierwsze" type="etd:TImie">
				<xsd:annotation>
					<xsd:documentation>Pierwsze imię</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Nazwisko" type="etd:TNazwisko">
				<xsd:annotation>
					<xsd:documentation>Nazwisko</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="DataUrodzenia" type="etd:TData">
				<xsd:annotation>
					<xsd:documentation>Data urodzenia</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TIdentyfikatorOsobyFizycznej2">
		<xsd:annotation>
			<xsd:documentation>Podstawowy zestaw danych identyfikacyjnych o osobie fizycznej z identyfikatorem NIP</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="NIP" type="etd:TNrNIP">
				<xsd:annotation>
					<xsd:documentation>Identyfikator podatkowy NIP</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ImiePierwsze" type="etd:TImie">
				<xsd:annotation>
					<xsd:documentation>Pierwsze imię</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Nazwisko" type="etd:TNazwisko">
				<xsd:annotation>
					<xsd:documentation>Nazwisko</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="DataUrodzenia" type="etd:TData">
				<xsd:annotation>
					<xsd:documentation>Data urodzenia</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TIdentyfikatorOsobyFizycznejPelny">
		<xsd:annotation>
			<xsd:documentation>Pełny zestaw danych identyfikacyjnych o osobie fizycznej</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="NIP" type="etd:TNrNIP" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Identyfikator podatkowy NIP</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ImiePierwsze" type="etd:TImie">
				<xsd:annotation>
					<xsd:documentation>Pierwsze imię</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Nazwisko" type="etd:TNazwisko">
				<xsd:annotation>
					<xsd:documentation>Nazwisko</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="DataUrodzenia" type="etd:TData">
				<xsd:annotation>
					<xsd:documentation>Data urodzenia</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ImieOjca" type="etd:TImie">
				<xsd:annotation>
					<xsd:documentation>Imię ojca</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ImieMatki" type="etd:TImie">
				<xsd:annotation>
					<xsd:documentation>Imię matki</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="PESEL" type="etd:TNrPESEL">
				<xsd:annotation>
					<xsd:documentation>Identyfikator podatkowy numer PESEL</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TIdentyfikatorOsobyFizycznejZagranicznej">
		<xsd:annotation>
			<xsd:documentation>Zestaw danych identyfikacyjnych dla osoby fizycznej zagranicznej</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="ImiePierwsze" type="etd:TImie">
				<xsd:annotation>
					<xsd:documentation>Imię pierwsze [First name]</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Nazwisko" type="etd:TNazwisko">
				<xsd:annotation>
					<xsd:documentation>Nazwisko [Family name]</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="DataUrodzenia" type="etd:TData">
				<xsd:annotation>
					<xsd:documentation>Data urodzenia [Date of Birth]</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="MiejsceUrodzenia" type="etd:TMiejscowosc">
				<xsd:annotation>
					<xsd:documentation>Miejsce urodzenia [Place of Birth]</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ImieOjca" type="etd:TImie" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Imię ojca [Father’s name]</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ImieMatki" type="etd:TImie" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Imię matki [Mother’s name]</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="NIP" type="etd:TNrNIP" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Identyfikator podatkowy NIP [Tax Identification Number (NIP)]</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TIdentyfikatorOsobyNiefizycznej">
		<xsd:annotation>
			<xsd:documentation>Podstawowy zestaw danych identyfikacyjnych o osobie niefizycznej</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="NIP" type="etd:TNrNIP">
				<xsd:annotation>
					<xsd:documentation>Identyfikator podatkowy NIP</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="PelnaNazwa">
				<xsd:annotation>
					<xsd:documentation>Pełna nazwa</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="xsd:token">
						<xsd:minLength value="1"/>
						<xsd:maxLength value="240"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="REGON" type="etd:TNrREGON" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Numer REGON</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TIdentyfikatorOsobyNiefizycznej1">
		<xsd:annotation>
			<xsd:documentation>Podstawowy zestaw danych identyfikacyjnych o osobie niefizycznej  - bez elementu Numer REGON</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="NIP" type="etd:TNrNIP">
				<xsd:annotation>
					<xsd:documentation>Identyfikator podatkowy NIP</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="PelnaNazwa">
				<xsd:annotation>
					<xsd:documentation>Pełna nazwa</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="xsd:token">
						<xsd:minLength value="1"/>
						<xsd:maxLength value="240"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TIdentyfikatorOsobyNiefizycznejPelny">
		<xsd:annotation>
			<xsd:documentation>Pełny zestaw danych identyfikacyjnych o osobie niefizycznej</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="NIP" type="etd:TNrNIP" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Identyfikator podatkowy NIP</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="PelnaNazwa">
				<xsd:annotation>
					<xsd:documentation>Pełna nazwa</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="xsd:token">
						<xsd:minLength value="1"/>
						<xsd:maxLength value="240"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="SkroconaNazwa">
				<xsd:annotation>
					<xsd:documentation>Skrócona nazwa</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="xsd:token">
						<xsd:minLength value="1"/>
						<xsd:maxLength value="70"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="REGON" type="etd:TNrREGON">
				<xsd:annotation>
					<xsd:documentation>Numer REGON</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TIdentyfikatorOsobyNiefizycznejZagranicznej">
		<xsd:annotation>
			<xsd:documentation>Zestaw danych identyfikacyjnych dla osoby niefizycznej zagranicznej</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="PelnaNazwa">
				<xsd:annotation>
					<xsd:documentation>Pełna nazwa [Name]</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="xsd:token">
						<xsd:minLength value="1"/>
						<xsd:maxLength value="240"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="SkroconaNazwa" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Nazwa skrócona [Short Name]</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="xsd:token">
						<xsd:minLength value="1"/>
						<xsd:maxLength value="70"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="NIP" type="etd:TNrNIP" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Identyfikator podatkowy NIP [Tax Identification Number (NIP)]</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!--Dane podstawowe bez adresu (Do załączników)-->
	<xsd:complexType name="TPodmiotDowolnyBezAdresu">
		<xsd:annotation>
			<xsd:documentation>Skrócony zestaw danych o osobie fizycznej lub niefizycznej</xsd:documentation>
		</xsd:annotation>
		<xsd:choice>
			<xsd:element name="OsobaFizyczna" type="etd:TIdentyfikatorOsobyFizycznej"/>
			<xsd:element name="OsobaNiefizyczna" type="etd:TIdentyfikatorOsobyNiefizycznej"/>
		</xsd:choice>
	</xsd:complexType>
	<xsd:complexType name="TPodmiotDowolnyBezAdresu1">
		<xsd:annotation>
			<xsd:documentation>Skrócony zestaw danych o osobie fizycznej lub niefizycznej z identyfikatorem NIP albo PESEL</xsd:documentation>
		</xsd:annotation>
		<xsd:choice>
			<xsd:element name="OsobaFizyczna" type="etd:TIdentyfikatorOsobyFizycznej1"/>
			<xsd:element name="OsobaNiefizyczna" type="etd:TIdentyfikatorOsobyNiefizycznej"/>
		</xsd:choice>
	</xsd:complexType>
	<xsd:complexType name="TPodmiotDowolnyBezAdresu2">
		<xsd:annotation>
			<xsd:documentation>Skrócony zestaw danych o osobie fizycznej lub niefizycznej z identyfikatorem NIP</xsd:documentation>
		</xsd:annotation>
		<xsd:choice>
			<xsd:element name="OsobaFizyczna" type="etd:TIdentyfikatorOsobyFizycznej2"/>
			<xsd:element name="OsobaNiefizyczna" type="etd:TIdentyfikatorOsobyNiefizycznej"/>
		</xsd:choice>
	</xsd:complexType>
	<xsd:complexType name="TPodmiotDowolnyBezAdresu3">
		<xsd:annotation>
			<xsd:documentation>Skrócony zestaw danych o osobie fizycznej lub niefizycznej z identyfikatorem NIP - bez elementu numer REGON dla osoby niefizycznej</xsd:documentation>
		</xsd:annotation>
		<xsd:choice>
			<xsd:element name="OsobaFizyczna" type="etd:TIdentyfikatorOsobyFizycznej2"/>
			<xsd:element name="OsobaNiefizyczna" type="etd:TIdentyfikatorOsobyNiefizycznej1"/>
		</xsd:choice>
	</xsd:complexType>
	<!--Dane podstawowe z adresem-->
	<xsd:complexType name="TOsobaFizyczna">
		<xsd:annotation>
			<xsd:documentation>Podstawowy zestaw danych o osobie fizycznej</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="OsobaFizyczna" type="etd:TIdentyfikatorOsobyFizycznej"/>
			<xsd:element name="AdresZamieszkania">
				<xsd:complexType>
					<xsd:complexContent>
						<xsd:extension base="etd:TAdres">
							<xsd:attribute name="rodzajAdresu" type="xsd:string" use="required" fixed="RAD"/>
						</xsd:extension>
					</xsd:complexContent>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TOsobaFizyczna1">
		<xsd:annotation>
			<xsd:documentation>Podstawowy zestaw danych o osobie fizycznej z identyfikatorem NIP albo PESEL</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="OsobaFizyczna" type="etd:TIdentyfikatorOsobyFizycznej1"/>
			<xsd:element name="AdresZamieszkania">
				<xsd:complexType>
					<xsd:complexContent>
						<xsd:extension base="etd:TAdres">
							<xsd:attribute name="rodzajAdresu" type="xsd:string" use="required" fixed="RAD"/>
						</xsd:extension>
					</xsd:complexContent>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TOsobaFizyczna5">
		<xsd:annotation>
			<xsd:documentation>Podstawowy zestaw danych o osobie fizycznej - bez elementu Poczta w adresie polskim</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="OsobaFizyczna" type="etd:TIdentyfikatorOsobyFizycznej"/>
			<xsd:element name="AdresZamieszkania">
				<xsd:complexType>
					<xsd:complexContent>
						<xsd:extension base="etd:TAdres1">
							<xsd:attribute name="rodzajAdresu" type="xsd:string" use="required" fixed="RAD"/>
						</xsd:extension>
					</xsd:complexContent>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TOsobaFizyczna3">
		<xsd:annotation>
			<xsd:documentation>Podstawowy zestaw danych o osobie fizycznej z identyfikatorem NIP albo PESEL - bez elementu Poczta w adresie polskim</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="OsobaFizyczna" type="etd:TIdentyfikatorOsobyFizycznej1"/>
			<xsd:element name="AdresZamieszkania">
				<xsd:complexType>
					<xsd:complexContent>
						<xsd:extension base="etd:TAdres1">
							<xsd:attribute name="rodzajAdresu" type="xsd:string" use="required" fixed="RAD"/>
						</xsd:extension>
					</xsd:complexContent>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TOsobaFizyczna2">
		<xsd:annotation>
			<xsd:documentation>Podstawowy zestaw danych o osobie fizycznej z identyfikatorem NIP</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="OsobaFizyczna" type="etd:TIdentyfikatorOsobyFizycznej2"/>
			<xsd:element name="AdresZamieszkania">
				<xsd:complexType>
					<xsd:complexContent>
						<xsd:extension base="etd:TAdres">
							<xsd:attribute name="rodzajAdresu" type="xsd:string" use="required" fixed="RAD"/>
						</xsd:extension>
					</xsd:complexContent>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TOsobaNiefizyczna">
		<xsd:annotation>
			<xsd:documentation>Podstawowy zestaw danych o osobie niefizycznej</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="OsobaNiefizyczna" type="etd:TIdentyfikatorOsobyNiefizycznej"/>
			<xsd:element name="AdresSiedziby">
				<xsd:complexType>
					<xsd:complexContent>
						<xsd:extension base="etd:TAdres">
							<xsd:attribute name="rodzajAdresu" type="xsd:string" use="required" fixed="RAD"/>
						</xsd:extension>
					</xsd:complexContent>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TOsobaNiefizyczna1">
		<xsd:annotation>
			<xsd:documentation>Podstawowy zestaw danych o osobie niefizycznej - bez elementu Poczta w adresie polskim</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="OsobaNiefizyczna" type="etd:TIdentyfikatorOsobyNiefizycznej"/>
			<xsd:element name="AdresSiedziby">
				<xsd:complexType>
					<xsd:complexContent>
						<xsd:extension base="etd:TAdres1">
							<xsd:attribute name="rodzajAdresu" type="xsd:string" use="required" fixed="RAD"/>
						</xsd:extension>
					</xsd:complexContent>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TOsobaNiefizyczna2">
		<xsd:annotation>
			<xsd:documentation>Podstawowy zestaw danych o osobie niefizycznej - bez elementu Numer REGON oraz bez elementu Poczta w adresie polskim</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="OsobaNiefizyczna" type="etd:TIdentyfikatorOsobyNiefizycznej1"/>
			<xsd:element name="AdresSiedziby">
				<xsd:complexType>
					<xsd:complexContent>
						<xsd:extension base="etd:TAdres1">
							<xsd:attribute name="rodzajAdresu" type="xsd:string" use="required" fixed="RAD"/>
						</xsd:extension>
					</xsd:complexContent>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TOsobaFizyczna4">
		<xsd:annotation>
			<xsd:documentation>Podstawowy zestaw danych o osobie fizycznej z identyfikatorem NIP - bez elementu Poczta w adresie polskim</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="OsobaFizyczna" type="etd:TIdentyfikatorOsobyFizycznej2"/>
			<xsd:element name="AdresZamieszkania">
				<xsd:complexType>
					<xsd:complexContent>
						<xsd:extension base="etd:TAdres1">
							<xsd:attribute name="rodzajAdresu" type="xsd:string" use="required" fixed="RAD"/>
						</xsd:extension>
					</xsd:complexContent>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!--Dane pełne z adresem (NIP opcjonalny)-->
	<xsd:complexType name="TPodmiotDowolny">
		<xsd:annotation>
			<xsd:documentation>Podstawowy zestaw danych o osobie fizycznej lub niefizycznej</xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:extension base="etd:TPodmiotDowolnyBezAdresu">
				<xsd:sequence>
					<xsd:element name="AdresZamieszkaniaSiedziby">
						<xsd:complexType>
							<xsd:complexContent>
								<xsd:extension base="etd:TAdres">
									<xsd:attribute name="rodzajAdresu" type="xsd:string" use="required" fixed="RAD"/>
								</xsd:extension>
							</xsd:complexContent>
						</xsd:complexType>
					</xsd:element>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<xsd:complexType name="TOsobaFizycznaPelna">
		<xsd:annotation>
			<xsd:documentation>Pełny zestaw danych o osobie fizycznej</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="OsobaFizyczna" type="etd:TIdentyfikatorOsobyFizycznejPelny"/>
			<xsd:element name="AdresZamieszkania">
				<xsd:complexType>
					<xsd:complexContent>
						<xsd:extension base="etd:TAdres">
							<xsd:attribute name="rodzajAdresu" type="xsd:string" use="required" fixed="RAD"/>
						</xsd:extension>
					</xsd:complexContent>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TOsobaFizycznaPelna1">
		<xsd:annotation>
			<xsd:documentation>Pełny zestaw danych o osobie fizycznej - bez elementu Poczta w adresie polskim</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="OsobaFizyczna" type="etd:TIdentyfikatorOsobyFizycznejPelny"/>
			<xsd:element name="AdresZamieszkania">
				<xsd:complexType>
					<xsd:complexContent>
						<xsd:extension base="etd:TAdres1">
							<xsd:attribute name="rodzajAdresu" type="xsd:string" use="required" fixed="RAD"/>
						</xsd:extension>
					</xsd:complexContent>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TPodmiotDowolny1">
		<xsd:annotation>
			<xsd:documentation>Podstawowy zestaw danych o osobie fizycznej lub niefizycznej - bez elementu Poczta w adresie polskim</xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:extension base="etd:TPodmiotDowolnyBezAdresu">
				<xsd:sequence>
					<xsd:element name="AdresZamieszkaniaSiedziby">
						<xsd:complexType>
							<xsd:complexContent>
								<xsd:extension base="etd:TAdres1">
									<xsd:attribute name="rodzajAdresu" type="xsd:string" use="required" fixed="RAD"/>
								</xsd:extension>
							</xsd:complexContent>
						</xsd:complexType>
					</xsd:element>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<xsd:complexType name="TPodmiotDowolny2">
		<xsd:annotation>
			<xsd:documentation>Podstawowy zestaw danych o osobie fizycznej lub niefizycznej - bez elementu Numer REGON oraz bez elementu Poczta w adresie polskim</xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:extension base="etd:TPodmiotDowolnyBezAdresu3">
				<xsd:sequence>
					<xsd:element name="AdresZamieszkaniaSiedziby">
						<xsd:complexType>
							<xsd:complexContent>
								<xsd:extension base="etd:TAdres1">
									<xsd:attribute name="rodzajAdresu" type="xsd:string" use="required" fixed="RAD"/>
								</xsd:extension>
							</xsd:complexContent>
						</xsd:complexType>
					</xsd:element>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<xsd:complexType name="TOsobaNiefizycznaPelna">
		<xsd:annotation>
			<xsd:documentation>Pełny zestaw danych o niefizycznej</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="OsobaNiefizyczna" type="etd:TIdentyfikatorOsobyNiefizycznejPelny"/>
			<xsd:element name="AdresSiedziby">
				<xsd:complexType>
					<xsd:complexContent>
						<xsd:extension base="etd:TAdres">
							<xsd:attribute name="rodzajAdresu" type="xsd:string" use="required" fixed="RAD"/>
						</xsd:extension>
					</xsd:complexContent>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TPodmiotDowolnyPelny">
		<xsd:annotation>
			<xsd:documentation>Pełny zestaw danych o osobie fizycznej lub niefizycznej</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:choice>
				<xsd:element name="OsobaFizyczna" type="etd:TIdentyfikatorOsobyFizycznejPelny"/>
				<xsd:element name="OsobaNiefizyczna" type="etd:TIdentyfikatorOsobyNiefizycznejPelny"/>
			</xsd:choice>
			<xsd:element name="AdresZamieszkaniaSiedziby">
				<xsd:complexType>
					<xsd:complexContent>
						<xsd:extension base="etd:TAdres">
							<xsd:attribute name="rodzajAdresu" type="xsd:string" use="required" fixed="RAD"/>
						</xsd:extension>
					</xsd:complexContent>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TPodmiotDowolnyPelny1">
		<xsd:annotation>
			<xsd:documentation>Pełny zestaw danych o osobie fizycznej lub niefizycznej - bez elementu Poczta w adresie polskim</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:choice>
				<xsd:element name="OsobaFizyczna" type="etd:TIdentyfikatorOsobyFizycznejPelny"/>
				<xsd:element name="OsobaNiefizyczna" type="etd:TIdentyfikatorOsobyNiefizycznejPelny"/>
			</xsd:choice>
			<xsd:element name="AdresZamieszkaniaSiedziby">
				<xsd:complexType>
					<xsd:complexContent>
						<xsd:extension base="etd:TAdres1">
							<xsd:attribute name="rodzajAdresu" type="xsd:string" use="required" fixed="RAD"/>
						</xsd:extension>
					</xsd:complexContent>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TOsobaNiefizycznaPelna1">
		<xsd:annotation>
			<xsd:documentation>Pełny zestaw danych o osobie niefizycznej - bez elementu Poczta w adresie polskim</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="OsobaNiefizyczna" type="etd:TIdentyfikatorOsobyNiefizycznejPelny"/>
			<xsd:element name="AdresSiedziby">
				<xsd:complexType>
					<xsd:complexContent>
						<xsd:extension base="etd:TAdres1">
							<xsd:attribute name="rodzajAdresu" type="xsd:string" use="required" fixed="RAD"/>
						</xsd:extension>
					</xsd:complexContent>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
</xsd:schema>
