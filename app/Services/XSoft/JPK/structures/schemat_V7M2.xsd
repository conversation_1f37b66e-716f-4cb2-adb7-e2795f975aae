<?xml version="1.0" encoding="UTF-8"?><xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:etd="http://crd.gov.pl/xml/schematy/dziedzinowe/mf/2021/06/08/eD/DefinicjeTypy/" xmlns:tns="http://crd.gov.pl/wzor/2021/12/27/11148/" targetNamespace="http://crd.gov.pl/wzor/2021/12/27/11148/" elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.0" xml:lang="pl">
	<xsd:import namespace="http://crd.gov.pl/xml/schematy/dziedzinowe/mf/2021/06/08/eD/DefinicjeTypy/" schemaLocation="http://crd.gov.pl/xml/schematy/dziedzinowe/mf/2021/06/08/eD/DefinicjeTypy/StrukturyDanych_v8-0E.xsd"/>
	<xsd:simpleType name="TKodFormularza">
		<xsd:annotation>
			<xsd:documentation>Symbol wzoru formularza</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="JPK_VAT"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TDataCzas">
		<xsd:restriction base="etd:TDataCzas">
			<xsd:minInclusive value="2022-01-01T00:00:00Z"/>
			<xsd:maxInclusive value="2050-12-31T23:59:59Z"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TDataT">
		<xsd:annotation>
			<xsd:documentation>Data transakcji lub zdarzenia</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="etd:TData">
			<xsd:minInclusive value="2006-01-01"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TDataTP">
		<xsd:annotation>
			<xsd:documentation>Data upływu terminu płatności lub dokonania zapłaty</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="etd:TData">
			<xsd:minInclusive value="2016-01-01"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="TNaglowek">
		<xsd:annotation>
			<xsd:documentation>Nagłówek JPK_VAT</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="KodFormularza">
				<xsd:complexType>
					<xsd:simpleContent>
						<xsd:extension base="tns:TKodFormularza">
							<xsd:attribute name="kodSystemowy" type="xsd:string" use="required" fixed="JPK_V7M (2)"/>
							<xsd:attribute name="wersjaSchemy" type="xsd:string" use="required" fixed="1-0E"/>
						</xsd:extension>
					</xsd:simpleContent>
				</xsd:complexType>
			</xsd:element>
			<xsd:element name="WariantFormularza">
				<xsd:simpleType>
					<xsd:restriction base="xsd:byte">
						<xsd:enumeration value="2"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="DataWytworzeniaJPK" type="tns:TDataCzas">
				<xsd:annotation>
					<xsd:documentation>Data i czas sporządzenia JPK_VAT</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="NazwaSystemu" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Nazwa systemu, z którego pochodzą dane</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="xsd:token">
						<xsd:minLength value="1"/>
						<xsd:maxLength value="240"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="CelZlozenia">
				<xsd:complexType>
					<xsd:simpleContent>
						<xsd:extension base="etd:TCelZlozenia">
							<xsd:attribute name="poz" type="xsd:string" use="required" fixed="P_7"/>
						</xsd:extension>
					</xsd:simpleContent>
				</xsd:complexType>
			</xsd:element>
			<xsd:element name="KodUrzedu" type="etd:TKodUS"/>
			<xsd:element name="Rok">
				<xsd:simpleType>
					<xsd:restriction base="etd:TRok">
						<xsd:minInclusive value="2022"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="Miesiac" type="etd:TMiesiac"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:simpleType name="TKwotowy">
		<xsd:annotation>
			<xsd:documentation>Wartość numeryczna 18 znaków max, w tym 2 znaki po przecinku</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="18"/>
			<xsd:fractionDigits value="2"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TNaturalnyJPK">
		<xsd:annotation>
			<xsd:documentation>Liczby naturalne większe od zera</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="etd:TNaturalny">
			<xsd:minExclusive value="0"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TZnakowyJPK">
		<xsd:annotation>
			<xsd:documentation>Typ znakowy ograniczony do 256 znaków</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:token">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="256"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TDowoduSprzedazy">
		<xsd:annotation>
			<xsd:documentation>Oznaczenie dowodu sprzedaży</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="RO"/>
			<xsd:enumeration value="WEW"/>
			<xsd:enumeration value="FP"/>
		</xsd:restriction>
		<!--Wypełnić zgodnie z § 10 ust. 5 rozporządzenia Ministra Finansów, Inwestycji i Rozwoju z dnia 15 października 2019 r. w sprawie szczegółowego zakresu danych zawartych w deklaracjach podatkowych i w ewidencji w zakresie podatku od towarów i usług (Dz. U. z 2019 r. poz. 1988, z późn. zm.)-->
	</xsd:simpleType>
	<xsd:simpleType name="TDowoduZakupu">
		<xsd:annotation>
			<xsd:documentation>Oznaczenie dowodu nabycia</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="MK"/>
			<xsd:enumeration value="VAT_RR"/>
			<xsd:enumeration value="WEW"/>
		</xsd:restriction>
		<!--Wypełnić zgodnie z § 11 ust. 8 rozporządzenia Ministra Finansów, Inwestycji i Rozwoju z dnia 15 października 2019 r. w sprawie szczegółowego zakresu danych zawartych w deklaracjach podatkowych i w ewidencji w zakresie podatku od towarów i usług.-->
	</xsd:simpleType>
	<xsd:simpleType name="TAdresEmail">
		<xsd:annotation>
			<xsd:documentation>Adres poczty elektronicznej</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:token">
			<xsd:minLength value="3"/>
			<xsd:maxLength value="255"/>
			<xsd:pattern value="(.)+@(.)+"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="TIdentyfikatorOsobyNiefizycznej">
		<xsd:annotation>
			<xsd:documentation>Podstawowy zestaw danych identyfikacyjnych o osobie niefizycznej</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="NIP" type="etd:TNrNIP">
				<xsd:annotation>
					<xsd:documentation>Identyfikator podatkowy NIP</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="PelnaNazwa">
				<xsd:annotation>
					<xsd:documentation>Pełna nazwa</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="xsd:token">
						<xsd:minLength value="1"/>
						<xsd:maxLength value="240"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TPodmiotDowolnyBezAdresu">
		<xsd:annotation>
			<xsd:documentation>Skrócony zestaw danych o osobie fizycznej lub niefizycznej z identyfikatorem NIP</xsd:documentation>
		</xsd:annotation>
		<xsd:choice>
			<xsd:element name="OsobaFizyczna">
				<xsd:complexType>
					<xsd:complexContent>
						<xsd:extension base="etd:TIdentyfikatorOsobyFizycznej2">
							<xsd:sequence>
								<xsd:element name="Email" type="tns:TAdresEmail"/>
								<xsd:element name="Telefon" minOccurs="0">
									<xsd:annotation>
										<xsd:documentation>Numer telefonu kontaktowego</xsd:documentation>
									</xsd:annotation>
									<xsd:simpleType>
										<xsd:restriction base="etd:TZnakowy">
											<xsd:maxLength value="16"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:element>
							</xsd:sequence>
						</xsd:extension>
					</xsd:complexContent>
				</xsd:complexType>
			</xsd:element>
			<xsd:element name="OsobaNiefizyczna">
				<xsd:complexType>
					<xsd:complexContent>
						<xsd:extension base="tns:TIdentyfikatorOsobyNiefizycznej">
							<xsd:sequence>
								<xsd:element name="Email" type="tns:TAdresEmail"/>
								<xsd:element name="Telefon" minOccurs="0">
									<xsd:annotation>
										<xsd:documentation>Numer telefonu kontaktowego</xsd:documentation>
									</xsd:annotation>
									<xsd:simpleType>
										<xsd:restriction base="etd:TZnakowy">
											<xsd:maxLength value="16"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:element>
							</xsd:sequence>
						</xsd:extension>
					</xsd:complexContent>
				</xsd:complexType>
			</xsd:element>
		</xsd:choice>
	</xsd:complexType>
	<xsd:simpleType name="TKodKraju">
		<xsd:union memberTypes="etd:TKodKraju tns:TKodKrajuEL"/>
	</xsd:simpleType>
	<xsd:simpleType name="TKodKrajuEL">
		<xsd:annotation>
			<xsd:documentation>Dodanie do słownika kodów krajów unijnego kodu dla Grecji - EL</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:normalizedString">
			<xsd:enumeration value="EL">
				<xsd:annotation>
					<xsd:documentation>GRECJA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TKodKrajuJPK">
		<xsd:annotation>
			<xsd:documentation>Wyklucznie ze słownika kodów krajów kodu dla Grecji - GR</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="tns:TKodKraju">
			<xsd:pattern value="[A-FH-Z][A-Z]"/>
			<xsd:pattern value="[A-Z][A-QS-Z]"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TKodFormularzaVAT7">
		<xsd:annotation>
			<xsd:documentation>Symbol wzoru formularza</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="VAT-7"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:element name="JPK">
		<xsd:annotation>
			<xsd:documentation>Jednolity plik kontrolny dla ewidencji w zakresie rozliczenia podatku należnego i naliczonego oraz deklaracji VAT-7</xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="Naglowek">
					<xsd:annotation>
						<xsd:documentation>Nagłówek JPK_VAT</xsd:documentation>
					</xsd:annotation>
					<xsd:complexType>
						<xsd:complexContent>
							<xsd:extension base="tns:TNaglowek"/>
						</xsd:complexContent>
					</xsd:complexType>
				</xsd:element>
				<xsd:element name="Podmiot1">
					<xsd:complexType>
						<xsd:complexContent>
							<xsd:extension base="tns:TPodmiotDowolnyBezAdresu">
								<xsd:attribute name="rola" type="xsd:string" use="required" fixed="Podatnik"/>
							</xsd:extension>
						</xsd:complexContent>
					</xsd:complexType>
				</xsd:element>
				<xsd:element name="Deklaracja" minOccurs="0">
					<xsd:annotation>
						<xsd:documentation>DEKLARACJA DLA PODATKU OD TOWARÓW I USŁUG</xsd:documentation>
					</xsd:annotation>
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="Naglowek">
								<xsd:complexType>
									<xsd:sequence>
										<xsd:element name="KodFormularzaDekl">
											<xsd:annotation>
												<xsd:documentation>Kod formularza VAT-7</xsd:documentation>
											</xsd:annotation>
											<xsd:complexType>
												<xsd:simpleContent>
													<xsd:extension base="tns:TKodFormularzaVAT7">
														<xsd:attribute name="kodSystemowy" type="xsd:string" use="required" fixed="VAT-7 (22)"/>
														<xsd:attribute name="kodPodatku" type="xsd:string" use="required" fixed="VAT"/>
														<xsd:attribute name="rodzajZobowiazania" type="xsd:token" use="required" fixed="Z"/>
														<xsd:attribute name="wersjaSchemy" type="xsd:string" use="required" fixed="1-0E"/>
													</xsd:extension>
												</xsd:simpleContent>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="WariantFormularzaDekl">
											<xsd:annotation>
												<xsd:documentation>Wariant formularza VAT-7</xsd:documentation>
											</xsd:annotation>
											<xsd:simpleType>
												<xsd:restriction base="xsd:byte">
													<xsd:enumeration value="22"/>
												</xsd:restriction>
											</xsd:simpleType>
										</xsd:element>
									</xsd:sequence>
								</xsd:complexType>
							</xsd:element>
							<xsd:element name="PozycjeSzczegolowe">
								<xsd:annotation>
									<xsd:documentation>Dane niezbędne do obliczenia wysokości podatku należnego, obliczenia wysokości podatku naliczonego, obliczenia wysokości podatku lub zwrotu podatku wraz z oznaczeniem sposobu dokonania tego zwrotu</xsd:documentation>
								</xsd:annotation>
								<xsd:complexType>
									<xsd:sequence>
										<xsd:element name="P_10" type="etd:TKwotaC" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Wysokość podstawy opodatkowania z tytułu dostawy towarów oraz świadczenia usług na terytorium kraju, zwolnionych od podatku</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:sequence minOccurs="0">
											<xsd:element name="P_11" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania z tytułu dostawy towarów oraz świadczenia usług poza terytorium kraju</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="P_12" type="etd:TKwotaC" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania z tytułu świadczenia usług, o których mowa w art. 100 ust. 1 pkt 4 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
										</xsd:sequence>
										<xsd:sequence minOccurs="0">
											<xsd:element name="P_13" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania z tytułu dostawy towarów oraz świadczenia usług na terytorium kraju, opodatkowanych stawką 0%</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="P_14" type="etd:TKwotaC" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania z tytułu dostawy towarów, o której mowa w art. 129 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
										</xsd:sequence>
										<xsd:sequence minOccurs="0">
											<xsd:element name="P_15" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania z tytułu dostawy towarów oraz świadczenia usług na terytorium kraju, opodatkowanych stawką 5%, oraz korekty dokonanej zgodnie z art. 89a ust. 1 i 4 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="P_16" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podatku należnego z tytułu dostawy towarów oraz świadczenia usług na terytorium kraju, opodatkowanych stawką 5%, oraz korekty dokonanej zgodnie z art. 89a ust. 1 i 4 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
										</xsd:sequence>
										<xsd:sequence minOccurs="0">
											<xsd:element name="P_17" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania z tytułu dostawy towarów oraz świadczenia usług na terytorium kraju, opodatkowanych stawką 7% albo 8%, oraz korekty dokonanej zgodnie z art. 89a ust. 1 i 4 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="P_18" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podatku należnego z tytułu dostawy towarów oraz świadczenia usług na terytorium kraju, opodatkowanych stawką 7% albo 8%, oraz korekty dokonanej zgodnie z art. 89a ust. 1 i 4 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
										</xsd:sequence>
										<xsd:sequence minOccurs="0">
											<xsd:element name="P_19" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania z tytułu dostawy towarów oraz świadczenia usług na terytorium kraju, opodatkowanych stawką 22% albo 23%, oraz korekty dokonanej zgodnie z art. 89a ust. 1 i 4 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="P_20" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podatku należnego z tytułu dostawy towarów oraz świadczenia usług na terytorium kraju, opodatkowanych stawką 22% albo 23%, oraz korekty dokonanej zgodnie z art. 89a ust. 1 i 4 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
										</xsd:sequence>
										<xsd:element name="P_21" type="etd:TKwotaC" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Wysokość podstawy opodatkowania z tytułu wewnątrzwspólnotowej dostawy towarów</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="P_22" type="etd:TKwotaC" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Wysokość podstawy opodatkowania z tytułu eksportu towarów</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:sequence minOccurs="0">
											<xsd:element name="P_23" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania z tytułu wewnątrzwspólnotowego nabycia towarów</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="P_24" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podatku należnego z tytułu wewnątrzwspólnotowego nabycia towarów</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
										</xsd:sequence>
										<xsd:sequence minOccurs="0">
											<xsd:element name="P_25" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania z tytułu importu towarów rozliczanego zgodnie z art. 33a ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="P_26" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podatku należnego z tytułu importu towarów rozliczanego zgodnie z art. 33a ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
										</xsd:sequence>
										<xsd:sequence minOccurs="0">
											<xsd:element name="P_27" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania z tytułu importu usług, z wyłączeniem usług nabywanych od podatników podatku od wartości dodanej, do których stosuje się art. 28b ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="P_28" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podatku należnego z tytułu importu usług, z wyłączeniem usług nabywanych od podatników podatku od wartości dodanej, do których stosuje się art. 28b ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
										</xsd:sequence>
										<xsd:sequence minOccurs="0">
											<xsd:element name="P_29" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania z tytułu importu usług nabywanych od podatników podatku od wartości dodanej, do których stosuje się art. 28b ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="P_30" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podatku należnego z tytułu importu usług nabywanych od podatników podatku od wartości dodanej, do których stosuje się art. 28b ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
										</xsd:sequence>
										<xsd:sequence minOccurs="0">
											<xsd:element name="P_31" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania z tytułu dostawy towarów, dla których podatnikiem jest nabywca zgodnie z art. 17 ust. 1 pkt 5 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="P_32" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podatku należnego z tytułu dostawy towarów, dla których podatnikiem jest nabywca zgodnie z art. 17 ust. 1 pkt 5 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
										</xsd:sequence>
										<xsd:element name="P_33" type="etd:TKwotaCNieujemna" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Wysokość podatku należnego od towarów objętych spisem z natury, o którym mowa w art. 14 ust. 5 ustawy</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="P_34" type="etd:TKwotaCNieujemna" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Wysokość zwrotu odliczonej lub zwróconej kwoty wydanej na zakup kas rejestrujących, o którym mowa w art. 111 ust. 6 ustawy</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="P_35" type="etd:TKwotaCNieujemna" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Wysokość podatku należnego od wewnątrzwspólnotowego nabycia środków transportu, wykazana w wysokości podatku należnego z tytułu określonego w P_24, podlegająca wpłacie w terminie, o którym mowa w art. 103 ust. 3, w związku z ust. 4 ustawy</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="P_36" type="etd:TKwotaCNieujemna" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Wysokość podatku od wewnątrzwspólnotowego nabycia towarów, o których mowa w art. 103 ust. 5aa ustawy, podlegająca wpłacie w terminach, o których mowa w art. 103 ust. 5a i 5b ustawy</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:sequence>
											<xsd:element name="P_37" type="etd:TKwotaC" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Łączna wysokość podstawy opodatkowania. Suma kwot z P_10, P_11, P_13, P_15, P_17, P_19, P_21, P_22, P_23, P_25, P_27, P_29, P_31</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="P_38" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Łączna wysokość podatku należnego. Suma kwot z P_16, P_18, P_20, P_24, P_26, P_28, P_30, P_32, P_33, P_34 pomniejszona o kwotę z P_35 i P_36</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
										</xsd:sequence>
										<xsd:element name="P_39" type="etd:TKwotaCNieujemna" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Wysokość nadwyżki podatku naliczonego nad należnym z poprzedniej deklaracji</xsd:documentation>
											</xsd:annotation>
											<!--Kwota z P_62 wykazana w poprzedniej deklaracji lub wynikająca z decyzji.-->
										</xsd:element>
										<xsd:sequence minOccurs="0">
											<xsd:element name="P_40" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wartość netto z tytułu nabycia towarów i usług zaliczanych u podatnika do środków trwałych</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="P_41" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podatku naliczonego z tytułu nabycia towarów i usług zaliczanych u podatnika do środków trwałych</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
										</xsd:sequence>
										<xsd:sequence minOccurs="0">
											<xsd:element name="P_42" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wartość netto z tytułu nabycia pozostałych towarów i usług</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="P_43" type="etd:TKwotaC">
												<xsd:annotation>
													<xsd:documentation>Wysokość podatku naliczonego z tytułu nabycia pozostałych towarów i usług</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
										</xsd:sequence>
										<xsd:element name="P_44" type="etd:TKwotaC" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Wysokość podatku naliczonego z tytułu korekty podatku naliczonego od nabycia towarów i usług zaliczanych u podatnika do środków trwałych</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="P_45" type="etd:TKwotaC" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Wysokość podatku naliczonego z tytułu korekty podatku naliczonego od nabycia pozostałych towarów i usług</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="P_46" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Wysokość podatku naliczonego z tytułu korekty podatku naliczonego, o której mowa w art. 89b ust. 1 ustawy</xsd:documentation>
											</xsd:annotation>
											<xsd:simpleType>
												<xsd:restriction base="etd:TKwotaC">
													<xsd:maxInclusive value="0"/>
												</xsd:restriction>
											</xsd:simpleType>
										</xsd:element>
										<xsd:element name="P_47" type="etd:TKwotaCNieujemna" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Wysokość podatku naliczonego z tytułu korekty podatku naliczonego, o której mowa w art. 89b ust. 4 ustawy</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="P_48" type="etd:TKwotaC" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Łączna wysokość podatku naliczonego do odliczenia. Suma kwot z P_39, P_41, P_43, P_44, P_45, P_46 i P_47</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="P_49" type="etd:TKwotaCNieujemna" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Kwota wydana na zakup kas rejestrujących, do odliczenia w danym okresie rozliczeniowym pomniejszająca wysokość podatku należnego</xsd:documentation>
											</xsd:annotation>
											<!--Kwota wykazana w P_49 nie może być wyższa od różnicy kwot z P_38 i P_48. Jeżeli różnica kwot pomiędzy P_38 i P_48 jest mniejsza lub równa 0, wówczas należy wykazać 0.-->
										</xsd:element>
										<xsd:element name="P_50" type="etd:TKwotaCNieujemna" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Wysokość podatku objęta zaniechaniem poboru</xsd:documentation>
											</xsd:annotation>
											<!--Kwota ta nie może być wyższa niż różnica pomiędzy kwotą z P_38 a sumą kwot z P_48 i P_49. Jeżeli różnica kwot pomiędzy P_38 i P_48, pomniejszona o kwotę z P_49 jest mniejsza od 0, wówczas należy wykazać 0.-->
										</xsd:element>
										<xsd:element name="P_51" type="etd:TKwotaCNieujemna">
											<xsd:annotation>
												<xsd:documentation>Wysokość podatku podlegająca wpłacie do urzędu skarbowego</xsd:documentation>
											</xsd:annotation>
											<!--Jeżeli różnica kwot pomiędzy P_38 i P_48 jest większa od 0, wówczas P_51 = P_38 - P_48 - P_49 - P_50, w przeciwnym wypadku należy wykazać 0.-->
										</xsd:element>
										<xsd:element name="P_52" type="etd:TKwotaCNieujemna" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Kwota wydana na zakup kas rejestrujących, do odliczenia w danym okresie rozliczeniowym przysługująca do zwrotu w danym okresie rozliczeniowym lub powiększająca wysokość podatku naliczonego do przeniesienia na następny okres rozliczeniowy</xsd:documentation>
											</xsd:annotation>
											<!--W przypadku gdy kwota wykazana w P_48 jest większa lub równa kwocie z P_38 w danym okresie rozliczeniowym lub kwota ulgi z tytułu zakupu kas rejestrujących jest wyższa od nadwyżki podatku należnego nad naliczonym wówczas w P_52 wykazuje się pozostałą nieodliczoną w P_49 kwotę ulgi z tytułu zakupu kas rejestrujących, przysługującą podatnikowi do zwrotu lub do odliczenia od podatku należnego za następne okresy rozliczeniowe.-->
										</xsd:element>
										<xsd:element name="P_53" type="etd:TKwotaCNieujemna" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Wysokość nadwyżki podatku naliczonego nad należnym</xsd:documentation>
											</xsd:annotation>
											<!--Jeżeli P_51 > 0 to P_53 = 0 w przeciwnym wypadku jeżeli (P_48 + P_49 + P_50  + P_52) – P_38 >  0 to P_53 = P_48 - P_38 + P_49 + P_50  + P_52 w pozostałych przypadkach P_53 = 0.-->
										</xsd:element>
										<xsd:sequence minOccurs="0">
											<xsd:element name="P_54" type="etd:TKwotaCNieujemna">
												<xsd:annotation>
													<xsd:documentation>Wysokość nadwyżki podatku naliczonego nad należnym do zwrotu na rachunek wskazany przez podatnika</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:choice>
												<xsd:element name="P_540" type="etd:TWybor1">
													<xsd:annotation>
														<xsd:documentation>Zwrot na rachunek rozliczeniowy podatnika w terminie 15 dni: 1 - tak</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="P_55" type="etd:TWybor1">
													<xsd:annotation>
														<xsd:documentation>Zwrot na rachunek VAT podatnika w terminie 25 dni: 1 - tak</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="P_56" type="etd:TWybor1">
													<xsd:annotation>
														<xsd:documentation>Zwrot na rachunek rozliczeniowy podatnika w terminie 25 dni (art. 87 ust. 6 ustawy): 1 - tak</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="P_560" type="etd:TWybor1">
													<xsd:annotation>
														<xsd:documentation>Zwrot na rachunek rozliczeniowy podatnika w terminie 40 dni: 1 - tak</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="P_57" type="etd:TWybor1">
													<xsd:annotation>
														<xsd:documentation>Zwrot na rachunek rozliczeniowy podatnika w terminie 60 dni: 1 - tak</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="P_58" type="etd:TWybor1">
													<xsd:annotation>
														<xsd:documentation>Zwrot na rachunek rozliczeniowy podatnika w terminie 180 dni: 1 - tak</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
											</xsd:choice>
											<xsd:sequence minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Podatnik wnosi o zaliczenie zwrotu podatku na poczet przyszłych zobowiązań podatkowych, zgodnie z art. 76 § 1 i art. 76b § 1 ustawy z dnia 29 sierpnia 1997 r. - Ordynacja podatkowa (Dz.U. z 2021 r. poz. 1540, z późn. zm.), wraz z podaniem wysokości zaliczenia oraz rodzaju zobowiązania podatkowego</xsd:documentation>
												</xsd:annotation>
												<xsd:element name="P_59" type="etd:TWybor1">
													<xsd:annotation>
														<xsd:documentation>Zaliczenie zwrotu podatku na poczet przyszłych zobowiązań podatkowych: 1 - tak</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="P_60">
													<xsd:annotation>
														<xsd:documentation>Wysokość zwrotu do zaliczenia na poczet przyszłych zobowiązań podatkowych</xsd:documentation>
													</xsd:annotation>
													<xsd:simpleType>
														<xsd:restriction base="etd:TKwotaCNieujemna">
															<xsd:minExclusive value="0"/>
														</xsd:restriction>
													</xsd:simpleType>
												</xsd:element>
												<xsd:element name="P_61" type="etd:TZnakowy">
													<xsd:annotation>
														<xsd:documentation>Rodzaj przyszłego zobowiązania podatkowego</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
											</xsd:sequence>
										</xsd:sequence>
										<xsd:element name="P_62" type="etd:TKwotaCNieujemna" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Wysokość nadwyżki podatku naliczonego nad należnym do przeniesienia na następny okres rozliczeniowy</xsd:documentation>
											</xsd:annotation>
											<!--Od kwoty wykazanej w P_53 należy odjąć kwotę z P_54.-->
										</xsd:element>
										<xsd:element name="P_63" type="etd:TWybor1" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Podatnik wykonywał w okresie rozliczeniowym czynności, o których mowa w art. 119 ustawy: 1 - tak</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="P_64" type="etd:TWybor1" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Podatnik wykonywał w okresie rozliczeniowym czynności, o których mowa w art. 120 ust. 4 lub 5 ustawy: 1 - tak</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="P_65" type="etd:TWybor1" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Podatnik wykonywał w okresie rozliczeniowym czynności, o których mowa w art. 122 ustawy: 1 - tak</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="P_66" type="etd:TWybor1" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Podatnik wykonywał w okresie rozliczeniowym czynności, o których mowa w art. 136 ustawy: 1 - tak</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="P_660" type="etd:TWybor1" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Podatnik ułatwiał w okresie rozliczeniowym dokonanie czynności, o których mowa w art. 109b ust. 4 ustawy: 1 - tak</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="P_67" type="etd:TWybor1" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Podatnik korzysta z obniżenia zobowiązania podatkowego, o którym mowa w art. 108d ustawy: 1 - tak</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:sequence minOccurs="0">
											<xsd:element name="P_68">
												<xsd:annotation>
													<xsd:documentation>Wysokość korekty podstawy opodatkowania, o której mowa w art. 89a ust. 1 ustawy</xsd:documentation>
												</xsd:annotation>
												<xsd:simpleType>
													<xsd:restriction base="etd:TKwotaC">
														<xsd:maxInclusive value="0"/>
													</xsd:restriction>
												</xsd:simpleType>
											</xsd:element>
											<xsd:element name="P_69">
												<xsd:annotation>
													<xsd:documentation>Wysokość korekty podatku należnego, o której mowa w art. 89a ust. 1 ustawy</xsd:documentation>
												</xsd:annotation>
												<xsd:simpleType>
													<xsd:restriction base="etd:TKwotaC">
														<xsd:maxInclusive value="0"/>
													</xsd:restriction>
												</xsd:simpleType>
											</xsd:element>
										</xsd:sequence>
										<xsd:element name="P_ORDZU" type="etd:TTekstowy" minOccurs="0">
											<xsd:annotation>
												<xsd:documentation>Uzasadnienie przyczyn złożenia korekty</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
									</xsd:sequence>
								</xsd:complexType>
							</xsd:element>
							<xsd:element name="Pouczenia">
								<xsd:annotation>
									<xsd:documentation>Wartość 1 oznacza potwierdzenie zapoznania się z treścią i akceptację poniższych pouczeń:
W przypadku niewpłacenia w obowiązującym terminie podatku podlegającego wpłacie do urzędu skarbowego lub wpłacenia go w niepełnej wysokości niniejsza deklaracja stanowi podstawę do wystawienia tytułu wykonawczego zgodnie z przepisami o postępowaniu egzekucyjnym w administracji.

Za podanie nieprawdy lub zatajenie prawdy i przez to narażenie podatku na uszczuplenie grozi odpowiedzialność przewidziana w przepisach Kodeksu karnego skarbowego.</xsd:documentation>
								</xsd:annotation>
								<xsd:simpleType>
									<xsd:restriction base="etd:TKwota2Nieujemna">
										<xsd:minExclusive value="0"/>
										<xsd:maxExclusive value="2"/>
										<xsd:fractionDigits value="0"/>
									</xsd:restriction>
								</xsd:simpleType>
							</xsd:element>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
				<xsd:element name="Ewidencja" minOccurs="0">
					<xsd:annotation>
						<xsd:documentation>Ewidencja zawierająca dane pozwalające na prawidłowe rozliczenie podatku należnego i podatku naliczonego</xsd:documentation>
					</xsd:annotation>
					<xsd:complexType>
						<xsd:sequence>
							<xsd:sequence>
								<xsd:annotation>
									<xsd:documentation>Ewidencja zawierająca dane pozwalające na prawidłowe rozliczenie podatku należnego</xsd:documentation>
								</xsd:annotation>
								<xsd:element name="SprzedazWiersz" minOccurs="0" maxOccurs="unbounded">
									<xsd:complexType>
										<xsd:sequence>
											<xsd:element name="LpSprzedazy" type="tns:TNaturalnyJPK">
												<xsd:annotation>
													<xsd:documentation>Lp. wiersza ewidencji w zakresie rozliczenia podatku należnego</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:sequence>
												<xsd:element name="KodKrajuNadaniaTIN" type="tns:TKodKrajuJPK" minOccurs="0">
													<xsd:annotation>
														<xsd:documentation>Kod kraju nadania numeru, za pomocą którego nabywca, dostawca lub usługodawca jest zidentyfikowany na potrzeby podatku lub podatku od wartości dodanej</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="NrKontrahenta" type="etd:TNrIdentyfikacjiPodatkowej">
													<xsd:annotation>
														<xsd:documentation>Numer, za pomocą którego nabywca, dostawca lub usługodawca jest zidentyfikowany na potrzeby podatku lub podatku od wartości dodanej (wyłącznie kod cyfrowo-literowy)</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
											</xsd:sequence>
											<xsd:element name="NazwaKontrahenta" type="tns:TZnakowyJPK">
												<xsd:annotation>
													<xsd:documentation>Imię i nazwisko lub nazwa nabywcy, dostawcy lub usługodawcy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="DowodSprzedazy" type="tns:TZnakowyJPK">
												<xsd:annotation>
													<xsd:documentation>Numer dowodu</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="DataWystawienia" type="tns:TDataT">
												<xsd:annotation>
													<xsd:documentation>Data wystawienia dowodu</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="DataSprzedazy" type="tns:TDataT" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Data dokonania lub zakończenia dostawy towarów lub wykonania usługi lub data otrzymania zapłaty, o której mowa w art. 106b ust. 1 pkt 4 ustawy, o ile taka data jest określona i różni się od daty wystawienia dowodu. W przeciwnym przypadku - pole puste</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="TypDokumentu" type="tns:TDowoduSprzedazy" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Oznaczenie dowodu sprzedaży</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:sequence minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Oznaczenie dotyczące dostawy towarów i świadczenia usług</xsd:documentation>
												</xsd:annotation>
												<!--Wypełnić zgodnie z § 10 ust. 3 rozporządzenia Ministra Finansów, Inwestycji i Rozwoju z dnia 15 października 2019 r. w sprawie szczegółowego zakresu danych zawartych w deklaracjach podatkowych i w ewidencji w zakresie podatku od towarów i usług.-->
												<xsd:element name="GTU_01" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="GTU_02" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="GTU_03" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="GTU_04" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="GTU_05" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="GTU_06" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="GTU_07" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="GTU_08" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="GTU_09" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="GTU_10" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="GTU_11" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="GTU_12" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="GTU_13" type="etd:TWybor1" minOccurs="0"/>
											</xsd:sequence>
											<xsd:sequence minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Oznaczenia dotyczące procedur</xsd:documentation>
												</xsd:annotation>
												<!--Wypełnić zgodnie z § 10 ust. 4 rozporządzenia Ministra Finansów, Inwestycji i Rozwoju z dnia 15 października 2019 r. w sprawie szczegółowego zakresu danych zawartych w deklaracjach podatkowych i w ewidencji w zakresie podatku od towarów i usług.-->
												<xsd:element name="WSTO_EE" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="IED" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="TP" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="TT_WNT" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="TT_D" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="MR_T" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="MR_UZ" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="I_42" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="I_63" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="B_SPV" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="B_SPV_DOSTAWA" type="etd:TWybor1" minOccurs="0"/>
												<xsd:element name="B_MPV_PROWIZJA" type="etd:TWybor1" minOccurs="0"/>
											</xsd:sequence>
											<xsd:sequence minOccurs="0">
												<xsd:element name="KorektaPodstawyOpodt" type="etd:TWybor1">
													<xsd:annotation>
														<xsd:documentation>Korekta podstawy opodatkowania oraz podatku należnego, o której mowa w art. 89a ust. 1 i 4 ustawy</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:choice>
													<xsd:element name="TerminPlatnosci" type="tns:TDataTP">
														<xsd:annotation>
															<xsd:documentation>Data upływu terminu płatności w przypadku korekt dokonanych zgodnie z art. 89a ust. 1 ustawy</xsd:documentation>
														</xsd:annotation>
													</xsd:element>
													<xsd:element name="DataZaplaty" type="tns:TDataTP">
														<xsd:annotation>
															<xsd:documentation>Data uregulowania lub zbycia należności w przypadku korekty dokonanej zgodnie z art. 89a ust. 4 ustawy</xsd:documentation>
														</xsd:annotation>
													</xsd:element>
												</xsd:choice>
											</xsd:sequence>
											<xsd:element name="K_10" type="tns:TKwotowy" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania wynikająca z dostawy towarów oraz świadczenia usług na terytorium kraju, zwolnionych od podatku</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="K_11" type="tns:TKwotowy" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania wynikająca z dostawy towarów oraz świadczenia usług poza terytorium kraju</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="K_12" type="tns:TKwotowy" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania wynikająca ze świadczenia usług, o których mowa w art. 100 ust. 1 pkt 4 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="K_13" type="tns:TKwotowy" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania wynikająca z dostawy towarów oraz świadczenia usług na terytorium kraju, opodatkowanych stawką 0%</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="K_14" type="tns:TKwotowy" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania wynikająca z dostawy towarów, o której mowa w art. 129 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:sequence minOccurs="0">
												<xsd:element name="K_15" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wysokość podstawy opodatkowania wynikająca z dostawy towarów oraz świadczenia usług na terytorium kraju, opodatkowanych stawką 5%, z uwzględnieniem korekty dokonanej zgodnie z art. 89a ust. 1 i 4 ustawy</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="K_16" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wysokość podatku należnego wynikająca z dostawy towarów oraz świadczenia usług na terytorium kraju, opodatkowanych stawką 5%, z uwzględnieniem korekty dokonanej zgodnie z art. 89a ust. 1 i 4 ustawy</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
											</xsd:sequence>
											<xsd:sequence minOccurs="0">
												<xsd:element name="K_17" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wysokość podstawy opodatkowania wynikająca z dostawy towarów oraz świadczenia usług na terytorium kraju, opodatkowanych stawką 7% albo 8%, z uwzględnieniem korekty dokonanej zgodnie z art. 89a ust. 1 i 4 ustawy</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="K_18" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wysokość podatku należnego wynikająca z dostawy towarów oraz świadczenia usług na terytorium kraju, opodatkowanych stawką 7% albo 8%, z uwzględnieniem korekty dokonanej zgodnie z art. 89a ust. 1 i 4 ustawy</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
											</xsd:sequence>
											<xsd:sequence minOccurs="0">
												<xsd:element name="K_19" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wysokość podstawy opodatkowania wynikająca z dostawy towarów oraz świadczenia usług na terytorium kraju, opodatkowanych stawką 22% albo 23%, z uwzględnieniem korekty dokonanej zgodnie z art. 89a ust. 1 i 4 ustawy</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="K_20" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wysokość podatku należnego wynikająca z dostawy towarów oraz świadczenia usług na terytorium kraju, opodatkowanych stawką 22% albo 23%, z uwzględnieniem korekty dokonanej zgodnie z art. 89a ust. 1 i 4 ustawy</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
											</xsd:sequence>
											<xsd:element name="K_21" type="tns:TKwotowy" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania wynikająca z wewnątrzwspólnotowej dostawy towarów, o której mowa w art. 13 ust. 1 i 3 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="K_22" type="tns:TKwotowy" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Wysokość podstawy opodatkowania wynikająca z eksportu towarów</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:sequence minOccurs="0">
												<xsd:element name="K_23" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wysokość podstawy opodatkowania wynikająca z wewnątrzwspólnotowego nabycia towarów</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="K_24" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wysokość podatku należnego wynikająca z wewnątrzwspólnotowego nabycia towarów</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
											</xsd:sequence>
											<xsd:sequence minOccurs="0">
												<xsd:element name="K_25" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wysokość podstawy opodatkowania wynikająca z importu towarów rozliczanego zgodnie z art. 33a ustawy, potwierdzona zgłoszeniem celnym lub deklaracją importową, o której mowa w art. 33b ustawy</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="K_26" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wysokość podatku należnego wynikająca z importu towarów rozliczanego zgodnie z art. 33a ustawy, potwierdzona zgłoszeniem celnym lub deklaracją importową, o której mowa w art. 33b ustawy</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
											</xsd:sequence>
											<xsd:sequence minOccurs="0">
												<xsd:element name="K_27" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wysokość podstawy opodatkowania wynikająca z importu usług, z wyłączeniem usług nabywanych od podatników podatku od wartości dodanej, do których stosuje się art. 28b ustawy</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="K_28" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wysokość podatku należnego wynikająca z importu usług, z wyłączeniem usług nabywanych od podatników podatku od wartości dodanej, do których stosuje się art. 28b ustawy</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
											</xsd:sequence>
											<xsd:sequence minOccurs="0">
												<xsd:element name="K_29" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wysokość podstawy opodatkowania wynikająca z importu usług nabywanych od podatników podatku od wartości dodanej, do których stosuje się art. 28b ustawy</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="K_30" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wysokość podatku należnego wynikająca z importu usług nabywanych od podatników podatku od wartości dodanej, do których stosuje się art. 28b ustawy</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
											</xsd:sequence>
											<xsd:sequence minOccurs="0">
												<xsd:element name="K_31" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wysokość podstawy opodatkowania wynikająca z dostawy towarów, dla których podatnikiem jest nabywca zgodnie z art. 17 ust. 1 pkt 5 ustawy</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="K_32" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wysokość podatku należnego wynikająca z dostawy towarów, dla których podatnikiem jest nabywca zgodnie z art. 17 ust. 1 pkt 5 ustawy</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
											</xsd:sequence>
											<xsd:element name="K_33" type="tns:TKwotowy" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Wysokość podatku należnego od towarów objętych spisem z natury, o którym mowa w art. 14 ust. 5 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="K_34" type="tns:TKwotowy" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Wysokość zwrotu odliczonej lub zwróconej kwoty wydanej na zakup kas rejestrujących, o którym mowa w art. 111 ust. 6 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="K_35" type="tns:TKwotowy" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Wysokość podatku należnego od wewnątrzwspólnotowego nabycia środków transportu, wykazana w wysokości podatku należnego z tytułu wewnątrzwspólnotowego nabycia towarów, podlegająca wpłacie w terminie, o którym mowa w art. 103 ust. 3, w związku z ust. 4 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="K_36" type="tns:TKwotowy" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Wysokość podatku należnego od wewnątrzwspólnotowego nabycia towarów, o których mowa w art. 103 ust. 5aa ustawy, podlegająca wpłacie w terminie, o którym mowa w art. 103 ust. 5a i 5b ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="SprzedazVAT_Marza" type="tns:TKwotowy" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Wartość sprzedaży brutto dostawy towarów i świadczenia usług opodatkowanych na zasadach marży zgodnie z art. 119 i art. 120 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
										</xsd:sequence>
									</xsd:complexType>
								</xsd:element>
								<xsd:element name="SprzedazCtrl">
									<xsd:annotation>
										<xsd:documentation>Sumy kontrolne dla ewidencji w zakresie rozliczenia podatku należnego</xsd:documentation>
									</xsd:annotation>
									<xsd:complexType>
										<xsd:sequence>
											<xsd:element name="LiczbaWierszySprzedazy" type="etd:TNaturalny">
												<xsd:annotation>
													<xsd:documentation>Liczba wierszy ewidencji w zakresie rozliczenia podatku należnego, w okresie którego dotyczy JPK. Jeżeli ewidencja nie zawiera wierszy należy wykazać 0</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="PodatekNalezny" type="tns:TKwotowy">
												<xsd:annotation>
													<xsd:documentation>Podatek należny według ewidencji w okresie, którego dotyczy JPK - suma kwot z K_16, K_18, K_20, K_24, K_26, K_28, K_30, K_32, K_33 i K_34 pomniejszona o kwotę z K_35 i K_36, z wyłączeniem faktur, o których mowa w art. 109 ust. 3d ustawy (oznaczonych FP). Jeżeli w ewidencji nie wypełniono żadnego ze wskazanych elementów, wówczas należy wykazać 0.00</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
										</xsd:sequence>
									</xsd:complexType>
								</xsd:element>
							</xsd:sequence>
							<xsd:sequence>
								<xsd:annotation>
									<xsd:documentation>Ewidencja zawierająca dane pozwalające na prawidłowe rozliczenie podatku naliczonego</xsd:documentation>
								</xsd:annotation>
								<xsd:element name="ZakupWiersz" minOccurs="0" maxOccurs="unbounded">
									<xsd:complexType>
										<xsd:sequence>
											<xsd:element name="LpZakupu" type="tns:TNaturalnyJPK">
												<xsd:annotation>
													<xsd:documentation>Lp. wiersza ewidencji w zakresie rozliczenia podatku naliczonego</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:sequence>
												<xsd:element name="KodKrajuNadaniaTIN" type="tns:TKodKrajuJPK" minOccurs="0">
													<xsd:annotation>
														<xsd:documentation>Kod kraju nadania numeru, za pomocą którego dostawca lub usługodawca jest zidentyfikowany na potrzeby podatku lub podatku od wartości dodanej</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="NrDostawcy" type="etd:TNrIdentyfikacjiPodatkowej">
													<xsd:annotation>
														<xsd:documentation>Numer, za pomocą którego dostawca lub usługodawca jest zidentyfikowany na potrzeby podatku lub podatku od wartości dodanej (wyłącznie kod cyfrowo-literowy)</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
											</xsd:sequence>
											<xsd:element name="NazwaDostawcy" type="tns:TZnakowyJPK">
												<xsd:annotation>
													<xsd:documentation>Imię i nazwisko lub nazwa dostawcy lub usługodawcy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="DowodZakupu" type="tns:TZnakowyJPK">
												<xsd:annotation>
													<xsd:documentation>Numer dowodu zakupu</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="DataZakupu" type="tns:TDataT">
												<xsd:annotation>
													<xsd:documentation>Data wystawienia dowodu zakupu</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="DataWplywu" type="tns:TDataT" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Data wpływu dowodu zakupu</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="DokumentZakupu" type="tns:TDowoduZakupu" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Oznaczenie dowodu zakupu</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="IMP" type="etd:TWybor1" minOccurs="0">
												<!--Wypełnić zgodnie z § 11 ust. 2 pkt 1 rozporządzenia Ministra Finansów, Inwestycji i Rozwoju z dnia 15 października 2019 r. w sprawie szczegółowego zakresu danych zawartych w deklaracjach podatkowych i w ewidencji w zakresie podatku od towarów i usług.-->
											</xsd:element>
											<xsd:sequence minOccurs="0">
												<xsd:element name="K_40" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wartość netto wynikająca z nabycia towarów i usług zaliczanych u podatnika do środków trwałych</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="K_41" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wysokość podatku naliczonego przysługująca do odliczenia z podstaw określonych w art. 86 ust. 2 ustawy, na warunkach określonych w ustawie wynikająca z nabycia towarów i usług zaliczanych u podatnika do środków trwałych</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
											</xsd:sequence>
											<xsd:sequence minOccurs="0">
												<xsd:element name="K_42" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wartość netto wynikająca z nabycia pozostałych towarów i usług</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="K_43" type="tns:TKwotowy">
													<xsd:annotation>
														<xsd:documentation>Wysokość podatku naliczonego przysługująca do odliczenia z podstaw określonych w art. 86 ust. 2 ustawy, na warunkach określonych w ustawie wynikająca z nabycia pozostałych towarów i usług</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
											</xsd:sequence>
											<xsd:element name="K_44" type="tns:TKwotowy" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Wysokość podatku naliczonego wynikająca z korekt podatku naliczonego, o których mowa w art. 90a-90c oraz art. 91 ustawy, z tytułu nabycia towarów i usług zaliczanych u podatnika do środków trwałych</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="K_45" type="tns:TKwotowy" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Wysokość podatku naliczonego wynikająca z korekt podatku naliczonego, o których mowa w art. 90a-90c oraz art. 91 ustawy, z tytułu nabycia pozostałych towarów i usług</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="K_46" type="tns:TKwotowy" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Wysokość podatku naliczonego wynikająca z korekty podatku naliczonego, o której mowa w art. 89b ust. 1 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="K_47" type="tns:TKwotowy" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Wysokość podatku naliczonego wynikająca z korekty podatku naliczonego, o której mowa w art. 89b ust. 4 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="ZakupVAT_Marza" type="tns:TKwotowy" minOccurs="0">
												<xsd:annotation>
													<xsd:documentation>Kwota nabycia towarów i usług nabytych od innych podatników dla bezpośredniej korzyści turysty, a także nabycia towarów używanych, dzieł sztuki, przedmiotów kolekcjonerskich i antyków związanych ze sprzedażą opodatkowaną na zasadzie marży zgodnie z art. 120 ustawy</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
										</xsd:sequence>
									</xsd:complexType>
								</xsd:element>
								<xsd:element name="ZakupCtrl">
									<xsd:annotation>
										<xsd:documentation>Sumy kontrolne dla ewidencji w zakresie rozliczenia podatku naliczonego</xsd:documentation>
									</xsd:annotation>
									<xsd:complexType>
										<xsd:sequence>
											<xsd:element name="LiczbaWierszyZakupow" type="etd:TNaturalny">
												<xsd:annotation>
													<xsd:documentation>Liczba wierszy ewidencji w zakresie rozliczenia podatku naliczonego, w okresie którego dotyczy JPK. Jeżeli ewidencja nie zawiera wierszy należy wykazać 0</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
											<xsd:element name="PodatekNaliczony" type="tns:TKwotowy">
												<xsd:annotation>
													<xsd:documentation>Razem kwota podatku naliczonego do odliczenia - suma kwot z K_41, K_43, K_44, K_45, K_46, K_47. Jeżeli w ewidencji nie wypełniono żadnego ze wskazanych elementów, wówczas należy wykazać 0.00</xsd:documentation>
												</xsd:annotation>
											</xsd:element>
										</xsd:sequence>
									</xsd:complexType>
								</xsd:element>
							</xsd:sequence>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>