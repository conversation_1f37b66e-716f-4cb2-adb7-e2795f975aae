<?php

namespace XSoft\JPK\Objects;

use XSoft\JPK\Objects\StructOsoba;
use XSoft\JPK\Objects\V7M\Deklaracja;
use XSoft\JPK\Objects\V7M\Ewidencja;
use XSoft\JPK\Objects\V7M\Podmiot;
use XSoft\JPK\Objects\V7M\Naglowek;

class JPKV7M
{
    protected string $version = "1.0";
    protected string $encoding = "utf-8";

    protected string $namespace = "";

    public bool $is_ewidencja = true;
    public bool $is_deklaracja = false;

    protected $JPK_attributes = [
//        'xsi:schemaLocation' => "http://crd.gov.pl/wzor/2021/12/27/11148/schemat.xsd",
        'xmlns' => "http://crd.gov.pl/wzor/2021/12/27/11148/",
        'xmlns:xsi' => "http://www.w3.org/2001/XMLSchema-instance",
        "xmlns:etd" => "http://crd.gov.pl/xml/schematy/dziedzinowe/mf/2021/06/08/eD/DefinicjeTypy/"
    ];

    protected Naglowek $naglowek;
    protected Podmiot $podatnik;

    protected Ewidencja $ewidencja;
    protected Deklaracja $deklaracja;
    protected $podmioty = [];

    public function setNaglowek(Naglowek $naglowek)
    {
        $this->naglowek = $naglowek;
    }

    public function setPodatnik(StructOsoba $osoba)
    {
        $podmiot = new Podmiot();
        $podmiot->setAttributeRola('Podatnik');
        $podmiot->setAttributeId('1');
        $podmiot->setOsoba($osoba);
        $this->podatnik = $podmiot;
    }

    public function setEwidencja(Ewidencja $ewidencja)
    {
        $this->ewidencja = $ewidencja;
    }


    /**
     * @return false|string
     * @throws \DOMException
     */
    public function getXml()
    {
        $doc = new \DOMDocument("1.0", "utf-8");
        $jpk = $doc->createElement($this->namespace . 'JPK');
        foreach ($this->JPK_attributes as $name => $value) {
            $jpk->setAttribute($name, $value);
        }

        $naglowek = $this->naglowek->getXML($doc, $jpk);
        $podatnik = $this->podatnik->getXML($doc, $jpk);
        if ($this->is_deklaracja) {
            $this->deklaracja = new Deklaracja();
            $sprzedaz = $this->ewidencja->getSprzedaz();

            echo 'Sprzedaz: ' . PHP_EOL;
            $this->deklaracja->calcPositionsP($sprzedaz);
            $zakup = $this->ewidencja->getZakupy();
            echo 'Zakup:' . PHP_EOL;
            $this->deklaracja->calcPositionsP($zakup);

            $this->deklaracja->sumPositions();
            $deklaracja = $this->deklaracja->getXML($doc, $jpk);
        }
        $ewidencja = $this->ewidencja->getXML($doc, $jpk);
        $jpk->appendChild($naglowek);
        $jpk->appendChild($podatnik);
        if ($this->is_deklaracja) {
            $jpk->appendChild($deklaracja);
        }
        $jpk->appendChild($ewidencja);
        $doc->appendChild($jpk);
        $doc->formatOutput = true;
        return $doc->saveXML();
    }
}
