<?php

namespace XSoft\JPK\Objects\V7M;

use XSoft\JPK\Objects\StructOsoba;

class PodmiotOsobaFizyczna implements StructOsoba
{

    protected string $type = 'OsobaFizyczna';

    protected $attributes = [];
    protected $values = [
        'etd:NIP' => '',
        'etd:ImiePierwsze' => '',
        'etd:Nazwisko' => '',
        'etd:DataUrodzenia' => '',
        'Email' => '',
        'Telefon' => '',
    ];

    protected $error_message_prefix = "Podmiot, OsobaFizyczna ";

    protected $error_messages = [];

    protected $required = [
        'etd:NIP' => 'Pole NIP jest wymagane',
        'etd:ImiePierwsze' => 'Pole Imię jest wymagane',
        'etd:Nazwisko' => 'Pole Nazwisko jest wymagane',
        'etd:DataUrodzenia' => 'Pole data urodzenia jest wymagane',
        'Email' => 'Pole email jest wymagane'
    ];


    public function setNIP(string $nip): void
    {
        $this->values['etd:NIP'] = $nip;
    }

    public function setImiePierwsze(string $imie): void
    {
        $this->values['etd:ImiePierwsze'] = $imie;
    }

    public function setNazwisko(string $nazwisko): void
    {
        $this->values['etd:Nazwisko'] = $nazwisko;
    }

    public function setDataUrodzenia(\DateTimeImmutable $data): void
    {
        $this->values['etd:DataUrodzenia'] = $data->format('Y-m-d');
    }


    public function setEmail(string $email)
    {
        if (false === filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return null;
        }
        $this->values['Email'] = $email;
    }


    public function setTelefon(string $telefon): void
    {
        $this->values['Telefon'] = $telefon;
    }


    /**
     * @param \DOMDocument $root
     * @param \DOMElement $fragment
     * @param bool $append
     * @return \DOMElement
     * @throws \DOMException
     */
    public function getXML(\DOMDocument $root, \DOMElement $fragment, bool $append = false): \DOMElement
    {

        if (!$this->validate()) {
            throw new \Exception('Błąd danych: ' . implode(', ', $this->error_messages), '1010');
        }

        $current_element = $root->createElement('OsobaFizyczna');

        foreach ($this->values as $element => $element_value) {
            if (empty($element_value)) {
                continue;
            }
            $property = $root->createElement($element, $element_value);
            if (array_key_exists($element, $this->attributes)) {
                foreach ($this->attributes[$element] as $attr_name => $attr_value) {
                    $property->setAttribute($attr_name, $attr_value);
                }
            }
            $current_element->appendChild($property);
        }

        return $current_element;
    }


    public function validate(): bool
    {
        $is_valid = true;
        foreach ($this->values as $key => $val) {
            if (empty($val) && array_key_exists($key, $this->required)) {
                $this->error_messages[] = $this->error_message_prefix . ': ' . $this->required[$key];
                $is_valid = false;
            }
        }

        return $is_valid;
    }

    public function setType(string $type): void
    {
        $this->type = $type;
    }

    public function getType(): string
    {
        return $this->type;
    }
}
