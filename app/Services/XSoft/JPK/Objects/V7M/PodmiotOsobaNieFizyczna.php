<?php

namespace XSoft\JPK\Objects\V7M;

use XSoft\JPK\Objects\StructOsoba;

class PodmiotOsobaNieFizyczna implements StructOsoba
{

    protected string $type = 'OsobaNieFizyczna';

    protected $attributes = [];
    protected $values = [
        'NIP' => '',
        'PelnaNazwa' => '',
        'Email' => '',
        'Telefon' => '',
    ];

    protected $error_message_prefix = "Podmiot, OsobaNieFizyczna ";

    protected $error_messages = [];

    protected $required = [
        'NIP' => 'Pole NIP jest wymagane',
        'PelnaNazwa' => 'Pole Nazwa jest wymagane',
        'Email' => 'Pole Email jest wymagane'
    ];

    protected $namespace = "";


    public function setNIP(string $nip): void
    {
        $this->values['NIP'] = $nip;
    }

    public function setPelnaNazwa(string $nazwa): void
    {
        $this->values['<PERSON>elna<PERSON>azwa'] = $nazwa;
    }


    public function setEmail(string $email)
    {
        if (false === filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return null;
        }
        $this->values['Email'] = $email;
    }


    public function setTelefon(string $telefon): void
    {
        $this->values['Telefon'] = $telefon;
    }


    /**
     * @param \DOMDocument $root
     * @param \DOMElement $fragment
     * @param bool $append
     * @return \DOMElement
     * @throws \DOMException
     */
    public function getXML(\DOMDocument $root, \DOMElement $fragment, bool $append = false): \DOMElement
    {

        if (!$this->validate()) {
            throw new \Exception('Błąd danych ' . __CLASS__ . ': ' . implode(', ', $this->error_messages), '1010');
        }

        $current_element = $root->createElement($this->namespace . 'OsobaNieFizyczna');

        foreach ($this->values as $element => $element_value) {
            if (empty($element_value)) {
                continue;
            }
            $property = $root->createElement($element, $element_value);
            if (array_key_exists($element, $this->attributes)) {
                foreach ($this->attributes[$element] as $attr_name => $attr_value) {
                    $property->setAttribute($attr_name, $attr_value);
                }
            }
            $current_element->appendChild($property);
        }

        return $current_element;
    }


    public function validate(): bool
    {
        $is_valid = true;
        foreach ($this->values as $key => $val) {
            if (empty($val) && array_key_exists($key, $this->required)) {
                $this->error_messages[] = $this->error_message_prefix . ': ' . $this->required[$key];
                $is_valid = false;
            }
        }

        return $is_valid;
    }

    public function setType(string $type): void
    {
        $this->type = $type;
    }

    public function getType(): string
    {
        return $this->type;
    }
}
