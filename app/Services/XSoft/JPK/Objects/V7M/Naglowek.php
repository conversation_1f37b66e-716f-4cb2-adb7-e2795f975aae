<?php

namespace XSoft\JPK\Objects\V7M;

class Naglowek
{
    protected array $attributes = [
        'KodFormularza' => [
            "kodSystemowy" => "JPK_V7M (2)",
            "wersjaSchemy" => "1-0E"
        ],
        "CelZlozenia" => [
            'poz' => 'P_7'
        ]
    ];

    protected array $values = [
        "KodFormularza" => "JPK_VAT",
        "WariantFormularza" => 2,
        "DataWytworzeniaJPK" => '',
        "NazwaSystemu" => 'JPKGen',
        "CelZlozenia" => 1,
        "KodUrzedu" => 2813,
        "Rok" => 2023,
        "Miesiac" => 1,
    ];

    protected $namespace = "";


    public function setNazwaSystemu(string $nazwa): void
    {
        $this->values['NazwaSystemu'] = $nazwa;
    }

    public function setCelZlozenia(int $cel): void
    {
        $this->values['CelZlozenia'] = $cel;
    }

    public function setKodUrzedu(int $kod): void
    {
        $this->values['KodUrzedu'] = $kod;
    }


    public function setRok(int $rok): void
    {
        $this->values['Rok'] = $rok;
    }

    public function setMiesiac(int $miesiac): void
    {
        $this->values['Miesiac'] = $miesiac;
    }

    public function setDataWytworzeniaFormularza(\DateTimeImmutable $date = null): void
    {
        $date = $date ?? new \DateTimeImmutable("now", new \DateTimeZone('Europe/Warsaw'));
        $this->values['DataWytworzeniaJPK'] = $date->format('Y-m-d\TH:i:s');
    }

    public function getXML(\DOMDocument $root, \DOMElement $fragment, $append = false): \DOMElement
    {
        $naglowek = $root->createElement($this->namespace . 'Naglowek');
        if ($this->values['DataWytworzeniaJPK'] === '') {
            $this->setDataWytworzeniaFormularza();
        }
        foreach ($this->values as $element => $element_value) {
            $property = $root->createElement($this->namespace . $element, $element_value);
            if (array_key_exists($element, $this->attributes)) {
                foreach ($this->attributes[$element] as $attr_name => $attr_value) {
                    $property->setAttribute($attr_name, $attr_value);
                }
            }
            $naglowek->appendChild($property);
        }

        return $naglowek;
    }
}
