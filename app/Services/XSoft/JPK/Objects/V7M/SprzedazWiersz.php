<?php

namespace XSoft\JPK\Objects\V7M;

use Illuminate\Support\Facades\Log;

class SprzedazWiersz
{
    protected int $lp_sprzedazy = 0;

    protected array $values = [
        'KodKrajuNadaniaTIN' => '',
        'NrKontrahenta' => '',
        'NazwaKontrahenta' => '',
        'DowodSprzedazy' => '',
        'DataWystawienia' => '',
        'DataSprzedazy' => '',
        'TypDokumentu' => '',
        'K_10' => '',
        'K_11' => '',
        'K_12' => '',
        'K_13' => '',
        'K_14' => '',
        'K_15' => '',
        'K_16' => '',
        'K_17' => '',
        'K_18' => '',
        'K_19' => '',
        'K_20' => '',
        'K_21' => '',
        'K_22' => '',
        'K_23' => '',
        'K_24' => '',
        'K_25' => '',
        'K_26' => '',
        'K_27' => '',
        'K_28' => '',
        'K_29' => '',
        'K_30' => '',
        'K_31' => '',
        'K_32' => '',
        'K_33' => '',
        'K_34' => '',
        'K_35' => '',
        'K_36' => '',
        'SprzedazVat_Marza' => '',
    ];


    public function setKodKrajuNadania($kod): void
    {
        $this->values['KodKrajuNadaniaTIN'] = $kod;
    }


    public function setNumerKontrahenta($value): void
    {
        $this->values['NrKontrahenta'] = $value;
    }


    public function setNazwaKontrahenta($value): void
    {
        $this->values['NazwaKontrahenta'] = $value;
    }


    public function setDowodSprzedazy($value): void
    {
        $this->values['DowodSprzedazy'] = $value;
    }


    public function setDataWystawienia($value): void
    {
        $this->values['DataWystawienia'] = $value;
    }


    public function setDataSprzedazy($value): void
    {
        $this->values['DataSprzedazy'] = $value;
    }


    public function setVatZwolnionyK10($netto): void
    {
        $this->values['K_10'] = round($netto, 2);
    }


    public function setValueK11($value): void
    {
        $this->values['K_11'] = round($value, 2);
    }


    public function setValueK12($value): void
    {
        $this->values['K_12'] = round($value, 2);
    }


    public function setKFieldValue($wartosc, $pole): void
    {
        $this->values['' . $pole] = $wartosc;
    }


    public function setWPOVat0K13($value): void
    {
        $this->values['K_13'] = round($value, 2);
    }

    public function setWPOVat0K14($value): void
    {
        $this->values['K_14'] = round($value, 2);
    }


    public function setVat5procK15($netto, $wartosc_podatku): void
    {
        $this->values['K_15'] = round($netto, 2);
        $this->values['K_16'] = round($wartosc_podatku, 2);
    }


    public function setVat7procK17($netto, $wartosc_podatku): void
    {
        $this->values['K_17'] = round($netto, 2);
        $this->values['K_18'] = round($wartosc_podatku, 2);
    }


    public function setVat23procK19($netto, $wartosc_podatku): void
    {
        $this->values['K_19'] = round($netto, 2);
        $this->values['K_20'] = round($wartosc_podatku, 2);
    }


    public function setWPOK21($podstawa): void
    {
        $this->values['K_21'] = round($podstawa, 2);
    }


    public function setWPOK22($podstawa): void
    {
        $this->values['K_22'] = round($podstawa, 2);
    }

    public function setVatWNTK23($netto, $wartosc_podatku): void
    {
        $this->values['K_23'] = round($netto, 2);
        $this->values['K_24'] = round($wartosc_podatku, 2);
    }

    public function setVatImpTowK25($netto, $wartosc_podatku): void
    {
        $this->values['K_25'] = round($netto, 2);
        $this->values['K_26'] = round($wartosc_podatku, 2);
    }


    public function setVatImpUslK27($netto, $wartosc_podatku): void
    {
        $this->values['K_27'] = round($netto, 2);
        $this->values['K_28'] = round($wartosc_podatku, 2);
    }


    public function setVatWIUK29($netto, $wartosc_podatku): void
    {
        $this->values['K_29'] = round($netto, 2);
        $this->values['K_30'] = round($wartosc_podatku, 2);
    }

    public function setLPSprzedazy(int $value): void
    {
        $this->lp_sprzedazy = $value;
    }

    public function getValues(): array
    {
        return $this->values;
    }

    /**
     * @param \DOMDocument $root
     * @param \DOMElement $fragment
     * @param bool $append
     * @return \DOMElement
     * @throws \Exception
     */
    public function getXML(\DOMDocument $root, \DOMElement $fragment, bool $append = false): \DOMElement
    {
        $c_element = $root->createElement('SprzedazWiersz');
        $lp = $root->createElement('LpSprzedazy', $this->lp_sprzedazy);
        $c_element->appendChild($lp);
        foreach ($this->values as $name => $value) {
            if (empty($value)) {
                continue;
            }
            try {
                $property = $root->createElement(
                    $name,
                    htmlspecialchars(str_replace(["\n", "\r", "\r\n"], ' ', (string)$value))
                );
            } catch (\Exception $e) {
                throw new \Exception('JPK SprzedazWiersz. Name: ' . $name . ', value: ' .  $value);
            }

            $c_element->appendChild($property);
        }
        return $c_element;
    }
}
