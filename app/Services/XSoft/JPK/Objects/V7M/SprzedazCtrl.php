<?php

namespace XSoft\JPK\Objects\V7M;

use XSoft\JPK\Objects\V7M\SprzedazWiersz;

class SprzedazCtrl
{
    protected int $liczba_wierszy = 0;
    protected int $podatek_nalezny = 0;

    private array $targetFieldsSUM = [
        'K_16',
        'K_18',
        'K_20',
        'K_24',
        'K_26',
        'K_28',
        'K_30',
        'K_32',
        'K_33',
        'K_34',
    ];

    private array $targetFieldsREDUCE = [
        'K_35',
        'K_36'
    ];

    /**
     * @param array<SprzedazWiersz> $sprzedaz
     * @return void
     */
    public function podliczSprzedaz(array $sprzedaz): void
    {
        $sum = 0;
        $reduce = 0;
        foreach ($sprzedaz as $wiersz) {
            $this->liczba_wierszy++;
            $values = $wiersz->getValues();
            foreach ($this->targetFieldsSUM as $fied) {
                if (!empty($values[$fied])) {
                    $sum += (int)($values[$fied] * 100);
                }
            }
            foreach ($this->targetFieldsREDUCE as $field) {
                if (!empty($values[$field])) {
                    $reduce += (int)($values[$fied] * 100);
                }
            }
        }

        $this->podatek_nalezny = $sum - $reduce;
    }

    public function getXML(\DOMDocument $root, \DOMElement $fragment, bool $append = false): \DOMElement
    {
        $current_element = $root->createElement('SprzedazCtrl');
        $liczba_wierszy = $root->createElement('LiczbaWierszySprzedazy', $this->liczba_wierszy);
        $podatek_nalezny = $root->createElement(
            'PodatekNalezny',
            number_format($this->podatek_nalezny / 100, 2, '.', '')
        );

        $current_element->appendChild($liczba_wierszy);
        $current_element->appendChild($podatek_nalezny);
        return $current_element;
    }
}
