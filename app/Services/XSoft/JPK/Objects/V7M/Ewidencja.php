<?php

namespace XSoft\JPK\Objects\V7M;

class Ewidencja
{
    /**
     * @var array<SprzedazWiersz>
     */
    protected array $sprzedaz = [];
    protected SprzedazCtrl $sprzedaz_ctrl;
    /**
     * @var array<ZakupWiersz>
     */
    protected array $zakup = [];
    protected ZakupCtrl $zakup_ctrl;

    private int $counter_sprzedaz = 0;
    private int $counter_zakup = 0;

    public function __construct()
    {
        $this->sprzedaz_ctrl = new SprzedazCtrl();
        $this->zakup_ctrl = new ZakupCtrl();
    }

    public function addSprzedaz(SprzedazWiersz $pozycja): void
    {
        $pozycja->setLPSprzedazy(++$this->counter_sprzedaz);
        $this->sprzedaz[] = $pozycja;
    }

    public function addZakup(ZakupWiersz $pozycja): void
    {
        $pozycja->setLPZakupu(++$this->counter_zakup);
        $this->zakup[] = $pozycja;
    }

    /**
     * @return SprzedazWiersz[]
     */
    public function getSprzedaz(): array
    {
        return $this->sprzedaz;
    }

    /**
     * @return ZakupWiersz[]
     */
    public function getZakupy(): array
    {
        return $this->zakup;
    }

    public function calculateSprzedaz(): void
    {
        $this->sprzedaz_ctrl->podliczSprzedaz($this->sprzedaz);
    }

    public function calculateZakupy(): void
    {
        $this->zakup_ctrl->podliczZakupy($this->zakup);
    }

    public function getXML(\DOMDocument $root, \DOMElement $fragment, bool $append = false): \DOMElement
    {
        $c_element = $root->createElement('Ewidencja');
        $this->calculateSprzedaz();
        $this->calculateZakupy();
        foreach ($this->sprzedaz as $value) {
            $wiersz = $value->getXML($root, $c_element);
            $c_element->appendChild($wiersz);
        }
        $c_element->appendChild($this->sprzedaz_ctrl->getXML($root, $c_element));

        foreach ($this->zakup as $value) {
            $wiersz = $value->getXML($root, $c_element);
            $c_element->appendChild($wiersz);
        }
        $c_element->appendChild($this->zakup_ctrl->getXML($root, $c_element));

        return $c_element;
    }
}
