<?php

namespace XSoft\JPK\Objects\V7M;

use XSoft\JPK\Objects\StructOsoba;

class Podmiot
{
    protected string $type = "";
    protected array $attributes = [
        '_' => [
            "_id" => 1,
            "rola" => "Podatnik"
        ]
    ];

    protected array $values = [];

    protected array $required = [];
    public array $error_messages = [];

    protected StructOsoba $osoba;

    public function setAttributeRola($rola): void
    {
        $this->attributes['_']['rola'] = $rola;
    }

    public function setAttributeId($id): void
    {
        $this->attributes['_']['id'] = $id;
    }

    public function getOsoba(): StructOsoba
    {
        return $this->values[$this->type];
    }

    public function setOsoba(StructOsoba $osoba): void
    {
        $this->values[$osoba->getType()] = $osoba;
        $this->type = $osoba->getType();
    }

    public function getXML(\DOMDocument $root, \DOMElement $fragment, $append = false): \DOMElement
    {
        $el_name = 'Podmiot' . $this->attributes['_']['_id'];
        $current_element = $root->createElement($el_name);
        $current_element->setAttribute('rola', $this->attributes['_']['rola']);
        foreach ($this->values as $element => $element_value) {
            if (empty($element_value)) {
                continue;
            }
            $property = $element_value->getXML($root, $current_element);
            if (array_key_exists($element, $this->attributes)) {
                foreach ($this->attributes[$element] as $attr_name => $attr_value) {
                    $property->setAttribute($attr_name, $attr_value);
                }
            }
            $current_element->appendChild($property);
        }

        return $current_element;
    }
}
