<?php

namespace XSoft\JPK\Objects\V7M;

class ZakupWiersz
{
    protected int $lp_zakupu = 0;

    protected array $values = [
        'KodKrajuNadaniaTIN' => '',
        'NrDostawcy' => '',
        'NazwaDostawcy' => '',
        'DowodZakupu' => '',
        'DataDokumentuZakupu' => '',
        'DataZakupu' => '',
        'DataWplywu' => '',
        'DokumentZakupu' => '',
        'IMP' => '',
        'K_40' => '',
        'K_41' => '',
        'K_42' => '',
        'K_43' => '',
        'K_44' => '',
        'K_45' => '',
        'K_46' => '',
        'K_47' => '',
        'ZakupVat_Marza' => '',
    ];


    public function setKodKrajuNadania($kod): void
    {
        $this->values['KodKrajuNadaniaTIN'] = $kod;
    }

    public function setNrDostawcy($value): void
    {
        $this->values['NrDostawcy'] = $value;
    }

    public function setNazwaDostawcy($value): void
    {
        $this->values['NazwaDostawcy'] = $value;
    }

    public function setDowodZakupu($value): void
    {
        $this->values['DowodZakupu'] = $value;
    }

    public function setDataDokumentuZakupu($value): void
    {
        $this->values['DataDokumentuZakupu'] = $value;
    }

    public function setDataZakupu($value): void
    {
        $this->values['DataZakupu'] = $value;
    }

    public function setDataWplywu($value): void
    {
        $this->values['DataWplywu'] = $value;
    }

    public function setDokumentZakupu($value): void
    {
        $this->values['DokumentZakupu'] = $value;
    }

    public function setSrodkiTrwaleK40($netto, $wartosc_podatku): void
    {
        $this->values['K_40'] = $netto;
        $this->values['K_41'] = $wartosc_podatku;
    }

    public function setKorektaSrodkiTrwaleK44($wartosc_podatku): void
    {
        $this->values['K_44'] = $wartosc_podatku;
    }

    public function setSrodkiPozostaleK42($netto, $wartosc_podatku): void
    {
        $this->values['K_42'] = $netto;
        $this->values['K_43'] = $wartosc_podatku;
    }

    public function setKorektaSrodkiPozostaleK45($wartosc_podatku): void
    {
        $this->values['K_45'] = $wartosc_podatku;
    }

    public function setZakupVatMarza($netto): void
    {
        $this->values['ZakupVat_Marza'] = $netto;
    }


    public function setLPZakupu(int $value): void
    {
        $this->lp_zakupu = $value;
    }

    public function getValues(): array
    {
        return $this->values;
    }

    /**
     * @param \DOMDocument $root
     * @param \DOMElement $fragment
     * @param bool $append
     * @return \DOMElement
     * @throws \Exception
     */
    public function getXML(\DOMDocument $root, \DOMElement $fragment, bool $append = false): \DOMElement
    {
        $c_element = $root->createElement('ZakupWiersz');
        $lp = $root->createElement('LpZakupu', $this->lp_zakupu);
        $c_element->appendChild($lp);
        foreach ($this->values as $name => $value) {
            if (empty($value)) {
                continue;
            }
            try {
                $property = $root->createElement(
                    $name,
                    htmlspecialchars(str_replace(["\n", "\r", "\r\n"], ' ', (string)$value))
                );
            } catch (\Exception $e) {
                throw new \Exception('JPK ZakupWiersz. Name: ' . $name . ', value: ' .  $value);
            }

            $c_element->appendChild($property);
        }
        return $c_element;
    }
}
