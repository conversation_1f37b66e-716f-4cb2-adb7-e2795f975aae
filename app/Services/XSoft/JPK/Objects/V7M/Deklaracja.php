<?php

namespace XSoft\JPK\Objects\V7M;

class Deklaracja
{
    protected array $attributes = [
        'KodFormularzaDekl' => [
            "kodSystemowy" => "VAT-7 (22)",
            "kodPodatku" => "VAT",
            "rodzajZobowiazania" => "Z",
            "wersjaSchemy" => "1-0E"
        ]
    ];

    protected array $values = [
        'KodFormularzaDekl' => "VAT-7",
        "WariantFormularzaDekl" => 22
    ];


    protected array $pozycje = [
        "P_10" => null,
        "P_11" => null,
        "P_12" => null,
        "P_13" => null,
        "P_14" => null,
        "P_15" => null,
        "P_16" => null,
        "P_17" => null,
        "P_18" => null,
        "P_19" => null,
        "P_20" => null,
        "P_21" => null,
        "P_22" => null,
        "P_23" => null,
        "P_24" => null,
        "P_25" => null,
        "P_26" => null,
        "P_27" => null,
        "P_28" => null,
        "P_29" => null,
        "P_30" => null,
        "P_31" => null,
        "P_32" => null,
        "P_33" => null,
        "P_34" => null,
        "P_35" => null,
        "P_36" => null,
        "P_37" => null,
        "P_38" => 0,
        "P_39" => null,
        "P_40" => null,
        "P_41" => null,
        "P_42" => null,
        "P_43" => null,
        "P_44" => null,
        "P_45" => null,
        "P_46" => null,
        "P_47" => null,
        "P_48" => null,
        "P_49" => null,
        "P_50" => null,
        "P_51" => 0,
        "P_52" => null,
        "P_53" => null,
        "P_54" => null,
        "P_540" => null,
        "P_55" => null,
        "P_56" => null,
        "P_560" => null,
        "P_57" => null,
        "P_58" => null,
        "P_59" => null,
        "P_60" => null,
        "P_61" => null,
        "P_62" => null,
        "P_63" => null,
        "P_64" => null,
        "P_65" => null,
        "P_66" => null,
        "P_660" => null,
        "P_67" => null,
        "P_68" => null,
        "P_69" => null,
        "P_ORDZU" => null,
    ];

    protected array $summary_fields = [
        37,
        38,
        48,
        68,
        69
    ];

    protected $namespace = "";

    public function calcPositionsP(array $k_pozycje): void
    {

        foreach ($k_pozycje as $wiersz) {
            $values = $wiersz->getValues();
            foreach ($values as $kpoz => $kval) {
                if (1 !== preg_match('/K_\d\d/', $kpoz)) {
                    continue;
                }
                echo $kpoz . ': ' . $kval . PHP_EOL;
                $counter = (int)substr($kpoz, 2);
                if ($kval !== "" && !in_array($counter, $this->summary_fields)) {
                    $this->pozycje['P_' . $counter] === null ?
                        $this->pozycje['P_' . $counter] = $kval :
                        $this->pozycje['P_' . $counter] += $kval;
                }
            }
        }
    }

    public function calcSumaP37AndP38(): void
    {
        $this->pozycje['P_37'] = ($this->pozycje['P_10'] ?? 0) +
            ($this->pozycje['P_11'] ?? 0) +
            ($this->pozycje['P_13'] ?? 0) +
            ($this->pozycje['P_15'] ?? 0) +
            ($this->pozycje['P_17'] ?? 0) +
            ($this->pozycje['P_19'] ?? 0) +
            ($this->pozycje['P_21'] ?? 0) +
            ($this->pozycje['P_22'] ?? 0) +
            ($this->pozycje['P_23'] ?? 0) +
            ($this->pozycje['P_25'] ?? 0) +
            ($this->pozycje['P_27'] ?? 0) +
            ($this->pozycje['P_29'] ?? 0) +
            ($this->pozycje['P_31'] ?? 0);

        $this->pozycje['P_38'] = ($this->pozycje['P_16'] ?? 0) +
            ($this->pozycje['P_18'] ?? 0) +
            ($this->pozycje['P_20'] ?? 0) +
            ($this->pozycje['P_24'] ?? 0) +
            ($this->pozycje['P_26'] ?? 0) +
            ($this->pozycje['P_28'] ?? 0) +
            ($this->pozycje['P_30'] ?? 0) +
            ($this->pozycje['P_32'] ?? 0) +
            ($this->pozycje['P_33'] ?? 0) +
            ($this->pozycje['P_34'] ?? 0) -
            ($this->pozycje['P_35'] ?? 0) -
            ($this->pozycje['P_36'] ?? 0);
    }

    public function calcSumaP48(): void
    {
        $this->pozycje['P_48'] = ($this->pozycje['P_39'] ?? 0) +
            ($this->pozycje['P_41'] ?? 0) +
            ($this->pozycje['P_43'] ?? 0) +
            ($this->pozycje['P_44'] ?? 0) +
            ($this->pozycje['P_45'] ?? 0) +
            ($this->pozycje['P_46'] ?? 0) +
            ($this->pozycje['P_47'] ?? 0);
    }

    public function calcSumaP68(): void
    {
        $this->pozycje['P_68'] = ($this->pozycje['P_15'] ?? 0) +
            ($this->pozycje['P_17'] ?? 0) +
            ($this->pozycje['P_19'] ?? 0);

        if ($this->pozycje['P_68'] > 0) {
            $this->pozycje['P_68'] *= -1;
        }
    }

    public function calcSumaP69(): void
    {
        $this->pozycje['P_69'] = ($this->pozycje['P_16'] ?? 0) +
            ($this->pozycje['P_18'] ?? 0) +
            ($this->pozycje['P_20'] ?? 0);

        if ($this->pozycje['P_69'] > 0) {
            $this->pozycje['P_69'] *= -1;
        }
    }

    public function sumPositions(): void
    {
        $this->calcSumaP37AndP38();
        $this->calcSumaP48();
        $this->calcSumaP68();
        $this->calcSumaP69();
    }


    public function getXML(\DOMDocument $root, \DOMElement $fragment, bool $append = false): \DOMElement
    {
        $deklaracja = $root->createElement($this->namespace . 'Deklaracja');
        $naglowek = $root->createElement($this->namespace . 'Naglowek');
        foreach ($this->values as $element => $element_value) {
            $property = $root->createElement($this->namespace . $element, $element_value);
            if (array_key_exists($element, $this->attributes)) {
                foreach ($this->attributes[$element] as $attr_name => $attr_value) {
                    $property->setAttribute($attr_name, $attr_value);
                }
            }
            $naglowek->appendChild($property);
        }
        $deklaracja->appendChild($naglowek);
        $pozycje = $root->createElement('PozycjeSzczegolowe');
        foreach ($this->pozycje as $element => $element_value) {
            if (null === $element_value) {
                continue;
            }
            $property = $root->createElement($this->namespace . $element, $element_value);
            $pozycje->appendChild($property);
        }

        $deklaracja->appendChild($pozycje);
        return $deklaracja;
    }
}
