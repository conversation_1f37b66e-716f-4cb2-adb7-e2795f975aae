<?php

namespace XSoft\JPK\Objects\V7M;

use XSoft\JPK\Objects\V7M\ZakupWiersz;

class ZakupCtrl
{
    protected int $liczba_wierszy = 0;
    protected int $podatek_naliczony = 0;

    private array $targetFieldsSUM = [
        'K_41',
        'K_43',
        'K_44',
        'K_45',
        'K_46',
        'K_47',
    ];


    /**
     * @param array<ZakupWiersz> $sprzedaz
     * @return void
     */
    public function podliczZakupy(array $zakupy): void
    {
        $sum = 0;
        foreach ($zakupy as $wiersz) {
            $this->liczba_wierszy++;
            $values = $wiersz->getValues();
            foreach ($this->targetFieldsSUM as $fied) {
                if (!empty($values[$fied])) {
                    $sum += ($values[$fied] * 100);
                }
            }
        }

        $this->podatek_naliczony = $sum;
    }

    public function getXML(\DOMDocument $root, \DOMElement $fragment, bool $append = false): \DOMElement
    {
        $current_element = $root->createElement('ZakupCtrl');
        $liczba_wierszy = $root->createElement('LiczbaWierszyZakupow', $this->liczba_wierszy);
        $podatek_nalezny = $root->createElement(
            'PodatekNaliczony',
            number_format(($this->podatek_naliczony) / 100, 2, '.', '')
        );

        $current_element->appendChild($liczba_wierszy);
        $current_element->appendChild($podatek_nalezny);
        return $current_element;
    }
}
