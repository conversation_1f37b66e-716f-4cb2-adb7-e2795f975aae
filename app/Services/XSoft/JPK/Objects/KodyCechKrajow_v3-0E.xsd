<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:kck="http://crd.gov.pl/xml/schematy/dziedzinowe/mf/2013/05/23/eD/KodyCECHKRAJOW/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://crd.gov.pl/xml/schematy/dziedzinowe/mf/2013/05/23/eD/KodyCECHKRAJOW/" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xsd:simpleType name="CountryCode_Type">
		<xsd:union memberTypes="kck:CountryCodeExMS_Type kck:MSCountryCode_Type"/>
	</xsd:simpleType>
	<!--  ISO 3166 alpha 2 Country Code -->
	<xsd:simpleType name="CountryCodeExMS_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">
			The appropriate country code from the ISO 3166 two-byte alpha version for the state in which the party concerned is a resident. Omit this only if  no data is available.
			This list excludes Member States of the European Union
The following entries must not be used:
	- AN --  NETHERLANDS ANTILLES
Valid entries are:
	- AF --  AFGHANISTAN 
	- AX --  ÅLAND ISLANDS
	- AL --  ALBANIA 
	- DZ --  ALGERIA 
	- AS --  AMERICAN SAMOA 
	- AD --  ANDORRA 
	- AO --  ANGOLA 
	- AI --  ANGUILLA 
	- AQ --  ANTARCTICA 
	- AG --  ANTIGUA AND BARBUDA 
	- AR --  ARGENTINA 
	- AM --  ARMENIA 
	- AW --  ARUBA 
	- AU --  AUSTRALIA 
	- AZ --  AZERBAIJAN 
	- BS --  BAHAMAS 
	- BH --  BAHRAIN 
	- BD --  BANGLADESH 
	- BB --  BARBADOS 
	- BY --  BELARUS 
	- BZ --  BELIZE 
	- BJ --  BENIN 
	- BM --  BERMUDA 
	- BT --  BHUTAN 
	- BO --  BOLIVIA 
	- BA --  BOSNIA AND HERZEGOVINA 
	- BW --  BOTSWANA 
	- BV --  BOUVET ISLAND 
	- BR --  BRAZIL 
	- IO --  BRITISH INDIAN OCEAN TERRITORY 
	- BN --  BRUNEI DARUSSALAM 
	- BF --  BURKINA FASO 
	- BI --  BURUNDI 
	- KH --  CAMBODIA 
	- CM --  CAMEROON 
	- CA --  CANADA 
	- CV --  CAPE VERDE 
	- KY --  CAYMAN ISLANDS 
	- CF --  CENTRAL AFRICAN REPUBLIC 
	- TD --  CHAD 
	- CL --  CHILE 
	- CN --  CHINA 
	- CX --  CHRISTMAS ISLAND 
	- CC --  COCOS (KEELING) ISLANDS 
	- CO --  COLOMBIA 
	- KM --  COMOROS 
	- CG --  CONGO 
	- CD --  CONGO, THE DEMOCRATIC REPUBLIC OF THE 
	- CK --  COOK ISLANDS 
	- CR --  COSTA RICA 
	- CI --  COTE D'IVOIRE 
	- CU --  CUBA 
	- DJ --  DJIBOUTI 
	- DM --  DOMINICA 
	- DO --  DOMINICAN REPUBLIC 
	- EC --  ECUADOR 
	- EG --  EGYPT 
	- SV --  EL SALVADOR 
	- GQ --  EQUATORIAL GUINEA 
	- ER --  ERITREA 
	- ET --  ETHIOPIA 
	- FK --  FALKLAND ISLANDS (MALVINAS) 
	- FO --  FAROE ISLANDS 
	- FJ --  FIJI 
	- GF --  FRENCH GUIANA 
	- PF --  FRENCH POLYNESIA 
	- TF --  FRENCH SOUTHERN TERRITORIES 
	- GA --  GABON 
	- GM --  GAMBIA 
	- GE --  GEORGIA 
	- GH --  GHANA 
	- GI --  GIBRALTAR 
	- GL --  GREENLAND 
	- GD --  GRENADA 
	- GP --  GUADELOUPE 
	- GU --  GUAM 
	- GT --  GUATEMALA 
	- GG --  GUERNSEY
	- GN --  GUINEA 
	- GW --  GUINEA-BISSAU 
	- GY --  GUYANA 
	- HT --  HAITI 
	- HM --  HEARD ISLAND AND MCDONALD ISLANDS 
	- VA --  HOLY SEE (VATICAN CITY STATE) 
	- HN --  HONDURAS 
	- HK --  HONG KONG 
	- IS --  ICELAND 
	- IN --  INDIA 
	- ID --  INDONESIA 
	- IR --  IRAN, ISLAMIC REPUBLIC OF 
	- IQ --  IRAQ 
	- IM --  ISLE OF MAN
	- IL --  ISRAEL 
	- JM --  JAMAICA 
	- JP --  JAPAN 
	- JE --	 JERSEY
	- JO --  JORDAN 
	- KZ --  KAZAKHSTAN 
	- KE --  KENYA 
	- KI --  KIRIBATI 
	- KP --  KOREA, DEMOCRATIC PEOPLE'S REPUBLIC OF 
	- KR --  KOREA, REPUBLIC OF 
	- KW --  KUWAIT 
	- KG --  KYRGYZSTAN 
	- LA --  LAO PEOPLE'S DEMOCRATIC REPUBLIC 
	- LB --  LEBANON 
	- LS --  LESOTHO 
	- LR --  LIBERIA 
	- LY --  LIBYAN ARAB JAMAHIRIYA 
	- LI --  LIECHTENSTEIN 
	- MO --  MACAO 
	- MK --  MACEDONIA, THE FORMER YUGOSLAV REPUBLIC OF 
	- MG --  MADAGASCAR 
	- MW --  MALAWI 
	- MY --  MALAYSIA 
	- MV --  MALDIVES 
	- ML --  MALI 
	- MH --  MARSHALL ISLANDS 
	- MQ --  MARTINIQUE 
	- MR --  MAURITANIA 
	- MU --  MAURITIUS 
	- YT --  MAYOTTE 
	- MX --  MEXICO 
	- FM --  MICRONESIA, FEDERATED STATES OF 
	- MD --  MOLDOVA, REPUBLIC OF 
	- MN --  MONGOLIA 
	- ME --  MONTENEGRO
	- MS --  MONTSERRAT 
	- MA --  MOROCCO 
	- MZ --  MOZAMBIQUE 
	- MM --  MYANMAR 
	- NA --  NAMIBIA 
	- NR --  NAURU 
	- NP --  NEPAL 
	- AN --  NETHERLANDS ANTILLES 
	- NC --  NEW CALEDONIA 
	- NZ --  NEW ZEALAND 
	- NI --  NICARAGUA 
	- NE --  NIGER 
	- NG --  NIGERIA 
	- NU --  NIUE 
	- NF --  NORFOLK ISLAND 
	- MP --  NORTHERN MARIANA ISLANDS 
	- NO --  NORWAY 
	- OM --  OMAN 
	- PK --  PAKISTAN 
	- PW --  PALAU 
	- PS --  PALESTINIAN TERRITORY, OCCUPIED 
	- PA --  PANAMA 
	- PG --  PAPUA NEW GUINEA 
	- PY --  PARAGUAY 
	- PE --  PERU 
	- PH --  PHILIPPINES 
	- PN --  PITCAIRN 
	- PR --  PUERTO RICO 
	- QA --  QATAR 
	- RE --  REUNION 
	- RU --  RUSSIAN FEDERATION 
	- RW --  RWANDA 
	- BL --  SAINT BARTHÉLEMY
	- SH --  SAINT HELENA 
	- KN --  SAINT KITTS AND NEVIS 
	- LC --  SAINT LUCIA 
	- MF --  SAINT MARTIN
	- PM --  SAINT PIERRE AND MIQUELON 
	- VC --  SAINT VINCENT AND THE GRENADINES 
	- WS --  SAMOA 
	- SM --  SAN MARINO 
	- ST --  SAO TOME AND PRINCIPE 
	- SA --  SAUDI ARABIA 
	- SN --  SENEGAL 
	- RS --  SERBIA
	- SC --  SEYCHELLES 
	- SL --  SIERRA LEONE 
	- SG --  SINGAPORE 
	- SB --  SOLOMON ISLANDS 
	- SO --  SOMALIA 
	- ZA --  SOUTH AFRICA 
	- GS --  SOUTH GEORGIA AND THE SOUTH SANDWICH ISLANDS 
	- LK --  SRI LANKA 
	- SD --  SUDAN 
	- SR --  SURINAME 
	- SJ --  SVALBARD AND JAN MAYEN 
	- SZ --  SWAZILAND 
	- CH --  SWITZERLAND 
	- SY --  SYRIAN ARAB REPUBLIC 
	- TW --  TAIWAN, PROVINCE OF CHINA 
	- TJ --  TAJIKISTAN 
	- TZ --  TANZANIA, UNITED REPUBLIC OF 
	- TH --  THAILAND 
	- TL --  TIMOR-LESTE 
	- TG --  TOGO 
	- TK --  TOKELAU 
	- TO --  TONGA 
	- TT --  TRINIDAD AND TOBAGO 
	- TN --  TUNISIA 
	- TR --  TURKEY 
	- TM --  TURKMENISTAN 
	- TC --  TURKS AND CAICOS ISLANDS 
	- TV --  TUVALU 
	- UG --  UGANDA 
	- UA --  UKRAINE 
	- AE --  UNITED ARAB EMIRATES 
	- US --  UNITED STATES 
	- UM --  UNITED STATES MINOR OUTLYING ISLANDS 
	- UY --  URUGUAY 
	- UZ --  UZBEKISTAN 
	- VU --  VANUATU 
	- VE --  VENEZUELA, BOLIVARIAN REPUBLIC OF
	- VN --  VIET NAM 
	- VG --  VIRGIN ISLANDS, BRITISH 
	- VI --  VIRGIN ISLANDS, U.S. 
	- WF --  WALLIS AND FUTUNA 
	- EH --  WESTERN SAHARA 
	- YE --  YEMEN 
	- ZM --  ZAMBIA 
	- ZW --  ZIMBABWE 
	- CW --  CURACAO
	- NM --  SAINT MARTIN (DUTCH PART) - invalidated
	- SX --  SAINT MARTIN (DUTCH PART)
	- BQ --  BONAIRE, SAINT EUSTATIUS AND SABA
			</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="AF"/>
			<xsd:enumeration value="AX"/>
			<xsd:enumeration value="AL"/>
			<xsd:enumeration value="DZ"/>
			<xsd:enumeration value="AS"/>
			<xsd:enumeration value="AD"/>
			<xsd:enumeration value="AO"/>
			<xsd:enumeration value="AI"/>
			<xsd:enumeration value="AQ"/>
			<xsd:enumeration value="AG"/>
			<xsd:enumeration value="AR"/>
			<xsd:enumeration value="AM"/>
			<xsd:enumeration value="AW"/>
			<xsd:enumeration value="AU"/>
			<xsd:enumeration value="AZ"/>
			<xsd:enumeration value="BS"/>
			<xsd:enumeration value="BH"/>
			<xsd:enumeration value="BD"/>
			<xsd:enumeration value="BB"/>
			<xsd:enumeration value="BY"/>
			<xsd:enumeration value="BZ"/>
			<xsd:enumeration value="BJ"/>
			<xsd:enumeration value="BM"/>
			<xsd:enumeration value="BT"/>
			<xsd:enumeration value="BO"/>
			<xsd:enumeration value="BA"/>
			<xsd:enumeration value="BW"/>
			<xsd:enumeration value="BV"/>
			<xsd:enumeration value="BR"/>
			<xsd:enumeration value="IO"/>
			<xsd:enumeration value="BN"/>
			<xsd:enumeration value="BF"/>
			<xsd:enumeration value="BI"/>
			<xsd:enumeration value="KH"/>
			<xsd:enumeration value="CM"/>
			<xsd:enumeration value="CA"/>
			<xsd:enumeration value="CV"/>
			<xsd:enumeration value="KY"/>
			<xsd:enumeration value="CF"/>
			<xsd:enumeration value="TD"/>
			<xsd:enumeration value="CL"/>
			<xsd:enumeration value="CN"/>
			<xsd:enumeration value="CX"/>
			<xsd:enumeration value="CC"/>
			<xsd:enumeration value="CO"/>
			<xsd:enumeration value="KM"/>
			<xsd:enumeration value="CG"/>
			<xsd:enumeration value="CD"/>
			<xsd:enumeration value="CK"/>
			<xsd:enumeration value="CR"/>
			<xsd:enumeration value="CI"/>
			<xsd:enumeration value="CU"/>
			<xsd:enumeration value="DJ"/>
			<xsd:enumeration value="DM"/>
			<xsd:enumeration value="DO"/>
			<xsd:enumeration value="EC"/>
			<xsd:enumeration value="EG"/>
			<xsd:enumeration value="SV"/>
			<xsd:enumeration value="GQ"/>
			<xsd:enumeration value="ER"/>
			<xsd:enumeration value="ET"/>
			<xsd:enumeration value="FK"/>
			<xsd:enumeration value="FO"/>
			<xsd:enumeration value="FJ"/>
			<xsd:enumeration value="GF"/>
			<xsd:enumeration value="PF"/>
			<xsd:enumeration value="TF"/>
			<xsd:enumeration value="GA"/>
			<xsd:enumeration value="GM"/>
			<xsd:enumeration value="GE"/>
			<xsd:enumeration value="GH"/>
			<xsd:enumeration value="GI"/>
			<xsd:enumeration value="GL"/>
			<xsd:enumeration value="GD"/>
			<xsd:enumeration value="GP"/>
			<xsd:enumeration value="GU"/>
			<xsd:enumeration value="GT"/>
			<xsd:enumeration value="GG"/>
			<xsd:enumeration value="GN"/>
			<xsd:enumeration value="GW"/>
			<xsd:enumeration value="GY"/>
			<xsd:enumeration value="HT"/>
			<xsd:enumeration value="HM"/>
			<xsd:enumeration value="VA"/>
			<xsd:enumeration value="HN"/>
			<xsd:enumeration value="HK"/>
			<xsd:enumeration value="IS"/>
			<xsd:enumeration value="IN"/>
			<xsd:enumeration value="ID"/>
			<xsd:enumeration value="IR"/>
			<xsd:enumeration value="IQ"/>
			<xsd:enumeration value="IM"/>
			<xsd:enumeration value="IL"/>
			<xsd:enumeration value="JM"/>
			<xsd:enumeration value="JP"/>
			<xsd:enumeration value="JE"/>
			<xsd:enumeration value="JO"/>
			<xsd:enumeration value="KZ"/>
			<xsd:enumeration value="KE"/>
			<xsd:enumeration value="KI"/>
			<xsd:enumeration value="KP"/>
			<xsd:enumeration value="KR"/>
			<xsd:enumeration value="KW"/>
			<xsd:enumeration value="KG"/>
			<xsd:enumeration value="LA"/>
			<xsd:enumeration value="LB"/>
			<xsd:enumeration value="LS"/>
			<xsd:enumeration value="LR"/>
			<xsd:enumeration value="LY"/>
			<xsd:enumeration value="LI"/>
			<xsd:enumeration value="MO"/>
			<xsd:enumeration value="MK"/>
			<xsd:enumeration value="MG"/>
			<xsd:enumeration value="MW"/>
			<xsd:enumeration value="MY"/>
			<xsd:enumeration value="MV"/>
			<xsd:enumeration value="ML"/>
			<xsd:enumeration value="MH"/>
			<xsd:enumeration value="MQ"/>
			<xsd:enumeration value="MR"/>
			<xsd:enumeration value="MU"/>
			<xsd:enumeration value="YT"/>
			<xsd:enumeration value="MX"/>
			<xsd:enumeration value="FM"/>
			<xsd:enumeration value="MD"/>
			<xsd:enumeration value="MN"/>
			<xsd:enumeration value="ME"/>
			<xsd:enumeration value="MS"/>
			<xsd:enumeration value="MA"/>
			<xsd:enumeration value="MZ"/>
			<xsd:enumeration value="MM"/>
			<xsd:enumeration value="NA"/>
			<xsd:enumeration value="NR"/>
			<xsd:enumeration value="NP"/>
			<xsd:enumeration value="AN"/>
			<xsd:enumeration value="NC"/>
			<xsd:enumeration value="NZ"/>
			<xsd:enumeration value="NI"/>
			<xsd:enumeration value="NE"/>
			<xsd:enumeration value="NG"/>
			<xsd:enumeration value="NU"/>
			<xsd:enumeration value="NF"/>
			<xsd:enumeration value="MP"/>
			<xsd:enumeration value="NO"/>
			<xsd:enumeration value="OM"/>
			<xsd:enumeration value="PK"/>
			<xsd:enumeration value="PW"/>
			<xsd:enumeration value="PS"/>
			<xsd:enumeration value="PA"/>
			<xsd:enumeration value="PG"/>
			<xsd:enumeration value="PY"/>
			<xsd:enumeration value="PE"/>
			<xsd:enumeration value="PH"/>
			<xsd:enumeration value="PN"/>
			<xsd:enumeration value="PR"/>
			<xsd:enumeration value="QA"/>
			<xsd:enumeration value="RE"/>
			<xsd:enumeration value="RU"/>
			<xsd:enumeration value="RW"/>
			<xsd:enumeration value="BL"/>
			<xsd:enumeration value="SH"/>
			<xsd:enumeration value="KN"/>
			<xsd:enumeration value="LC"/>
			<xsd:enumeration value="MF"/>
			<xsd:enumeration value="PM"/>
			<xsd:enumeration value="VC"/>
			<xsd:enumeration value="WS"/>
			<xsd:enumeration value="SM"/>
			<xsd:enumeration value="ST"/>
			<xsd:enumeration value="SA"/>
			<xsd:enumeration value="SN"/>
			<xsd:enumeration value="RS"/>
			<xsd:enumeration value="SC"/>
			<xsd:enumeration value="SL"/>
			<xsd:enumeration value="SG"/>
			<xsd:enumeration value="SB"/>
			<xsd:enumeration value="SO"/>
			<xsd:enumeration value="ZA"/>
			<xsd:enumeration value="GS"/>
			<xsd:enumeration value="LK"/>
			<xsd:enumeration value="SD"/>
			<xsd:enumeration value="SR"/>
			<xsd:enumeration value="SJ"/>
			<xsd:enumeration value="SZ"/>
			<xsd:enumeration value="CH"/>
			<xsd:enumeration value="SY"/>
			<xsd:enumeration value="TW"/>
			<xsd:enumeration value="TJ"/>
			<xsd:enumeration value="TZ"/>
			<xsd:enumeration value="TH"/>
			<xsd:enumeration value="TL"/>
			<xsd:enumeration value="TG"/>
			<xsd:enumeration value="TK"/>
			<xsd:enumeration value="TO"/>
			<xsd:enumeration value="TT"/>
			<xsd:enumeration value="TN"/>
			<xsd:enumeration value="TR"/>
			<xsd:enumeration value="TM"/>
			<xsd:enumeration value="TC"/>
			<xsd:enumeration value="TV"/>
			<xsd:enumeration value="UG"/>
			<xsd:enumeration value="UA"/>
			<xsd:enumeration value="AE"/>
			<xsd:enumeration value="US"/>
			<xsd:enumeration value="UM"/>
			<xsd:enumeration value="UY"/>
			<xsd:enumeration value="UZ"/>
			<xsd:enumeration value="VU"/>
			<xsd:enumeration value="VE"/>
			<xsd:enumeration value="VN"/>
			<xsd:enumeration value="VG"/>
			<xsd:enumeration value="VI"/>
			<xsd:enumeration value="WF"/>
			<xsd:enumeration value="EH"/>
			<xsd:enumeration value="YE"/>
			<xsd:enumeration value="ZM"/>
			<xsd:enumeration value="ZW"/>
			<xsd:enumeration value="CW"/>
			<xsd:enumeration value="NM"/>
			<xsd:enumeration value="SX"/>
			<xsd:enumeration value="BQ"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MSCountryCode_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">
			The appropriate country code from the ISO 3166 two-byte alpha version for the state in which the party concerned is a resident. Omit this only if  no data is available.
			This list includes only Member States of the European Union
Valid entries are:
  - AT --  AUSTRIA 
  - BE --  BELGIUM 
  - BG --  BULGARIA 
  - HR --  CROATIA 
  - CY --  CYPRUS 
  - CZ --  CZECH REPUBLIC 
  - DK --  DENMARK 
  - EE --  ESTONIA 
  - FI --  FINLAND 
  - FR --  FRANCE 
  - DE --  GERMANY 
  - EL --  GREECE 
  - HU --  HUNGARY 
  - IE --  IRELAND 
  - IT --  ITALY 
  - LV --  LATVIA 
  - LT --  LITHUANIA 
  - LU --  LUXEMBOURG 
  - MT --  MALTA 
  - NL --  NETHERLANDS 
  - PL --  POLAND 
  - PT --  PORTUGAL 
  - RO --  ROMANIA 
  - SK --  SLOVAKIA 
  - SI --  SLOVENIA 
  - ES --  SPAIN 
  - SE --  SWEDEN 
  - GB --  UNITED KINGDOM 
  - IC --  CANARY ISLANDS
  - XI --  CEUTA
  - XJ --  MELILLA
  - MC --  MONACO 
			</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="AT"/>
			<xsd:enumeration value="BE"/>
			<xsd:enumeration value="BG"/>
			<xsd:enumeration value="CY"/>
			<xsd:enumeration value="CZ"/>
			<xsd:enumeration value="DK"/>
			<xsd:enumeration value="EE"/>
			<xsd:enumeration value="FI"/>
			<xsd:enumeration value="FR"/>
			<xsd:enumeration value="DE"/>
			<xsd:enumeration value="EL"/>
			<xsd:enumeration value="HR"/>
			<xsd:enumeration value="HU"/>
			<xsd:enumeration value="IE"/>
			<xsd:enumeration value="IT"/>
			<xsd:enumeration value="LV"/>
			<xsd:enumeration value="LT"/>
			<xsd:enumeration value="LU"/>
			<xsd:enumeration value="MT"/>
			<xsd:enumeration value="NL"/>
			<xsd:enumeration value="PL"/>
			<xsd:enumeration value="PT"/>
			<xsd:enumeration value="RO"/>
			<xsd:enumeration value="SK"/>
			<xsd:enumeration value="SI"/>
			<xsd:enumeration value="ES"/>
			<xsd:enumeration value="SE"/>
			<xsd:enumeration value="GB"/>
			<xsd:enumeration value="IC"/>
			<xsd:enumeration value="XI"/>
			<xsd:enumeration value="XJ"/>
			<xsd:enumeration value="MC"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!--  ISO 4217 alpha 3 Currency Code -->
	<xsd:simpleType name="currCode_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">
			The appropriate currency code from the ISO 4217 three-byte alpha version for the currency in which a monetary amount is expressed. 
Valid entries are:
AED United Arab Emirates, Dirhams 
AFN Afghanistan, Afghanis 
ALL Albania, Leke 
AMD Armenia, Drams 
ANG Netherlands Antilles, Guilders 
AOA Angola, Kwanza 
ARS Argentina, Pesos 
AUD Australia, Dollars 
AWG Aruba, Guilders 
AZN Azerbaijan, Manats 
BAM Bosnia and Herzegovina, Convertible Marka 
BBD Barbados, Dollars 
BDT Bangladesh, Taka 
BGN Bulgaria, Leva 
BHD Bahrain, Dinars 
BIF Burundi, Francs 
BMD Bermuda, Dollars 
BND Brunei Darussalam, Dollars 
BOB Bolivia, Bolivianos 
BOV Bolivia, Mvdol
BRL Brazil, Brazil Real 
BSD Bahamas, Dollars 
BTN Bhutan, Ngultrum 
BWP Botswana, Pulas 
BYR Belarus, Rubles 
BZD Belize, Dollars 
CAD Canada, Dollars 
CDF Congo/Kinshasa, Congolese Francs 
CHF Switzerland, Francs 
CLF Chile, Unidades de fomento 
CLP Chile, Pesos 
CNY China, Yuan Renminbi 
COP Colombia, Pesos 
COU Colombia, Unidad de Valor Real
CRC Costa Rica, Colones 
CUC Cuba, Convertible Pesos 
CUP Cuba, Pesos 
CVE Cape Verde, Escudos 
CZK Czech Republic, Koruny 
DJF Djibouti, Francs 
DKK Denmark, Kroner 
DOP Dominican Republic, Pesos 
DZD Algeria, Algeria Dinars 
EEK Estonia, Krooni 
EGP Egypt, Pounds 
ERN Eritrea, Nakfa 
ETB Ethiopia, Birr 
EUR Euro Member Countries, Euro 
FJD Fiji, Dollars 
FKP Falkland Islands (Malvinas), Pounds 
GBP United Kingdom, Pounds 
GEL Georgia, Lari 
GHS Ghana, Cedis 
GIP Gibraltar, Pounds 
GMD Gambia, Dalasi 
GNF Guinea, Francs 
GTQ Guatemala, Quetzales 
GWP Guinea-Bissau Peso
GYD Guyana, Dollars 
HKD Hong Kong, Dollars 
HNL Honduras, Lempiras 
HRK Croatia, Kuna 
HTG Haiti, Gourdes 
HUF Hungary, Forint 
IDR Indonesia, Rupiahs 
ILS Israel, New Shekels 
INR India, Rupees 
IQD Iraq, Dinars 
IRR Iran, Rials 
ISK Iceland, Kronur  
JMD Jamaica, Dollars 
JOD Jordan, Dinars 
JPY Japan, Yen 
KES Kenya, Shillings 
KGS Kyrgyzstan, Soms 
KHR Cambodia, Riels 
KMF Comoros, Francs 
KPW Korea (North), Won 
KRW Korea (South), Won 
KWD Kuwait, Dinars 
KYD Cayman Islands, Dollars 
KZT Kazakstan, Tenge 
LAK Laos, Kips 
LBP Lebanon, Pounds 
LKR Sri Lanka, Rupees 
LRD Liberia, Dollars 
LSL Lesotho, Maloti 
LTL Lithuania, Litai 
LVL Latvia, Lati 
LYD Libya, Dinars 
MAD Morocco, Dirhams 
MDL Moldova, Lei 
MGA Madagascar, Malagasy Ariary 
MKD Macedonia, Denars 
MMK Myanmar (Burma), Kyats 
MNT Mongolia, Tugriks 
MOP Macau, Patacas 
MRO Mauritania, Ouguiyas 
MTL Malta, Liri 
MUR Mauritius, Rupees 
MVR Maldives (Maldive Islands), Rufiyaa 
MWK Malawi, Kwachas 
MXN Mexico, Pesos 
MXV Mexico, Mexican Unidad de Inversion
MYR Malaysia, Ringgits 
MZN Mozambique, Meticais 
NAD Namibia, Dollars 
NGN Nigeria, Nairas 
NIO Nicaragua, Gold Cordobas 
NOK Norway, Krone 
NPR Nepal, Nepal Rupees 
NZD New Zealand, Dollars 
OMR Oman, Rials 
PAB Panama, Balboa 
PEN Peru, Nuevos Soles 
PGK Papua New Guinea, Kina 
PHP Philippines, Pesos 
PKR Pakistan, Rupees 
PLN Poland, Zlotych 
PYG Paraguay, Guarani 
QAR Qatar, Rials 
RON Romania, New Lei 
RSD Serbian Dinar
RUB Russia, Rubles 
RWF Rwanda, Rwanda Francs 
SAR Saudi Arabia, Riyals 
SBD Solomon Islands, Dollars 
SCR Seychelles, Rupees 
SDG Sudan, Dinars 
SEK Sweden, Kronor 
SGD Singapore, Dollars 
SHP Saint Helena, Pounds 
SLL Sierra Leone, Leones 
SOS Somalia, Shillings 
SRD Suriname, Dollar 
STD São Tome and Principe, Dobras 
SVC El Salvador, Colones 
SYP Syria, Pounds 
SZL Swaziland, Emalangeni 
THB Thailand, Baht 
TJS Tajikistan, Somoni 
TMT Turkmenistan, Manats 
TND Tunisia, Dinars 
TOP Tonga, Pa'anga 
TRY Turkey, Liras 
TTD Trinidad and Tobago, Dollars 
TWD Taiwan, New Dollars 
TZS Tanzania, Shillings 
UAH Ukraine, Hryvnia 
UGX Uganda, Shillings 
USD United States of America, Dollars 
UYU Uruguay, Pesos 
UZS Uzbekistan, Sums 
VEF Venezuela, Bolivares 
VND Viet Nam, Dong 
VUV Vanuatu, Vatu 
WST Samoa, Tala 
XAF Communauté Financière Africaine BEAC, Francs 
XCD East Caribbean Dollars 
XOF Communauté Financière Africaine BCEAO, Francs 
XPD Palladium Ounces 
XPF Comptoirs Français du Pacifique Francs 
YER Yemen, Rials 
ZAR South Africa, Rand 
ZMK Zambia, Kwacha 
ZWL Zimbabwe, Zimbabwe Dollars 
			</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="AED"/>
			<xsd:enumeration value="AFN"/>
			<xsd:enumeration value="ALL"/>
			<xsd:enumeration value="AMD"/>
			<xsd:enumeration value="ANG"/>
			<xsd:enumeration value="AOA"/>
			<xsd:enumeration value="ARS"/>
			<xsd:enumeration value="AUD"/>
			<xsd:enumeration value="AWG"/>
			<xsd:enumeration value="AZN"/>
			<xsd:enumeration value="BAM"/>
			<xsd:enumeration value="BBD"/>
			<xsd:enumeration value="BDT"/>
			<xsd:enumeration value="BGN"/>
			<xsd:enumeration value="BHD"/>
			<xsd:enumeration value="BIF"/>
			<xsd:enumeration value="BMD"/>
			<xsd:enumeration value="BND"/>
			<xsd:enumeration value="BOB"/>
			<xsd:enumeration value="BOV"/>
			<xsd:enumeration value="BRL"/>
			<xsd:enumeration value="BSD"/>
			<xsd:enumeration value="BTN"/>
			<xsd:enumeration value="BWP"/>
			<xsd:enumeration value="BYR"/>
			<xsd:enumeration value="BZD"/>
			<xsd:enumeration value="CAD"/>
			<xsd:enumeration value="CDF"/>
			<xsd:enumeration value="CHF"/>
			<xsd:enumeration value="CLF"/>
			<xsd:enumeration value="CLP"/>
			<xsd:enumeration value="CNY"/>
			<xsd:enumeration value="COP"/>
			<xsd:enumeration value="COU"/>
			<xsd:enumeration value="CRC"/>
			<xsd:enumeration value="CUC"/>
			<xsd:enumeration value="CUP"/>
			<xsd:enumeration value="CVE"/>
			<xsd:enumeration value="CZK"/>
			<xsd:enumeration value="DJF"/>
			<xsd:enumeration value="DKK"/>
			<xsd:enumeration value="DOP"/>
			<xsd:enumeration value="DZD"/>
			<xsd:enumeration value="EEK"/>
			<xsd:enumeration value="EGP"/>
			<xsd:enumeration value="ERN"/>
			<xsd:enumeration value="ETB"/>
			<xsd:enumeration value="EUR"/>
			<xsd:enumeration value="FJD"/>
			<xsd:enumeration value="FKP"/>
			<xsd:enumeration value="GBP"/>
			<xsd:enumeration value="GEL"/>
			<xsd:enumeration value="GHS"/>
			<xsd:enumeration value="GIP"/>
			<xsd:enumeration value="GMD"/>
			<xsd:enumeration value="GNF"/>
			<xsd:enumeration value="GTQ"/>
			<xsd:enumeration value="GWP"/>
			<xsd:enumeration value="GYD"/>
			<xsd:enumeration value="HKD"/>
			<xsd:enumeration value="HNL"/>
			<xsd:enumeration value="HRK"/>
			<xsd:enumeration value="HTG"/>
			<xsd:enumeration value="HUF"/>
			<xsd:enumeration value="IDR"/>
			<xsd:enumeration value="ILS"/>
			<xsd:enumeration value="INR"/>
			<xsd:enumeration value="IQD"/>
			<xsd:enumeration value="IRR"/>
			<xsd:enumeration value="ISK"/>
			<xsd:enumeration value="JMD"/>
			<xsd:enumeration value="JOD"/>
			<xsd:enumeration value="JPY"/>
			<xsd:enumeration value="KES"/>
			<xsd:enumeration value="KGS"/>
			<xsd:enumeration value="KHR"/>
			<xsd:enumeration value="KMF"/>
			<xsd:enumeration value="KPW"/>
			<xsd:enumeration value="KRW"/>
			<xsd:enumeration value="KWD"/>
			<xsd:enumeration value="KYD"/>
			<xsd:enumeration value="KZT"/>
			<xsd:enumeration value="LAK"/>
			<xsd:enumeration value="LBP"/>
			<xsd:enumeration value="LKR"/>
			<xsd:enumeration value="LRD"/>
			<xsd:enumeration value="LSL"/>
			<xsd:enumeration value="LTL"/>
			<xsd:enumeration value="LVL"/>
			<xsd:enumeration value="LYD"/>
			<xsd:enumeration value="MAD"/>
			<xsd:enumeration value="MDL"/>
			<xsd:enumeration value="MGA"/>
			<xsd:enumeration value="MKD"/>
			<xsd:enumeration value="MMK"/>
			<xsd:enumeration value="MNT"/>
			<xsd:enumeration value="MOP"/>
			<xsd:enumeration value="MRO"/>
			<xsd:enumeration value="MUR"/>
			<xsd:enumeration value="MVR"/>
			<xsd:enumeration value="MWK"/>
			<xsd:enumeration value="MXN"/>
			<xsd:enumeration value="MXV"/>
			<xsd:enumeration value="MYR"/>
			<xsd:enumeration value="MZN"/>
			<xsd:enumeration value="NAD"/>
			<xsd:enumeration value="NGN"/>
			<xsd:enumeration value="NIO"/>
			<xsd:enumeration value="NOK"/>
			<xsd:enumeration value="NPR"/>
			<xsd:enumeration value="NZD"/>
			<xsd:enumeration value="OMR"/>
			<xsd:enumeration value="PAB"/>
			<xsd:enumeration value="PEN"/>
			<xsd:enumeration value="PGK"/>
			<xsd:enumeration value="PHP"/>
			<xsd:enumeration value="PKR"/>
			<xsd:enumeration value="PLN"/>
			<xsd:enumeration value="PYG"/>
			<xsd:enumeration value="QAR"/>
			<xsd:enumeration value="RON"/>
			<xsd:enumeration value="RSD"/>
			<xsd:enumeration value="RUB"/>
			<xsd:enumeration value="RWF"/>
			<xsd:enumeration value="SAR"/>
			<xsd:enumeration value="SBD"/>
			<xsd:enumeration value="SCR"/>
			<xsd:enumeration value="SDG"/>
			<xsd:enumeration value="SEK"/>
			<xsd:enumeration value="SGD"/>
			<xsd:enumeration value="SHP"/>
			<xsd:enumeration value="SLL"/>
			<xsd:enumeration value="SOS"/>
			<xsd:enumeration value="SRD"/>
			<xsd:enumeration value="STD"/>
			<xsd:enumeration value="SVC"/>
			<xsd:enumeration value="SYP"/>
			<xsd:enumeration value="SZL"/>
			<xsd:enumeration value="THB"/>
			<xsd:enumeration value="TJS"/>
			<xsd:enumeration value="TMT"/>
			<xsd:enumeration value="TND"/>
			<xsd:enumeration value="TOP"/>
			<xsd:enumeration value="TRY"/>
			<xsd:enumeration value="TTD"/>
			<xsd:enumeration value="TVD"/>
			<xsd:enumeration value="TWD"/>
			<xsd:enumeration value="TZS"/>
			<xsd:enumeration value="UAH"/>
			<xsd:enumeration value="UGX"/>
			<xsd:enumeration value="USD"/>
			<xsd:enumeration value="UYU"/>
			<xsd:enumeration value="UZS"/>
			<xsd:enumeration value="VEF"/>
			<xsd:enumeration value="VND"/>
			<xsd:enumeration value="VUV"/>
			<xsd:enumeration value="WST"/>
			<xsd:enumeration value="XAF"/>
			<xsd:enumeration value="XCD"/>
			<xsd:enumeration value="XOF"/>
			<xsd:enumeration value="XPD"/>
			<xsd:enumeration value="XPF"/>
			<xsd:enumeration value="YER"/>
			<xsd:enumeration value="ZAR"/>
			<xsd:enumeration value="ZMK"/>
			<xsd:enumeration value="ZWL"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MSCurrCode_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">
			The appropriate currency code from the ISO 4217 three-byte alpha version for the currency in which a monetary amount is expressed. Currency codes are limited to those of Member States.
Valid entries are:
BGN Bulgaria, Leva 
CZK Czech Republic, Koruny 
DKK Denmark, Kroner 
EEK Estonia, Krooni 
EUR Euro Member Countries, Euro 
GBP United Kingdom, Pounds 
HRK Croatia, Kuna 
HUF Hungary, Forint 
LTL Lithuania, Litai 
LVL Latvia, Lati 
PLN Poland, Zlotych 
RON Romania, New Lei 
SEK Sweden, Kronor 
			</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="BGN"/>
			<xsd:enumeration value="CZK"/>
			<xsd:enumeration value="DKK"/>
			<xsd:enumeration value="EEK"/>
			<xsd:enumeration value="EUR"/>
			<xsd:enumeration value="GBP"/>
			<xsd:enumeration value="HRK"/>
			<xsd:enumeration value="HUF"/>
			<xsd:enumeration value="LTL"/>
			<xsd:enumeration value="LVL"/>
			<xsd:enumeration value="PLN"/>
			<xsd:enumeration value="RON"/>
			<xsd:enumeration value="SEK"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MSCurrCodeExPLN_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">
			The appropriate currency code from the ISO 4217 three-byte alpha version for the currency in which a monetary amount is expressed. Currency codes are limited to those of Member States.
Valid entries are:
BGN Bulgaria, Leva 
CZK Czech Republic, Koruny 
DKK Denmark, Kroner 
EEK Estonia, Krooni 
EUR Euro Member Countries, Euro 
GBP United Kingdom, Pounds 
HRK Croatia, Kuna 
HUF Hungary, Forint 
LTL Lithuania, Litai 
LVL Latvia, Lati 
RON Romania, New Lei 
SEK Sweden, Kronor 
			</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="BGN"/>
			<xsd:enumeration value="CZK"/>
			<xsd:enumeration value="DKK"/>
			<xsd:enumeration value="EEK"/>
			<xsd:enumeration value="EUR"/>
			<xsd:enumeration value="GBP"/>
			<xsd:enumeration value="HRK"/>
			<xsd:enumeration value="HUF"/>
			<xsd:enumeration value="LTL"/>
			<xsd:enumeration value="LVL"/>
			<xsd:enumeration value="RON"/>
			<xsd:enumeration value="SEK"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="EULanguageCode_Type">
		<xsd:annotation>
			<xsd:documentation> The list of official languages of the EU.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="bg">
				<xsd:annotation>
					<xsd:documentation>Bulgarian</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="cs">
				<xsd:annotation>
					<xsd:documentation>Czech</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="da">
				<xsd:annotation>
					<xsd:documentation>Danish</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="de">
				<xsd:annotation>
					<xsd:documentation>German</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="el">
				<xsd:annotation>
					<xsd:documentation>Greek</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="en">
				<xsd:annotation>
					<xsd:documentation>English</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="es">
				<xsd:annotation>
					<xsd:documentation>Spanish</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="et">
				<xsd:annotation>
					<xsd:documentation>Estonian</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="fi">
				<xsd:annotation>
					<xsd:documentation>Finnish</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="fr">
				<xsd:annotation>
					<xsd:documentation>French</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ga">
				<xsd:annotation>
					<xsd:documentation>Irish</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="hr">
				<xsd:annotation>
					<xsd:documentation>Croatian</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="hu">
				<xsd:annotation>
					<xsd:documentation>Hungarian</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="it">
				<xsd:annotation>
					<xsd:documentation>Italian</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="lt">
				<xsd:annotation>
					<xsd:documentation>Lithuanian</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="lv">
				<xsd:annotation>
					<xsd:documentation>Latvian</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="mt">
				<xsd:annotation>
					<xsd:documentation>Maltese</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="nl">
				<xsd:annotation>
					<xsd:documentation>Dutch</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="pl">
				<xsd:annotation>
					<xsd:documentation>Polish</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="pt">
				<xsd:annotation>
					<xsd:documentation>Portuguese</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ro">
				<xsd:annotation>
					<xsd:documentation>Romanian</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="sk">
				<xsd:annotation>
					<xsd:documentation>Slovak </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="sl">
				<xsd:annotation>
					<xsd:documentation>Slovenian </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="sv">
				<xsd:annotation>
					<xsd:documentation>Swedish</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="tr">
				<xsd:annotation>
					<xsd:documentation>Turkish</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
</xsd:schema>
