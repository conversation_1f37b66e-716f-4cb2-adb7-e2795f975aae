<?php

namespace App\Jobs;

use App\Models\Tenant;
use Filament\Notifications\Notification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\HtmlString;

class NotifyTenantAdmins implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected string|HtmlString $message,
        protected string $title,
        protected Tenant|int $tenant,
    ) {
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $tenant = $this->getTenant();
        if (blank($tenant)) {
            return;
        }
        Notification::make()
            ->body($this->message)
            ->title($this->title)
            ->sendToDatabase($tenant?->admins()->get());
    }

    protected function getTenant(): ?Tenant
    {
        return is_int($this->tenant) ? Tenant::find($this->tenant) : $this->tenant;
    }
}
