<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum JobTaskStatuses: int implements HasLabel
{
    use EnumHelper;

    case COMPLETED = 0;
    case ACTIVE = 1;
    case IN_PROGRESS = 2;


//    public static function get_label($key): string
//    {
//        return __('app.enums.product_demands_statuses.' . (self::toArray()[$key] ?? 'UNKNOWN'));
//    }

    public function getLabel(): ?string
    {
        return $this->label();
    }
}
