<?php

namespace App\Enums;

enum TaxTypePL: int
{
    use EnumHelper;
    case PROGRESSIVE = 1;
    case LINEAR = 2;
    case FLAT = 3;
    case NONE = 4;

    public function label(): string
    {
        $names = [
            1 => 'Skala podatkowa',
            2 => 'Podatek liniowy',
            3 => 'Ryczałt',
            4 => 'brak',
        ];

        return $names[$this->value] ?? 'brak';
    }

    public static function toArrayWithLabels(): array
    {
        return array_combine(
            array_column(self::cases(), 'value'),
            array_map(fn($key) => $key->label(), self::cases())
        );
    }
}
