<?php

namespace App\Enums;

enum Roles: int
{

    use EnumHelper;

    case SUPER_ADMIN = 1;
    case TENANT_ADMIN = 2;
    case EMPLOYEE = 3;


    public static function get_label($key): string
    {
        $names = [
            1 => __('app.roles.SUPER_ADMIN'),
            2 => __('app.roles.TENANT_ADMIN'),
            3 => __('app.roles.EMPLOYEE'),
        ];

        return $names[$key] ?? 'none';
    }
}
