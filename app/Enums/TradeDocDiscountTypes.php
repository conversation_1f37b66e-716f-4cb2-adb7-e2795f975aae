<?php

namespace App\Enums;

enum TradeDocDiscountTypes: int
{
    use EnumHelper;

    case NO_DISCOUNT = 0;
    case VALUE = 1;
    case PERCENT = 2;

    public function getLabel(): string
    {
        return $this->label();
    }

    public static function getLabelFor($key): string
    {
        return self::tryFrom($key)?->label() ?? 'unknown';
    }

    public function label(): string
    {
        return __('app.enums.discount_types.' . $this->name);
    }
}
