<?php

namespace App\Enums;

enum TradeDocVatMethod: int
{
    use EnumHelper;

    case BASE_ON_NET = 1;
    case BASE_ON_GROSS = 2;



    public static function getLabelFor($key): string
    {
        return __('app.enums.vat_method.' . $key) ?? 'unknown';
    }

    public static function toArrayWithLabels(): array
    {
        $source = [];
        array_map(function ($idx) use (&$source) {
            $source[$idx] = self::tryFrom($idx)->label();
        }, array_keys(self::toArray()));

        return $source;
    }

    public function label()
    {
        return self::getLabelFor($this->name);
    }
}
