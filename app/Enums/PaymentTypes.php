<?php

namespace App\Enums;

enum PaymentTypes: int
{
    use EnumHelper;
    case CASH = 1;
    case BANK_TRANSFER = 2;
    case PREPAID = 3;
    case CASH_ON_DELIVERY = 4;


    public function label()
    {
        return __('app.enums.payment_types.' . $this->name);
    }

    public static function toArrayWithLabels(): array
    {
        $source = [];
        array_map(function ($idx) use (&$source) {
            $source[$idx] = self::tryFrom($idx)->label();
        }, array_keys(self::toArray()));

        return $source;
    }
}
