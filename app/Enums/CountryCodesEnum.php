<?php

namespace App\Enums;

enum CountryCodesEnum: string
{
    use EnumHelper;

    case US = "United States";
    case CA = "Canada";
    case AF = "Afghanistan";
    case AX = "Åland Islands";
    case AL = "Albania";
    case DZ = "Algeria";
    case AS = "American Samoa";
    case AD = "Andorra";
    case AO = "Angola";
    case AI = "Anguilla";
    case AQ = "Antarctica";
    case AG = "Antigua and Barbuda";
    case AR = "Argentina";
    case AM = "Armenia";
    case AW = "Aruba";
    case AU = "Australia";
    case AT = "Austria";
    case AZ = "Azerbaijan";
    case BS = "Bahamas";
    case BH = "Bahrain";
    case BD = "Bangladesh";
    case BB = "Barbados";
    case BY = "Belarus";
    case BE = "Belgium";
    case BZ = "Belize";
    case BJ = "Benin";
    case BM = "Bermuda";
    case BT = "Bhutan";
    case BO = "Bolivia";
    case BA = "Bosnia and Herzegovina";
    case BW = "Botswana";
    case BV = "Bouvet Island";
    case BR = "Brazil";
    case IO = "British Indian Ocean Territory";
    case BN = "Brunei Darussalam";
    case BG = "Bulgaria";
    case BF = "Burkina Faso";
    case BI = "Burundi";
    case KH = "Cambodia";
    case CM = "Cameroon";
    case CV = "Cape Verde";
    case KY = "Cayman Islands";
    case CF = "Central African Republic";
    case TD = "Chad";
    case CL = "Chile";
    case CN = "China";
    case CX = "Christmas Island";
    case CC = "Cocos (Keeling) Islands";
    case CO = "Colombia";
    case KM = "Comoros";
    case CG = "Congo";
    case CD = "Congo; The Democratic Republic of The";
    case CK = "Cook Islands";
    case CR = "Costa Rica";
    case CI = "Cote D'ivoire";
    case HR = "Croatia";
    case CU = "Cuba";
    case CY = "Cyprus";
    case CZ = "Czechia";
    case DK = "Denmark";
    case DJ = "Djibouti";
    case DM = "Dominica";
    case DO = "Dominican Republic";
    case EC = "Ecuador";
    case EG = "Egypt";
    case SV = "El Salvador";
    case GQ = "Equatorial Guinea";
    case ER = "Eritrea";
    case EE = "Estonia";
    case ET = "Ethiopia";
    case FK = "Falkland Islands (Malvinas)";
    case FO = "Faroe Islands";
    case FJ = "Fiji";
    case FI = "Finland";
    case FR = "France";
    case GF = "French Guiana";
    case PF = "French Polynesia";
    case TF = "French Southern Territories";
    case GA = "Gabon";
    case GM = "Gambia";
    case GE = "Georgia";
    case DE = "Germany";
    case GH = "Ghana";
    case GI = "Gibraltar";
    case GR = "Greece";
    case GL = "Greenland";
    case GD = "Grenada";
    case GP = "Guadeloupe";
    case GU = "Guam";
    case GT = "Guatemala";
    case GG = "Guernsey";
    case GN = "Guinea";
    case GW = "Guinea-bissau";
    case GY = "Guyana";
    case HT = "Haiti";
    case HM = "Heard Island and Mcdonald Islands";
    case VA = "Holy See (Vatican City State)";
    case HN = "Honduras";
    case HK = "Hong Kong";
    case HU = "Hungary";
    case IS = "Iceland";
    case IN = "India";
    case ID = "Indonesia";
    case IR = "Iran; Islamic Republic of";
    case IQ = "Iraq";
    case IE = "Ireland";
    case IM = "Isle of Man";
    case IL = "Israel";
    case IT = "Italy";
    case JM = "Jamaica";
    case JP = "Japan";
    case JE = "Jersey";
    case JO = "Jordan";
    case KZ = "Kazakhstan";
    case KE = "Kenya";
    case KI = "Kiribati";
    case KP = "Korea; Democratic People's Republic of";
    case KR = "Korea; Republic of";
    case KW = "Kuwait";
    case KG = "Kyrgyzstan";
    case LA = "Lao People's Democratic Republic";
    case LV = "Latvia";
    case LB = "Lebanon";
    case LS = "Lesotho";
    case LR = "Liberia";
    case LY = "Libya";
    case LI = "Liechtenstein";
    case LT = "Lithuania";
    case LU = "Luxembourg";
    case MO = "Macao";
    case MG = "Madagascar";
    case MW = "Malawi";
    case MY = "Malaysia";
    case MV = "Maldives";
    case ML = "Mali";
    case MT = "Malta";
    case MH = "Marshall Islands";
    case MQ = "Martinique";
    case MR = "Mauritania";
    case MU = "Mauritius";
    case YT = "Mayotte";
    case MX = "Mexico";
    case FM = "Micronesia; Federated States of";
    case MD = "Moldova; Republic of";
    case MC = "Monaco";
    case MN = "Mongolia";
    case ME = "Montenegro";
    case MS = "Montserrat";
    case MA = "Morocco";
    case MZ = "Mozambique";
    case MM = "Myanmar";
    case NA = "Namibia";
    case NR = "Nauru";
    case NP = "Nepal";
    case NL = "Netherlands";
    case NC = "New Caledonia";
    case NZ = "New Zealand";
    case NI = "Nicaragua";
    case NE = "Niger";
    case NG = "Nigeria";
    case NU = "Niue";
    case NF = "Norfolk Island";
    case MP = "Northern Mariana Islands";
    case NO = "Norway";
    case OM = "Oman";
    case PK = "Pakistan";
    case PW = "Palau";
    case PS = "Palestine; State of";
    case PA = "Panama";
    case PG = "Papua New Guinea";
    case PY = "Paraguay";
    case PE = "Peru";
    case PH = "Philippines";
    case PN = "Pitcairn";
    case PL = "Poland";
    case PT = "Portugal";
    case PR = "Puerto Rico";
    case QA = "Qatar";
    case RE = "Reunion";
    case RO = "Romania";
    case RU = "Russian Federation";
    case RW = "Rwanda";
    case BL = "Saint Barthelemy";
    case SH = "Saint Helena; Ascension and Tristan Da Cunha";
    case KN = "Saint Kitts and Nevis";
    case LC = "Saint Lucia";
    case MF = "Saint Martin (French Part)";
    case PM = "Saint Pierre and Miquelon";
    case VC = "Saint Vincent and The Grenadines";
    case WS = "Samoa";
    case SM = "San Marino";
    case ST = "Sao Tome and Principe";
    case SA = "Saudi Arabia";
    case SN = "Senegal";
    case RS = "Serbia";
    case SC = "Seychelles";
    case SL = "Sierra Leone";
    case SG = "Singapore";
    case SX = "Sint Maarten (Dutch Part)";
    case SK = "Slovakia";
    case SI = "Slovenia";
    case SB = "Solomon Islands";
    case SO = "Somalia";
    case ZA = "South Africa";
    case GS = "South Georgia and The South Sandwich Islands";
    case SS = "South Sudan";
    case ES = "Spain";
    case LK = "Sri Lanka";
    case SD = "Sudan";
    case SR = "Suriname";
    case SJ = "Svalbard and Jan Mayen";
    case SE = "Sweden";
    case CH = "Switzerland";
    case SY = "Syrian Arab Republic";
    case TW = "Taiwan; Province of China";
    case TJ = "Tajikistan";
    case TZ = "Tanzania; United Republic of";
    case TH = "Thailand";
    case TL = "Timor-leste";
    case TG = "Togo";
    case TK = "Tokelau";
    case TO = "Tonga";
    case TT = "Trinidad and Tobago";
    case TN = "Tunisia";
    case TR = "Turkey";
    case TM = "Turkmenistan";
    case TC = "Turks and Caicos Islands";
    case TV = "Tuvalu";
    case UG = "Uganda";
    case UA = "Ukraine";
    case AE = "United Arab Emirates";
    case GB = "United Kingdom";
    case UM = "United States Minor Outlying Islands";
    case UY = "Uruguay";
    case UZ = "Uzbekistan";
    case VU = "Vanuatu";
    case VE = "Venezuela; Bolivarian Republic of";
    case VN = "Viet Nam";
    case VG = "Virgin Islands; British";
    case VI = "Virgin Islands; U.S.";
    case WF = "Wallis and Futuna";
    case EH = "Western Sahara";
    case YE = "Yemen";
    case ZM = "Zambia";
    case ZW = "Zimbabwe";

    public function label()
    {
        return $this->value;
    }
}
