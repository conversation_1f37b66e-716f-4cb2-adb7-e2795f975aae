<?php

namespace App\Enums;

use App\Enums\EnumHelper;

enum AccountingTypesPL: int
{
    use EnumHelper;

    /**
     * @private FULL pełna księgowość
     * @private IOR księga przychodów i rozchodów
     * @private IR ewidencjas przychodów
     * @private NONE brak
     */
    case FULL = 1;
    case IOR = 2;
    case IR = 3;
    case NONE = 4;

    public function label(): string
    {
        $names = [
            1 => 'Pełna księgowość',
            2 => 'Księga przychodów i rozchodów',
            3 => 'Ewidencjas przychodów',
            4 => 'brak',
        ];

        return $names[$this->value] ?? 'brak';
    }

    public static function toArrayWithLabels(): array
    {
        return array_combine(
            array_column(self::cases(), 'value'),
            array_map(fn($key) => $key->label(), self::cases())
        );
    }
}
