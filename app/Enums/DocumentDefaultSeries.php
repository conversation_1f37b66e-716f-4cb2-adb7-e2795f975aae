<?php

namespace App\Enums;

enum DocumentDefaultSeries: string
{

    use EnumHelper;

    case PZ = 'PZ/%L%/%YYYY%||cy';
    case PW = 'PW/%L%/%YYYY%||cy';
    case ZW = 'ZW/%L%/%YYYY%||cy';
    case MP = 'MP/%L%/%YYYY%||cy';
    case WZ = 'WZ/%L%/%YYYY%||cy';
    case RW = 'RW/%L%/%YYYY%||cy';
    case MW = 'MW/%L%/%YYYY%||cy';

    public function getSwitch(): string
    {
        return explode('||', $this->value)[1];
    }

    public function getPattern(): string
    {
        return explode('||', $this->value)[0];
    }
}
