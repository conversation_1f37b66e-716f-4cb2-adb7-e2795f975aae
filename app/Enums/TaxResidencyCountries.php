<?php

namespace App\Enums;

use Illuminate\Support\Str;

enum TaxResidencyCountries
{
    use EnumHelper;

    case PL;
    case AT;
    case BE;
    case BG;
    case CY;
    case CZ;
    case DK;
    case EE;
    case FI;
    case FR;
    case DE;
    case EL;
    case HR;
    case HU;
    case IE;
    case IT;
    case LV;
    case LT;
    case LU;
    case MT;
    case NL;
    case PT;
    case RO;
    case SK;
    case SI;
    case ES;
    case SE;
    case XI;


    public function label(): string
    {
        $names = [
            'AT' => 'AUSTRIA',
            'BE' => 'BELGIA',
            'BG' => 'BUŁGARIA',
            'CY' => 'CYPR',
            'CZ' => 'CZECHY',
            'DK' => 'DANIA',
            'EE' => 'ESTONIA',
            'FI' => 'FINLANDIA',
            'FR' => 'FRANCJA',
            'DE' => 'NIEMCY',
            'EL' => 'GRECJA',
            'HR' => 'CHORWACJA',
            'HU' => 'WĘGRY',
            'IE' => 'IRLANDIA',
            'IT' => 'WŁOCHY',
            'LV' => 'ŁOTWA',
            'LT' => 'LITWA',
            'LU' => 'LUKSEMBURG',
            'MT' => 'MALTA',
            'NL' => 'HOLANDIA',
            'PL' => 'POLSKA',
            'PT' => 'PORTUGALIA',
            'RO' => 'RUMUNIA',
            'SK' => 'SŁOWACJA',
            'SI' => 'SŁOWENIA',
            'ES' => 'HISZPANIA',
            'SE' => 'SZWECJA',
            'XI' => 'IRLANDIA PÓŁNOCNA',
        ];
        return Str::title($names[$this->name]) ?? 'brak';
    }

    public static function toArrayWithLabels(): array
    {
        return array_combine(
            array_column(self::cases(), 'name'),
            array_map(fn($key) => $key->label(), self::cases())
        );
    }
}
