<?php

namespace App\Enums;

use Illuminate\Contracts\Database\Eloquent\Castable;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class MoneyVOCast implements CastsAttributes
{

    public function get(Model $model, string $key, mixed $value, array $attributes)
    {
        return number_format($value / 100, 2, '.', '');
    }

    public function set(Model $model, string $key, mixed $value, array $attributes)
    {
        return (int) round(($value * 100), 0);
    }
}
