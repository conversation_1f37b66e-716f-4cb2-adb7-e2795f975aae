<?php

namespace App\Enums;

use Illuminate\Support\Str;

trait EnumHelper
{
    public static function toArray()
    {
        return array_column(self::cases(), 'name', 'value');
    }

    public static function toArrayWithLabels(): array
    {
//        $source = self::toArray();
        $source = [];
        array_map(function ($idx) use (&$source) {
            $source[$idx] = self::get_label($idx);
        }, array_keys(self::toArray()));

        return $source;
    }

    public function label()
    {
        return self::get_label($this->value);
    }

    //phpcs:ignore
    public static function get_label($key): string
    {
        return self::getLabelFor($key);
    }

    public static function tryFromName(string $caseName)
    {
        try {
            $class = self::class;
            return constant("$class::$caseName");
        } catch (\Error $e) {
            throw new \InvalidArgumentException("Case $caseName does not exist in enum $class.");
        }
    }

    public static function getLabelFor($key): string
    {
        return \str(self::tryFrom($key)->name ?? 'none')->replace('_', ' ')->title();
    }
}
