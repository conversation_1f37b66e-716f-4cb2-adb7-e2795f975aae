<?php

namespace App\Enums;

class VatRates
{
    protected static $rates = [
        0 => 0,
        'zw.' => 0,
        5 => 5,
        8 => 8,
        23 => 23,
    ];

    public static function getRates(): array
    {
        return self::$rates;
    }

    public static function getRate(string $rate): ?int
    {
        return self::$rates[$rate] ?? null;
    }

    public static function getRatesForSelect(): array
    {
        return array_combine(array_keys(self::$rates), array_keys(self::$rates));
    }
}
