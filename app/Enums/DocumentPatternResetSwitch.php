<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum DocumentPatternResetSwitch: string implements HasLabel
{
    use EnumHelper;
    case  SWITCH_YEARLY = 'cy';
    case  SWITCH_MONTHLY = 'cm';

    public function label()
    {
        return __('app.doc_series_pattern._.' . $this->name);
    }

    public function getLabel(): ?string
    {
        return $this->label();
    }
}
