<?php

namespace App\Enums;

enum TradeDocsTypes: int
{
//    use EnumHelper;
//
//    case SELL_INVOICE = 111;
//    case RECEIPT = 112;
//    case SELL_INVOICE_CORRECTION = 113;
//    case ADVANCE_PREPAYMENT_INVOICE = 114;
//    case PROFORMA_INVOICE = 115;
//    case SIMPLE_INVOICE = 116;
//    case PURCHASE_INVOICE = 121;
//    case PURCHASE_INVOICE_CORRECTION = 122;
//
//    const OTHER = 130;
//
//    public function getLabel(): string
//    {
//        return $this->label();
//    }
//
//
//    public static function get_label($key): string
//    {
//        $names = [
//            111 => __('app.enums.trade_docs_types.SELL_INVOICE'),
//            112 => __('app.enums.trade_docs_types.RECEIPT'),
//            113 => __('app.enums.trade_docs_types.SELL_INVOICE_CORRECTION'),
//            114 => __('app.enums.trade_docs_types.ADVANCE_PREPAYMENT_INVOICE'),
//            115 => __('app.enums.trade_docs_types.PROFORMA_INVOICE'),
//            116 => __('app.enums.trade_docs_types.SIMPLE_INVOICE'),
//            121 => __('app.enums.trade_docs_types.PURCHASE_INVOICE'),
//            122 => __('app.enums.trade_docs_types.PURCHASE_INVOICE_CORRECTION'),
//            130 => __('app.enums.trade_docs_types.OTHER'),
//        ];
//
//        return $names[$key] ?? 'none';
//    }
//
//    public function label(): string
//    {
//        return __('app.enums.trade_docs_types.' . $this->name);
//    }
}
