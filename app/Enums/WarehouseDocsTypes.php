<?php

namespace App\Enums;

enum WarehouseDocsTypes: int
{

    use EnumHelper;

    case PZ = 11;
    case PW = 12;
    case ZW = 13;
    case MP = 15;
    case WZ = 21;
    case RW = 22;
    case MW = 25;


    public static function toArray()
    {
        return array_column(self::cases(), 'name', 'value');
    }

    public static function hidden()
    {
//        return [self::MP->value];
        return [];
    }

    public static function toArrayWithLabels(): array
    {
        $source = [];
        array_map(function ($idx) use (&$source) {
            if (in_array($idx, self::hidden())) {
                return;
            }
            $source[$idx] = self::_get_label($idx);
        }, array_keys(self::toArray()));

        return $source;
    }

    public function label()
    {
        return self::get_label($this->value);
    }

    public static function get_label($key)
    {
        $names = [
            11 => 'PZ Przyjęcie zewnętrzne',
            12 => 'PW Przyjęcie wewnętrzne',
            13 => 'Z<PERSON> Zwrot wewnętrzny',
            15 => 'MM+ Przesunięcie MM Przyjęcie',
            21 => 'WZ Wydanie zewnętrzne',
            22 => 'RW Rozchód wewnętrzne',
            25 => 'MM- Przesunięcie MM Wydanie',
        ];

        return $names[$key] ?? 'none';
    }
}
