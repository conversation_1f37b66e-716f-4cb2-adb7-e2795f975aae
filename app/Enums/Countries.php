<?php

namespace App\Enums;

enum Countries: string
{
    use EnumHelper;

    case PL = "Polska";
    case CA = "Kanada";
    case AF = "Afganistan";
    case AX = "Wyspy Alandzkie";
    case AL = "Albania";
    case DZ = "Algieria";
    case AS = "Samoa Amerykańskie";
    case AD = "Andora";
    case AO = "Angola";
    case AI = "Anguilla";
    case AQ = "Antarktyda";
    case AG = "Antigua i Barbuda";
    case AR = "Argentyna";
    case AM = "Armenia";
    case AW = "Aruba";
    case AU = "Australia";
    case AT = "Austria";
    case AZ = "Azerbejdżan";
    case BS = "Bahamy";
    case BH = "Bahrajn";
    case BD = "Bangladesz";
    case BB = "Barbados";
    case BY = "Białoruś";
    case BE = "Belgia";
    case BZ = "Belize";
    case BJ = "Benin";
    case BM = "Bermudy";
    case BT = "Bhutan";
    case BO = "Boliwia";
    case BA = "Bośnia i Hercegowina";
    case BW = "Botswana";
    case BV = "Wyspa Bouveta";
    case BR = "Brazylia";
    case IO = "Brytyjskie Terytorium Oceanu Indyjskiego";
    case BN = "Brunei";
    case BG = "Bułgaria";
    case BF = "Burkina Faso";
    case BI = "Burundi";
    case KH = "Kambodża";
    case CM = "Kamerun";
    case CV = "Republika Zielonego Przylądka";
    case KY = "Kajmany";
    case CF = "Republika Środkowoafrykańska";
    case TD = "Czad";
    case CL = "Chile";
    case CN = "Chiny";
    case CX = "Wyspa Bożego Narodzenia";
    case CC = "Wyspy Kokosowe";
    case CO = "Kolumbia";
    case KM = "Komory";
    case CG = "Kongo";
    case CD = "Demokratyczna Republika Konga";
    case CK = "Wyspy Cooka";
    case CR = "Kostaryka";
    case CI = "Wybrzeże Kości Słoniowej";
    case HR = "Chorwacja";
    case CU = "Kuba";
    case CY = "Cypr";
    case CZ = "Czechy";
    case DK = "Dania";
    case DJ = "Dżibuti";
    case DM = "Dominika";
    case DO = "Republika Dominikańska";
    case EC = "Ekwador";
    case EG = "Egipt";
    case SV = "Salwador";
    case GQ = "Gwinea Równikowa";
    case ER = "Erytrea";
    case EE = "Estonia";
    case ET = "Etiopia";
    case FK = "Falklandy";
    case FO = "Wyspy Owcze";
    case FJ = "Fidżi";
    case FI = "Finlandia";
    case FR = "Francja";
    case GF = "Gujana Francuska";
    case PF = "Polinezja Francuska";
    case TF = "Francuskie Terytoria Południowe";
    case GA = "Gabon";
    case GM = "Gambia";
    case GE = "Gruzja";
    case DE = "Niemcy";
    case GH = "Ghana";
    case GI = "Gibraltar";
    case GR = "Grecja";
    case GL = "Grenlandia";
    case GD = "Grenada";
    case GP = "Gwadelupa";
    case GU = "Guam";
    case GT = "Gwatemala";
    case GG = "Guernsey";
    case GN = "Gwinea";
    case GW = "Gwinea Bissau";
    case GY = "Gujana";
    case HT = "Haiti";
    case HM = "Wyspy Heard i McDonalda";
    case VA = "Watykan";
    case HN = "Honduras";
    case HK = "Hongkong";
    case HU = "Węgry";
    case IS = "Islandia";
    case IN = "Indie";
    case ID = "Indonezja";
    case IR = "Iran";
    case IQ = "Irak";
    case IE = "Irlandia";
    case IM = "Wyspa Man";
    case IL = "Izrael";
    case IT = "Włochy";
    case JM = "Jamajka";
    case JP = "Japonia";
    case JE = "Jersey";
    case JO = "Jordania";
    case KZ = "Kazachstan";
    case KE = "Kenia";
    case KI = "Kiribati";
    case KP = "Korea Północna";
    case KR = "Korea Południowa";
    case KW = "Kuwejt";
    case KG = "Kirgistan";
    case LA = "Laos";
    case LV = "Łotwa";
    case LB = "Liban";
    case LS = "Lesotho";
    case LR = "Liberia";
    case LY = "Libia";
    case LI = "Liechtenstein";
    case LT = "Litwa";
    case LU = "Luksemburg";
    case MO = "Makau";
    case MG = "Madagaskar";
    case MW = "Malawi";
    case MY = "Malezja";
    case MV = "Malediwy";
    case ML = "Mali";
    case MT = "Malta";
    case MH = "Wyspy Marshalla";
    case MQ = "Martynika";
    case MR = "Mauretania";
    case MU = "Mauritius";
    case YT = "Majotta";
    case MX = "Meksyk";
    case FM = "Mikronezja";
    case MD = "Mołdawia";
    case MC = "Monako";
    case MN = "Mongolia";
    case ME = "Czarnogóra";
    case MS = "Montserrat";
    case MA = "Maroko";
    case MZ = "Mozambik";
    case MM = "Mjanma";
    case NA = "Namibia";
    case NR = "Nauru";
    case NP = "Nepal";
    case NL = "Holandia";
    case NC = "Nowa Kaledonia";
    case NZ = "Nowa Zelandia";
    case NI = "Nikaragua";
    case NE = "Niger";
    case NG = "Nigeria";
    case NU = "Niue";
    case NF = "Wyspa Norfolk";
    case MP = "Mariany Północne";
    case NO = "Norwegia";
    case OM = "Oman";
    case PK = "Pakistan";
    case PW = "Palau";
    case PS = "Palestyna";
    case PA = "Panama";
    case PG = "Papua-Nowa Gwinea";
    case PY = "Paragwaj";
    case PE = "Peru";
    case PH = "Filipiny";
    case PN = "Pitcairn";
    case PT = "Portugalia";
    case PR = "Portoryko";
    case QA = "Katar";
    case RE = "Reunion";
    case RO = "Rumunia";
    case RU = "Rosja";
    case RW = "Rwanda";
    case BL = "Saint-Barthélemy";
    case SH = "Wyspa Świętej Heleny, Wyspa Wniebowstąpienia i Tristan da Cunha";
    case KN = "Saint Kitts i Nevis";
    case LC = "Saint Lucia";
    case MF = "Saint-Martin";
    case PM = "Saint-Pierre i Miquelon";
    case VC = "Saint Vincent i Grenadyny";
    case WS = "Samoa";
    case SM = "San Marino";
    case ST = "Wyspy Świętego Tomasza i Książęca";
    case SA = "Arabia Saudyjska";
    case SN = "Senegal";
    case RS = "Serbia";
    case SC = "Seszele";
    case SL = "Sierra Leone";
    case SG = "Singapur";
    case SX = "Sint Maarten";
    case SK = "Słowacja";
    case SI = "Słowenia";
    case SB = "Wyspy Salomona";
    case SO = "Somalia";
    case ZA = "Południowa Afryka";
    case GS = "Georgia Południowa i Sandwich Południowy";
    case SS = "Sudan Południowy";
    case ES = "Hiszpania";
    case LK = "Sri Lanka";
    case SD = "Sudan";
    case SR = "Surinam";
    case SJ = "Svalbard i Jan Mayen";
    case SE = "Szwecja";
    case CH = "Szwajcaria";
    case SY = "Syria";
    case TW = "Tajwan";
    case TJ = "Tadżykistan";
    case TZ = "Tanzania";
    case TH = "Tajlandia";
    case TL = "Timor Wschodni";
    case TG = "Togo";
    case TK = "Tokelau";
    case TO = "Tonga";
    case TT = "Trynidad i Tobago";
    case TN = "Tunezja";
    case TR = "Turcja";
    case TM = "Turkmenistan";
    case TC = "Turks i Caicos";
    case TV = "Tuvalu";
    case UG = "Uganda";
    case UA = "Ukraina";
    case AE = "Zjednoczone Emiraty Arabskie";
    case GB = "Wielka Brytania";
    case US = "Stany Zjednoczone";
    case UM = "Dalekie Wyspy Mniejsze Stanów Zjednoczonych";
    case UY = "Urugwaj";
    case UZ = "Uzbekistan";
    case VU = "Vanuatu";
    case VE = "Wenezuela";
    case VN = "Wietnam";
    case VG = "Brytyjskie Wyspy Dziewicze";
    case VI = "Wyspy Dziewicze Stanów Zjednoczonych";
    case WF = "Wallis i Futuna";
    case EH = "Sahara Zachodnia";
    case YE = "Jemen";
    case ZM = "Zambia";
    case ZW = "Zimbabwe";

    public static function toArray()
    {
        return array_column(self::cases(), 'value', 'name');
    }
}
