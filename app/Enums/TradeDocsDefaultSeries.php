<?php

namespace App\Enums;

enum TradeDocsDefaultSeries: string
{

    use EnumHelper;

    case SELL_INVOICE = 'INV/%L%/%YYYY%||cy';
    case RECEIPT = 'PAR/%L%/%YYYY%||cy';
    case SELL_INVOICE_CORRECTION = 'INVCOR/%L%/%YYYY%||cy';
    case ADVANCE_PREPAYMENT_INVOICE = 'INVAP/%L%/%YYYY%||cy';
    case PROFORMA_INVOICE = 'INVPRO/%L%/%YYYY%||cy';

    public function getSwitch(): string
    {
        return explode('||', $this->value)[1];
    }

    public function getPattern(): string
    {
        return explode('||', $this->value)[0];
    }
}
