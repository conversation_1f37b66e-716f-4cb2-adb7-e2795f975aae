<?php
declare(strict_types=1);

namespace App\Enums;

enum DocumentTypes: int
{
    use EnumHelper;

    #[DocDomainAttribute(DocumentGeneralTypes::WAREHOUSE)]
    case PZ = 11;

    #[DocDomainAttribute(DocumentGeneralTypes::WAREHOUSE)]
    case PW = 12;

    #[DocDomainAttribute(DocumentGeneralTypes::WAREHOUSE)]
    case ZW = 13;

    #[DocDomainAttribute(DocumentGeneralTypes::WAREHOUSE)]
    case MP = 15;

    #[DocDomainAttribute(DocumentGeneralTypes::WAREHOUSE)]
    case WZ = 21;

    #[DocDomainAttribute(DocumentGeneralTypes::WAREHOUSE)]
    case RW = 22;

    #[DocDomainAttribute(DocumentGeneralTypes::WAREHOUSE)]
    case MW = 25;

    #[DocDomainAttribute(DocumentGeneralTypes::TRADE)]
    #[\SellsTypeDoc]
    #[\VATDocType]
    case FVS = 111; //fvs

    #[DocDomainAttribute(DocumentGeneralTypes::TRADE)]
    #[\SellsTypeDoc]
    #[\VATDocType]
    case FVK = 113; //fvc

    #[DocDomainAttribute(DocumentGeneralTypes::TRADE)]
    #[\SellsTypeDoc]
    #[\VATDocType]
    case FVP = 114; //fvp

    #[DocDomainAttribute(DocumentGeneralTypes::TRADE)]
    #[\SellsTypeDoc]
    #[\NotVATDocType]
    case FAUP = 116; //fa

//    #[DocDomainAttribute(DocumentGeneralTypes::TRADE)]
//    #[\SellsTypeDoc]
//    case PAR = 112; //par

    #[DocDomainAttribute(DocumentGeneralTypes::TRADE)]
    #[\SellsTypeDoc]
    #[\VATDocType]
    case PROF = 115; //prof

    #[DocDomainAttribute(DocumentGeneralTypes::PURCHASE)]
    #[\PurchaseTypeDoc]
    case FZV = 121; //fra VAT zakupu

    #[DocDomainAttribute(DocumentGeneralTypes::PURCHASE)]
    #[\PurchaseTypeDoc]
    case FZK = 123; // korekta fry zakupu

    #[DocDomainAttribute(DocumentGeneralTypes::PURCHASE)]
    #[\PurchaseTypeDoc]
    case FZP = 124; // fra przedplata

    #[DocDomainAttribute(DocumentGeneralTypes::PURCHASE)]
    #[\PurchaseTypeDoc]
    case FZUP = 126; // faktura bez vat


    public static function getDocTypesOf(DocumentGeneralTypes $type): array
    {
        $ref = new \ReflectionClass(self::class);
        $docTypes = [];
        foreach ($ref->getReflectionConstants() as $name => $value) {
            array_map(
                static function (\ReflectionAttribute $attr) use ($type, &$docTypes, $value) {
                    if ($attr->newInstance()->domain === $type) {
                        $docTypes[] = $value->getValue();
                    }
                },
                $value->getAttributes(DocDomainAttribute::class)
            );
        }
        return $docTypes;
    }

    /**
     * @param string $subtype 'SellsDocType' or 'NotVATDocType' or 'VATDocType' or 'PurchaseTypeDoc'
     * @return self[]
     */
    public static function getSellsTypeDocs(string $subtype = 'SellsTypeDoc'): array
    {
        $ref = new \ReflectionClass(self::class);
        $docTypes = [];
        foreach ($ref->getReflectionConstants() as $name => $value) {
            if (filled($value->getAttributes($subtype))) {
                $docTypes[] = $value->getValue();
            }
        }
        return $docTypes;
    }


    public static function getTradeDocTypes(): array
    {
        $ref = new \ReflectionClass(self::class);
        $docTypes = [];
        foreach ($ref->getReflectionConstants() as $name => $value) {
            $attr = $value->getAttributes(DocDomainAttribute::class);
            if (count($attr) === 0) {
                continue;
            }
            if ($attr[0]->newInstance()->domain === 'trade') {
                $docTypes[] = $value->getValue();
            }
        }
        return $docTypes;
    }

    public function label()
    {
        return self::get_label($this->value);
    }

    public function getGeneralType(): ?DocumentGeneralTypes
    {
        $gtl = (new \ReflectionClass($this))
            ->getReflectionConstant($this->name)
            ->getAttributes(DocDomainAttribute::class);

        return $gtl[0]?->newInstance()->domain;
    }

    public function isGeneralType(DocumentGeneralTypes $type): bool
    {
        return $this->getGeneralType() === $type;
    }

    public function isSellsType(): bool
    {
        $gtl = (new \ReflectionClass($this))
            ->getReflectionConstant($this->name)
            ->getAttributes('SellsTypeDoc');

        return count($gtl) > 0;
    }

    public function isPurchaseType(): bool
    {
        $gtl = (new \ReflectionClass($this))
            ->getReflectionConstant($this->name)
            ->getAttributes('PurchaseTypeDoc');

        return count($gtl) > 0;
    }

    public function getDefaultSeriesPatternList(): ?string
    {
        $defaultSeries = [
            'FVS' => "FV/%L%/%MM%/%YYYY%||cy",
            'FVK' => "FVK/%L%/%YYYY%||cy",
            'FVP' => "FVP/%L%/%YYYY%||cy",
            'FAUP' => "FA/%L%/%YYYY%||cy",
            'PROF' => "PROF/%L%/%YYYY%||cy",
            'PZ' => "PZ/%L%/%YYYY%||cy",
            'PW' => "PW/%L%/%YYYY%||cy",
            'ZW' => "ZW/%L%/%YYYY%||cy",
            'MP' => "MP/%L%/%YYYY%||cy",
            'WZ' => "WZ/%L%/%YYYY%||cy",
            'RW' => "RW/%L%/%YYYY%||cy",
            'MW' => "MW/%L%/%YYYY%||cy",
        ];

        return $defaultSeries[$this->name] ?? null;
    }

    public function getDefaultSeriesSwitch(): ?string
    {
        return match ($pattern = $this->getDefaultSeriesPatternList()) {
            null => null,
            default => explode('||', $pattern)[1],
        };
    }

    public function getDefaultSeriesPattern(): ?string
    {
        return match ($pattern = $this->getDefaultSeriesPatternList()) {
            null => null,
            default => explode('||', $pattern)[0],
        };
    }

    //phpcs:ignore
    public static function get_label($key)
    {
        $names = [
            11 => __('app.enums.docs_types.PZ'),
            12 => __('app.enums.docs_types.PW'),
            13 => __('app.enums.docs_types.ZW'),
            15 => __('app.enums.docs_types.MP'),
            21 => __('app.enums.docs_types.WZ'),
            22 => __('app.enums.docs_types.RW'),
            25 => __('app.enums.docs_types.MW'),
            111 => __('app.enums.docs_types.FVS'),
            112 => __('app.enums.docs_types.PAR'),
            113 => __('app.enums.docs_types.FVK'),
            114 => __('app.enums.docs_types.FVP'),
            115 => __('app.enums.docs_types.PROF'),
            116 => __('app.enums.docs_types.FAUP'),
            121 => __('app.enums.docs_types.FZV'),
            123 => __('app.enums.docs_types.FZK'),
            124 => __('app.enums.docs_types.FZP'),
            126 => __('app.enums.docs_types.FZUP'),
            130 => __('app.enums.docs_types.OTHER'),
        ];

        return $names[$key] ?? 'none';
    }
}
