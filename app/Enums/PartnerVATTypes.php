<?php

namespace App\Enums;

enum PartnerVATTypes: int
{

    use EnumHelper;

    case LOCAL = 1;
    case EU = 2;
    case NONEU = 3;
    case EU3P = 4;
    case OSSRoutine = 5;
    case NOTVAT = 6;


    public static function get_label($key)
    {
        $names = [
            1 => 'krajowy',
            2 => 'wspólnota EU',
            3 => 'poza EU',
            4 => 'EU trójstronny',
            5 => 'procedura OSS',
            6 => 'nie jest płatnikiem VAT',
        ];

        return $names[$key] ?? 'none';
    }
}
