<?php

namespace App\Enums;

enum WarehouseItemTypes: int
{
    use EnumHelper;
    case PRODUCT = 1;
    case SERVICE = 2;
    case OTHER = 3;

    public function label(): string
    {
        $labels = [
            1 => 'Produkt',
            2 => 'Usługa',
            3 => 'Inne',
        ];
        return __('app.enums.warehouse_item_types.' . $this->name) ?? $labels[$this->value] ?? 'none';
    }

    public static function toArrayWithLabels(): array
    {
        $source = [];
        array_map(function ($idx) use (&$source) {
            $source[$idx->value] = $idx->label();
        }, self::cases());
        return $source;
    }
}
