<?php

namespace App\Observers;

use App\Models\DailySnapshots;

class DailySnapshot
{
    /**
     * Handle the DailySnapshots "created" event.
     */
    public function created(DailySnapshots $dailySnapshots): void
    {
        //
    }

    /**
     * Handle the DailySnapshots "updated" event.
     */
    public function updated(DailySnapshots $dailySnapshots): void
    {
        //
    }

    /**
     * Handle the DailySnapshots "deleted" event.
     */
    public function deleted(DailySnapshots $dailySnapshots): void
    {
        //
    }

    /**
     * Handle the DailySnapshots "restored" event.
     */
    public function restored(DailySnapshots $dailySnapshots): void
    {
        //
    }

    /**
     * Handle the DailySnapshots "force deleted" event.
     */
    public function forceDeleted(DailySnapshots $dailySnapshots): void
    {
        //
    }

    public function creating(DailySnapshots $dailySnapshots)
    {
       //
    }
}
