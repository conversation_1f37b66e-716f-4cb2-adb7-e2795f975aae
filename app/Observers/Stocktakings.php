<?php

namespace App\Observers;

use App\Filament\Filters\WHSelectFilter;
use App\Models\StocktakingItems;
use App\Repositories\StocktakingRepository;

class Stocktakings
{
    /**
     * Handle the StocktakingItems "creating" event.
     */
    public function creating(StocktakingItems $item)
    {
        //
        if (app()->get('startStocktaking')) {
            $warehouse = WHSelectFilter::deconstructCompoundIndex($item->warehouse_id);
            $warehouse_id = $warehouse['id'];
            $repo = new StocktakingRepository(new StocktakingItems(), $warehouse_id);
            $repo->create();
            return false;
        }
    }

    /**
     * Handle the StocktakingItems "created" event.
     */
    public function created(StocktakingItems $item): void
    {
        //
    }

    /**
     * Handle the StocktakingItems "updating" event.
     */
    public function updating(StocktakingItems $item): void
    {
        if (in_array('real_amount', array_keys($item->getDirty()))) {
            $item->difference_amount = (int)$item->real_amount - (int)$item->base_amount;
        }
    }

    /**
     * Handle the StocktakingItems "updated" event.
     */
    public function updated(StocktakingItems $item): void
    {
        //
    }

    /**
     * Handle the StocktakingItems "deleted" event.
     */
    public function deleted(StocktakingItems $item): void
    {
        //
    }

    /**
     * Handle the StocktakingItems "restored" event.
     */
    public function restored(StocktakingItems $item): void
    {
        //
    }

    /**
     * Handle the StocktakingItems "force deleted" event.
     */
    public function forceDeleted(StocktakingItems $item): void
    {
        //
    }
}
