<?php

namespace App\Traits;

use App\Services\CalculateTradeDoc;
use Filament\Forms\Set;

trait CalculateItemsTrait
{

    public function calculate(Set $set): void
    {
        $calculator = CalculateTradeDoc::make($this->docItem);
        $calculator->setVatMethod($this->getOwnerRecord()->vat_method);
        $calculator->calculate();
        $this->formatResult('discounted_unit_price', $calculator->getResults()['discounted_unit_price'], $set);
        $this->formatResult('vat_value', $calculator->getResults()['vat_value'], $set);
        $this->formatResult('net_value', $calculator->getResults()['net_value'], $set);
        $this->formatResult('gross_value', $calculator->getResults()['gross_value'], $set);
        $this->formatResult('net_unit_price', $calculator->getResults()['net_unit_price'], $set);
        $this->formatResult('gross_unit_price', $calculator->getResults()['gross_unit_price'], $set);
    }

    protected function formatResult($fieldName, $result, Set $set)
    {
        $set($fieldName, number_format($result, 2, '.', ''));
    }
}
