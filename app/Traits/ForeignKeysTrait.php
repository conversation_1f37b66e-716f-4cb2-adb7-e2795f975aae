<?php

namespace App\Traits;

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Schema;

trait ForeignKeysTrait
{
    public static function createForeignKey(
        string $table,
        string $column,
        string $foreignTable,
        string $foreignColumn = 'id'
    ): void {

        $name = self::createForeignKeyName($table, [$column]);

        if (self::hasForeignKey($table, $name)) {
            return;
        }
        Schema::table($table, static function (Blueprint $table) use ($name, $column, $foreignTable, $foreignColumn) {
            $table->foreign($column)
                ->references($foreignColumn)
                ->on($foreignTable)
                ->onDelete('cascade');
        });
    }

    public static function dropForeignKey(string $table, string $name): void
    {
        if (self::hasForeignKey($table, $name)) {
            Schema::table($table, static function (Blueprint $table) use ($name) {
                Schema::disableForeignKeyConstraints();
                $table->dropForeign($name);
                Schema::enableForeignKeyConstraints();
            });
        }
        if (Schema::hasIndex($table, $name)) {
            Schema::table($table, static function (Blueprint $table) use ($name) {
                $table->dropIndex($name);
            });
        }
    }

    public static function createForeignKeyName($table, array $columns, $type = 'foreign'): string
    {
        $index = strtolower($table . '_' . implode('_', $columns) . '_' . $type);
        return str_replace(['-', '.'], '_', $index);
    }

    public static function hasForeignKey(string $table, string $name): bool
    {
        if (Arr::first(
            Schema::getForeignKeys($table),
            static fn($key) => $key['name'] === $name,
            false
        )) {
            return true;
        }
        return false;
    }
}
