<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WarehouseDocRes extends JsonResource
{

    /** @var \App\Models\WarehouseDoc */
    public $resource;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $doc = $this->resource->makeHidden([
            'id',
            'installation',
            'creator_id',
            'user_id',
            'signatory_id',
            'partner_id',
            'document_series_id',
            'target_warehouse',
            'warehouse_id',
            'target_warehouse_id',
            'issuer_id',
            'buyer_id',
            'seller_id',
            'deleted_at',
            'log'
        ])->toArray();
        $doc['user'] = $this->resource->user->name;
        $doc['signatory'] = $this->resource->signatory?->name ?? '';
        $doc['type'] = $this->resource->type->name;
        $doc['warehouse'] = $this->resource->warehouse
            ->makeHidden(['id','installation','deleted_at','owner_type','owner_identifier'])
            ->setAttribute('ownerType', $this->resource->warehouse->owner_type->name)
            ->toArray();
        $doc['targetWarehouse'] = $this->resource->target_warehouse?->makeHidden([
            'id','installation'
        ])->toArray() ?? [];
        $doc['items'] = $this->getItems();
        return $doc;
    }

    protected function getItems(): array
    {
        $this->resource->log->makeHidden([
            'installation',
            'id',
            'warehouse_id',
            'product_id',
            'document_id',
            'product_name',
            'product',
        ]);
        $logs = $this->resource->log;
        foreach ($logs as $log) {
            $log->productName = $log->product->name;
        }
        return $logs->toArray();
    }
}
