<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TradeDocRes extends JsonResource
{

    /** @var \App\Models\TradeDoc */
    public $resource;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $purdoc = $this->resource->makeHidden([
            'installation',
            'creator_id',
            'document_series_id',
            'issuer_id',
            'buyer_id',
            'seller_id',
            'deleted_at',
            'meta',
            'items'
        ])->toArray();
        $purdoc['type'] = $this->resource->type->name;
        $purdoc['payment_type'] = $this->resource->payment_type->name;
        $purdoc['vat_method'] = $this->resource->vat_method->name;
        $purdoc['meta'] = $this->getMeta();
        $purdoc['items'] = $this->getItems();
        return $purdoc;
    }

    protected function getMeta(): array
    {
        $meta = $this->resource->meta->meta;
        unset(
            $meta['buyer_address']['config'],
            $meta['buyer_address']['vat_type'],
            $meta['buyer_address']['business_type'],
            $meta['buyer_address']['tax_type'],
            $meta['buyer_address']['accounting_type'],
            $meta['buyer_address']['deleted_at'],
            $meta['seller_address']['config'],
            $meta['seller_address']['vat_type'],
            $meta['seller_address']['business_type'],
            $meta['seller_address']['tax_type'],
            $meta['seller_address']['accounting_type'],
            $meta['seller_address']['deleted_at'],
            $meta['issuer_address']['config'],
            $meta['issuer_address']['vat_type'],
            $meta['issuer_address']['business_type'],
            $meta['issuer_address']['tax_type'],
            $meta['issuer_address']['accounting_type'],
            $meta['issuer_address']['deleted_at'],
        );
        $this->resource->meta->meta = $meta;
        return $this->resource->meta->makeHidden(['id', 'tenant_id', 'deleted_at'])->toArray();
    }

    protected function getItems(): array
    {
        return $this->resource->items->makeHidden(['installation'])->toArray();
    }
}
