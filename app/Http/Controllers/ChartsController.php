<?php

namespace App\Http\Controllers;

use App\Models\PurchaseDoc;
use App\Models\TradeDoc;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection as SupportCollection;

class ChartsController extends Controller
{

    public array $colors = [
        '#36A2EB',
        'rgba(181, 44, 25, 1)',
        'rgba(255, 159, 64, 1)',
        'rgba(75, 192, 192, 1)',
        'rgba(54, 162, 235, 1)',
        'rgba(153, 102, 255, 1)',
        'rgba(255, 99, 132, 1)',
        'rgba(255, 206, 86, 1)',
        ];
    public function getData(Request $request)
    {
        return $this->updateChartData($request->all());
    }

    public function updateChartData(array $data): array
    {
        return $this->getOneDayData($data);
    }

    private function getDateRange($data): array
    {
        $now = Carbon::now();

        return match ($data['date_range']) {
            'today' => [
                'start' => $now->copy()->startOfDay(),
                'end' => $now->copy()->endOfDay(),
            ],
            'yesterday' => [
                'start' => $now->copy()->subDay()->startOfDay(),
                'end' => $now->copy()->subDay()->endOfDay(),
            ],
            'last_week' => [
                'start' => $now->copy()->subWeek()->startOfWeek(),
                'end' => $now->copy()->subWeek()->endOfWeek(),
            ],
            'this_week' => [
                'start' => $now->copy()->startOfWeek(),
                'end' => $now->copy()->endOfWeek(),
            ],
            'last_month' => [
                'start' => $now->copy()->subMonth()->startOfMonth(),
                'end' => $now->copy()->subMonth()->endOfMonth(),
            ],
            'this_month' => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
            'this_year' => [
                'start' => $now->copy()->startOfYear(),
                'end' => $now->copy()->endOfDay(),
            ],
            'last_year' => [
                'start' => $now->copy()->subYear()->startOfYear(),
                'end' => $now->copy()->subYear()->endOfYear(),
            ],
            'specific_year' => [
                'start' => Carbon::createFromDate($data['year'], 1, 1),
                'end' => Carbon::createFromDate($data['year'], 12, 31),
            ],
            'custom' => [
                'start' => Carbon::parse($data['start_date']),
                'end' => Carbon::parse($data['end_date']),
            ],
        };
    }

    private function getOneDayData($data)
    {
        $dateRange = $this->getDateRange($data);
        $intervalString = match ($data['resolution']) {
            'y' => 'P1Y',
            'm' => 'P1M',
            'd' => 'P1D'
        };

        $groupByString = match ($data['resolution']) {
            'y' => 'YEAR(issued_at)',
            'm' => 'YEAR(issued_at), MONTH(issued_at)',
            'd' => 'issued_at'
        };

        $dateFormat = match ($data['resolution']) {
            'y' => 'Y',
            'm' => 'Y-m',
            'd' => 'Y-m-d'
        };

        $sells = TradeDoc::query()
            ->whereBetween('issued_at', [
                $dateRange['start'],
                $dateRange['end'],
            ])
            ->selectRaw(
                'issued_at, SUM(CASE WHEN currency = "PLN" THEN net ELSE net/exchange_rate END) AS net'
            )
            ->groupByRaw($groupByString)
            ->orderBy('issued_at')
            ->get()
            ->mapWithKeys(fn($el) => [Carbon::parse($el->issued_at)->format($dateFormat) => $el->net]);

        $purchase = PurchaseDoc::query()
            ->whereBetween('issued_at', [
                $dateRange['start'],
                $dateRange['end'],
            ])
            ->selectRaw(
                'issued_at, SUM(CASE WHEN currency = "PLN" THEN net ELSE net/exchange_rate END) AS net'
            )
            ->groupByRaw($groupByString)
            ->orderBy('issued_at')
            ->get()
            ->mapWithKeys(fn($el) => [Carbon::parse($el->issued_at)->format($dateFormat) => $el->net]);

        $labels = CarbonPeriod::create(new \DatePeriod(
            start: $dateRange['start'],
            interval: new \DateInterval($intervalString),
            end: $dateRange['end']
        ));

        $labels = collect($labels)->map(fn($el) => $el->format($dateFormat))->toArray();
        $dataSells = [];
        $dataPurchase = [];
        $datasets = collect();
        $sellsSum = 0;
        $purchaseSum = 0;
        $pieDatasets = collect();

        foreach ($labels as $date) {
            $dataSells[$date] = $sells[$date] ?? 0;
            $dataPurchase[$date] = $purchase[$date] ?? 0;
            $sellsSum += $dataSells[$date];
            $purchaseSum += $dataPurchase[$date];
        }

        $datasets->push([
            'label' => "Sprzedaż ",
            'data' => $dataSells,
            'borderWidth' => 2,
            'borderColor' => 'rgba(153, 102, 255, 1)',
            'backgroundColor' => 'rgba(153, 102, 255, 0.8)',
            'fill' => +1,
            'indexAxis' => 'x',
            'pointStyle' => 'circle',
        ]);

        $datasets->push([
            'label' => "Zakupy",
            'data' => $dataPurchase,
            'borderWidth' => 1,
            'borderColor' => 'rgba(255, 206, 86, 1)',
            'backgroundColor' => 'rgba(255, 206, 86, 0.8)',
            'fill' => 'origin',
            'indexAxis' => 'x',
            'pointStyle' => 'rectRot',
        ]);
        $pieDatasets->push([
            'data' => [$sellsSum, $purchaseSum],
            'backgroundColor' => [
                'rgba(153, 102, 255, 0.8)',
                'rgba(255, 206, 86, 0.8)'
            ],
        ]);
        $return['lineChart'] = $this->buildChartSchema($datasets, $labels);
        $return['pieChart'] = $this->buildChartSchema($pieDatasets, ['Sprzedaż', 'Zakupy']);
        return $return;
    }

    protected function buildChartSchema(SupportCollection $datasets, array $labels, bool $parsing = false): array
    {
        return [
            'labels' => $labels,
            'parsing' => $parsing,
            'datasets' => $datasets->toArray(),
        ];
    }
}
