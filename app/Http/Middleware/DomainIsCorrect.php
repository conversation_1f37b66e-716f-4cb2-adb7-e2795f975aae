<?php

namespace App\Http\Middleware;

use Closure;
use Filament\Facades\Filament;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class DomainIsCorrect
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {

        if (blank(tenant()->system_domain) &&  config('app.domain.app') === $request->getHost()) {
            return $next($request);
        }

        if (tenant()->system_domain !== $request->getHost()) {
            tenantFlush();
            Filament::auth()->logout();
            Session::invalidate();
            return redirect(Filament::getLoginUrl())->withErrors(['data.email' => 'Niepoprawny panel']);
        }

        return $next($request);
    }
}
