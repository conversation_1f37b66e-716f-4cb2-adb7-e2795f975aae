<?php

namespace App\Http\Middleware;

use App\Scopes\NoSuperAdminInList;
use App\Scopes\OnlyTenantUsers;
use Closure;
use Filament\Facades\Filament;
use Filament\Notifications\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class UserAllowedToUse
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $tenant = auth()->user()?->getTenant();
        if ($tenant !== null && $tenant->is_active) {
            if (!defined('INSTALLATION')) {
                define('INSTALLATION', $tenant->id);
            }
            auth()->user()::addGlobalScope(new NoSuperAdminInList());
            auth()->user()::addGlobalScope(new OnlyTenantUsers());
            return $next($request);
        }
        Auth::logout();
        Notification::make()
            ->title('Błąd 426')
            ->persistent()
            ->danger()
            ->send();
        return redirect(Filament::getLoginUrl());
    }
}
