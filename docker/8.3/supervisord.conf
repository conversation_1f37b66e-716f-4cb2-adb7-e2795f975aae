[supervisord]
nodaemon=true
user=root
#logfile=/var/log/supervisor/supervisord.log
logfile=/var/www/html/storage/logs/spv.log
pidfile=/var/run/supervisord.pid

[program:php]
command=%(ENV_SUPERVISOR_PHP_COMMAND)s
user=sail
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:queue]
command=/usr/bin/php /var/www/html/artisan queue:work --sleep=3 --tries=3 --timeout=60 --memory=512
user=sail
environment=LARAVEL_SAIL="1"
autostart=true
autorestart=true
stdout_logfile=/var/www/html/storage/logs/queue.log
stderr_logfile=/var/www/html/storage/logs/queue_error.log
stderr_logfile_maxbytes=20MB
stderr_logfile_backups=5
stdout_logfile_maxbytes=50MB
stopwaitsecs=360
