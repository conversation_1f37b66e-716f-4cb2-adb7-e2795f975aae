server {
    listen 80 default_server;
    server_name _;
    root /var/www/html/public;

    gzip on;                         # Włącz gzip dla tego serwera
    gzip_comp_level 5;                # Poziom kompresji (zalecany 4-6)
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript image/svg+xml;
    gzip_vary on;                     # Ustawienie nagłówka Vary: Accept-Encoding
    gzip_min_length 1000;             # Minimalna wielkość pliku do kompresji
    gzip_proxied any;                 # Obsługa gzip dla proxy

    index index.php index.html index.htm;

#    location /.well-known/acme-challenge/ {
#        root /var/www/certbot;
#    }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass app:9000;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    error_log  /var/log/nginx/error80.log;
    access_log /var/log/nginx/access80.log;
}
