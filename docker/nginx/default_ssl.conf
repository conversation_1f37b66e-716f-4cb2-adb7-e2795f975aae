server {
    if ($host = app.twojefaktury.eu) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    listen 80;
    server_name app.twojefaktury.eu;
    root /var/www/html/public;

    index index.php;

#    location /.well-known/acme-challenge/ {
#        root /var/www/certbot;
#    }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
}

server {
    if ($host = zaplecze.twojefaktury.eu) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    listen 80;
    server_name zaplecze.twojefaktury.eu;
    root /var/www/html/public;

    index index.php;

#    location /.well-known/acme-challenge/ {
#        root /var/www/certbot;
#    }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
}

server {
    listen 443 ssl;
    server_name app.twojefaktury.eu;
    server_tokens off;
    root /var/www/html/public;

    gzip on;                         # Włącz gzip dla tego serwera
    gzip_comp_level 5;                # Poziom kompresji (zalecany 4-6)
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript image/svg+xml;
    gzip_vary on;                     # Ustawienie nagłówka Vary: Accept-Encoding
    gzip_min_length 1000;             # Minimalna wielkość pliku do kompresji
    gzip_proxied any;                 # Obsługa gzip dla proxy

    ssl_certificate /etc/ssl/tf2ssl/cf_certificate.pem;
    ssl_certificate_key /etc/ssl/tf2ssl/cf_private.key;

    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass app:9000;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    error_log  /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;
}

server {
    listen 443 ssl;
    server_name zaplecze.twojefaktury.eu;
    server_tokens off;
    root /var/www/html/public;

    gzip on;                         # Włącz gzip dla tego serwera
    gzip_comp_level 5;                # Poziom kompresji (zalecany 4-6)
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript image/svg+xml;
    gzip_vary on;                     # Ustawienie nagłówka Vary: Accept-Encoding
    gzip_min_length 1000;             # Minimalna wielkość pliku do kompresji
    gzip_proxied any;                 # Obsługa gzip dla proxy

    ssl_certificate /etc/ssl/tf2ssl/cf_certificate.pem;
    ssl_certificate_key /etc/ssl/tf2ssl/cf_private.key;

    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass app:9000;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    error_log  /var/log/nginx/zaplecze_error.log;
    access_log /var/log/nginx/zaplecze_access.log;
}
