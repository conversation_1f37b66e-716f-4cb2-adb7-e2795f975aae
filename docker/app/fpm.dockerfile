FROM serversideup/php:8.3-fpm

ARG WWWGROUP
ARG WWWUSER

ENV TZ=UTC
WORKDIR /var/www/html

USER root
RUN apt-get update && apt-get upgrade -y
RUN apt-get install -y sqlite3
RUN apt-get install -y nodejs npm

RUN install-php-extensions sqlite3 gd imap bcmath intl readline msgpack igbinary memcached mongodb ftp
RUN install-php-extensions soap
RUN docker-php-serversideup-set-id www-data $WWWUSER:$WWWGROUP
USER www-data
