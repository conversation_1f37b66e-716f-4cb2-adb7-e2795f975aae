# Modular Email Templates

This directory contains a modular email template system that allows you to create consistent emails with reusable components.

## Directory Structure

- `layouts/` - Contains the main layout templates
  - `main.blade.php` - The main layout with HTML structure, head section, and styles

- `partials/` - Contains reusable sections of the email
  - `header.blade.php` - The header section with logo
  - `title.blade.php` - The title section
  - `footer.blade.php` - The footer section with copyright information

- `components/` - Contains reusable UI components
  - `button.blade.php` - A styled button component
  - `panel.blade.php` - A highlighted panel component
  - `subcopy.blade.php` - A subcopy section for additional information

## Example Templates

- `welcome-html.blade.php` - Welcome email for new users
- `password-reset-html.blade.php` - Password reset email
- `order-confirmation-html.blade.php` - Order confirmation email

## How to Use

### Creating a New Email Template

1. Create a new Blade file in the `email` directory
2. Extend the main layout: `@extends('email.layouts.main')`
3. Define the required sections:
   - `@section('header')` - Include the header partial
   - `@section('title')` - Include the title partial with a custom title
   - `@section('content')` - Add your custom content
   - `@section('footer')` - Include the footer partial

### Example

```blade
@extends('email.layouts.main')

@section('header')
    @include('email.partials.header')
@endsection

@section('title')
    @include('email.partials.title', ['title' => 'Your Custom Title'])
@endsection

@section('content')
    <p>Your custom content goes here...</p>
    
    @component('email.components.panel')
        <p>Panel content...</p>
    @endcomponent
    
    @include('email.components.button', [
        'url' => 'https://example.com', 
        'text' => 'Click Me'
    ])
    
    @component('email.components.subcopy')
        <p>Subcopy content...</p>
    @endcomponent
@endsection

@section('footer')
    @include('email.partials.footer')
@endsection
```

### Using Components

#### Button Component

```blade
@include('email.components.button', [
    'url' => 'https://example.com', 
    'text' => 'Click Me'
])
```

#### Panel Component

```blade
@component('email.components.panel')
    <p>Your panel content here...</p>
@endcomponent
```

#### Subcopy Component

```blade
@component('email.components.subcopy')
    <p>Your subcopy content here...</p>
@endcomponent
```

## Customizing Styles

To customize the styles for all emails, edit the CSS in `layouts/main.blade.php`.
