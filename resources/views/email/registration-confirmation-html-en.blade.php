@extends('email.layouts.main')

@section('header')
    @include('email.partials.header')
@endsection

@section('title')
    @include('email.partials.title', ['title' => 'Dokończ rejestrację'])
@endsection

@section('content')
    <p><PERSON><PERSON><PERSON> dob<PERSON>!</p>

    <p><strong>D<PERSON>ękujemy za rozpoczęcie procesu rejestracji!</strong> <br>
        We're excited to have you join us.
    </p>

    <p>To complete your registration, please use the confirmation code below:</p>

    @component('email.components.panel')
        <p style="font-size: 24px; text-align: center; font-weight: bold; letter-spacing: 2px;">{{ $registration->confirmation_code }}</p>
    @endcomponent

    <p>Please enter this code on the confirmation page to verify your email address and complete your registration.</p>

    @include('email.components.button', [
        'url' =>  $confirmUrl,
        'text' => 'Complete Registration'
    ])

    @component('email.components.subcopy')
        <p>If you did not request this registration, no further action is required.</p>
        <p>If you're having trouble clicking the "Complete Registration" button, copy and paste the URL below into your web browser: {{ $confirmUrl }}</p>
    @endcomponent

    <p>Best regards,<br>
    The {{ config('app.name') }} Team</p>
@endsection

@section('footer')
    @include('email.partials.footer')
@endsection
