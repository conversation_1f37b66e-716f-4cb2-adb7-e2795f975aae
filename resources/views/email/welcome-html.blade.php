@extends('email.layouts.main')

@section('header')
    @include('email.partials.header')
@endsection

@section('title')
    @include('email.partials.title', ['title' => 'Witamy w systemie ' . config('app.name') . '!'])
@endsection

@section('content')
    <p>Szanowny użytkowniku,</p>

    <p><strong>Dziękujemy za rejestracje w systemie {{ config('app.name') }}!</strong> <br>
        <PERSON><PERSON><PERSON> si<PERSON> cieszy<PERSON>, że jesteś z nami.
    </p>

    <p>Twoje konto zostało pomyślnie utworzone i jest teraz gotowe do użycia. Możesz się teraz zalogować, aby uzyskać dostęp do wszystkich oferowanych przez nas funkcji i usług.</p>

    @component('email.components.panel')
        <p>Dane konta:</p>
        <ul>
            <li><strong>Nazwa:</strong> {{ $user->fullName() ?? 'your name' }}</li>
            <li><strong>Email (login):</strong> {{ $user->email ?? 'your registered email' }}</li>
        </ul>
    @endcomponent

    @include('email.components.button', ['url' => config('app.url') . '/app/login', 'text' => 'Logowanie do konta'])

{{--    <p>Here are some things you can do now:</p>--}}
{{--    <ul>--}}
{{--        <li>Complete your profile information</li>--}}
{{--        <li>Explore our services and features</li>--}}
{{--        <li>Connect with other users</li>--}}
{{--        <li>Customize your account settings</li>--}}
{{--    </ul>--}}

    <p>Jeżeli masz pytania lub potrzbujesz pomocy przy naszym programie prosimy o kontakt z naszym zespołem wsparcia.</p>

    @component('email.components.subcopy')
        <p>Jeżeli ta rejestracja nie została dokonana przez Ciebie bardzo prosumy o kontakt z nami na adres support@{{ config('app.domain', 'example.com') }}.</p>
    @endcomponent

    <p>Pozdrawiamy,<br>
    Zespół {{ config('app.name') }}</p>
@endsection

@section('footer')
    @include('email.partials.footer')
@endsection
