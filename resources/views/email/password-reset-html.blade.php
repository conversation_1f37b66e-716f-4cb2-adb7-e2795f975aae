@extends('email.layouts.main')

@section('header')
    @include('email.partials.header')
@endsection

@section('title')
    @include('email.partials.title', ['title' => 'Zresetuj swoja hasło'])
@endsection

@section('content')
    <p>Witaj {{ $userData->name ?? 'użytkowniku' }},</p>

    <p>Otrzyumujesz tą wiadomość, poniew<PERSON><PERSON> otrzymaliśmy prośbę o zresetowanie hasła do Twojego konta.</p>

    @include('email.components.button', ['url' => $resetUrl ?? config('app.url') . '/reset-password', 'text' => 'Reset Password'])

    <p>Ten link resetujący hasło wygaśnie w ciągu 60 minut.</p>

    <p>Je<PERSON><PERSON> nie poprosiłeś o zresetowanie hasła, nic nie robić.</p>

    @component('email.components.subcopy')
        <p style="word-break-wrap: break-all">
            Jeżeli masz problem z kliknięciem przycisku "Reset Password", skopiuj i wklej poniższy link do swojej przeglądarki: <br>
        {{ $resetUrl ?? config('app.url') . '/reset-password' }}</p>
    @endcomponent

    <p>Z poważaniem,<br>
    Zespół {{ config('app.name') }}</p>
@endsection

@section('footer')
    @include('email.partials.footer')
@endsection
