<!DOCTYPE html>
<html>
<head>
    <title>{{ $title ?? config('app.name') }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            color: #4D5F80;
            line-height: 1.5;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
        }
        .header {
            text-align: center;
            padding: 20px 0;
        }
        .logo {
            max-width: 250px;
            height: auto;
        }
        h1 {
            color: #2D64C8;
            font-size: 24px;
            margin-top: 20px;
            text-align: center;
        }
        .content {
            padding: 20px 0;
        }
        .panel {
            background-color: #f8fafc;
            border-left: 4px solid #2D64C8;
            padding: 15px;
            margin: 20px 0;
        }
        .button {
            display: inline-block;
            background-color: #2D64C8;
            color: #ffffff !important;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        .footer {
            text-align: center;
            padding: 20px 0;
            font-size: 12px;
            color: #718096;
            border-top: 1px solid #e8e5ef;
            margin-top: 30px;
        }
        .subcopy {
            border-top: 1px solid #e8e5ef;
            margin-top: 25px;
            padding-top: 25px;
            font-size: 14px;
            color: #718096;
            word-break: break-all;
            word-wrap: anywhere;
        }
        strong {
            color: #2D64C8;
        }
    </style>
</head>
<body>
    <div class="container">
        @yield('header')

        @yield('title')

        <div class="content">
            @yield('content')
        </div>

        @yield('footer')
    </div>
</body>
</html>
