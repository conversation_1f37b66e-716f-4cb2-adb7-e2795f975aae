@extends('email.layouts.main')

@section('header')
    @include('email.partials.header')
@endsection

@section('title')
    @include('email.partials.title', ['title' => 'Dokończ rejestrację'])
@endsection

@section('content')
    <p><PERSON><PERSON><PERSON> dobry!</p>

    <p><strong>Dziękujemy za rozpoczęcie procesu rejestracji w serwisie {{ config('app.name') }}!</strong>
    </p>

    <p>Abu doko<PERSON>ć rejestrację, proszę użyć poniższego kodu potwierdzającego:</p>

    @component('email.components.panel')
        <p style="font-size: 24px; text-align: center; font-weight: bold; letter-spacing: 2px;">{{ $registration->confirmation_code }}</p>
    @endcomponent

    <p>Proszę wprowadzić ten kod na stronie potwierdzenia, aby zweryfikować swój adres e-mail i dokończyć rejestrację.</p>
    <p>Kod jest ważny przez 30 minut.</p>

    @include('email.components.button', [
        'url' =>  $confirmUrl,
        'text' => 'Do<PERSON>ńcz rejestrację'
    ])

    @component('email.components.subcopy')
        <p>Jeśli nie prosiłeś o tę rejestrację, nie musisz podejmować żadnych działań.</p>
        <p>Jeśli masz problem z kliknięciem przycisku "Dokończ rejestrację", skopiuj i wklej poniższy adres URL do przeglądarki: <br>
            {{ $confirmUrl }}
        </p>
    @endcomponent

    <p>Z poważaniem,<br>
    Zespół {{ config('app.name') }}</p>
@endsection

@section('footer')
    @include('email.partials.footer')
@endsection
