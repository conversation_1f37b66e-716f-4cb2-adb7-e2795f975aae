@extends('print.layout')
@section('content')
    <div>
        <div id="header">
            <h1>Dokument Zwrot Wewnętrzny (ZW) {{ $record->full_doc_number }}</h1>
            <h3>{{ $record->transaction_id }}</h3>
            @if($record->cancelled)
                <h3>DOKUMENT ANULOWANY!!!</h3>
            @endif
            <div class="header-dates">
                <small>
                    Data wystawienia: {{ $record->createdAt()->setTimezone(new DateTimeZone('Europe/Warsaw'))->format('Y-m-d H:i:s') }}<br>
                    Data przyjęcia: {{ $record->itemsIssuedAt()?->setTimezone(new DateTimeZone('Europe/Warsaw'))->format('Y-m-d H:i:s') }}
                    @if($record->cancelled)
                       <br> Data anulowania: {{ $record->cancelled_at->setTimezone(new DateTimeZone('Europe/Warsaw'))->format('Y-m-d H:i:s') }},
                         anulowany przez {{$record->cancelledby->fullName()}}
                    @endif
                </small>
            </div>
        </div>
        <div id="subheader">
            <div class="pull-left issuer_data">
                <strong>Wystawca:</strong><br>
                <span>
                    {!! nl2br($record->issuer_data) !!}
                </span>
                <div style="margin-top: 3mm;">
                    <strong>Magazyn: </strong>{{ $record->warehouse->name }}<br>
                </div>
            </div>
            <div class="push-right partner_data">
                <strong>Dostawca</strong>:<br>
                <span>
                    {!! nl2br($record->partner_data) !!}
                </span>
            </div>
        </div>
        @include('print.warehouse_docs.note')
        <div id="content">
            <table>
                <thead>
                <tr>
                    <th>L.P.</th>
                    <th>Nazwa</th>
                    <th>Seria</th>
                    <th>Ilość</th>
                    <th>Cena</th>
                    <th>Wartość</th>
                </tr>
                </thead>
                <tbody>
                @foreach($record->log as $row)
                    <tr>
                        <td class="text-right">{{ $loop->iteration }}.</td>
                        <td>{{ $row->product->name }}</td>
                        <td>{{ $row->series_no ?? '--' }}</td>
                        <td class="text-right">{{ $row->amount }}</td>
                        <td class="text-right">{{ $row->price }}</td>
                        <td class="text-right">{{ number_format($row->amount * $row->price, 2, '.', '') }}</td>
                    </tr>

                @endforeach

                </tbody>
            </table>
        </div>

        <div id="footer">
            <div id="wystawca">
                <span>Dokument wystawił</span>
                <div class="underlined">
                    {{ $record->user->fullName() }}
                </div>
            </div>
            <div id="odbiorca">
                <span>Dokument odebrał</span>
                <div class="underlined">
                    {{ $record->signatory_name }} {{ $record->signatory_last_name }}
                </div>
            </div>
            <div class="footer-line">
                Wydruk: {{ now(new DateTimeZone('Europe/Warsaw'))  }}
            </div>
        </div>
    </div>
@endsection
