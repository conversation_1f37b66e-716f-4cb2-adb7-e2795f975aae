<?php
    $issuer = $document->issuer_address;
    $seller = $document->options['different_seller'] ? $document->seller_address : $document->issuer_address;
    $buyer = $document->buyer_address;
    $bank = $document->bank_data;
    $vat = $document->vat;
    $vat_source = $document->vat_source;
    $vat_final = $document->vat_final;
    $options = $document->options;
    $items = $document->items;
    $sourcedoc = $record->getSourceDoc();
?>
@extends('print.trade_docs.main')
@section('content')
    <table cellpadding="0" cellspacing="0" class="header">
        <!-- G<PERSON><PERSON> część faktury -->
        <tr class="top-logo">
            <td colspan="4">
                <table>
                    <tr>
                        <td>
                            <img src="{{ imageFileToBase64(\Illuminate\Support\Facades\Storage::disk('local')->path('logo_.png')) }}" alt="Logo firmy" style="max-height:80px;">
                        </td>
                        <td style="text-align: right;">
                            <h2>Faktura VAT korekta
                                @if ($options['duplicate'] ?? false)
                                    <br><small style="font-size: 70%;">Duplikat, data duplikatu: {{$options['duplicate']}}</small>
                                @endif
                            </h2>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr class="top">
            <td colspan="4">
                <table>
                    <tr>
                        <td>
                            <strong>Sprzedawca:</strong><br>
                                {!! nl2br($seller['name']) !!}<br>
                                {{$seller['address']}}<br>
                                {{$seller['postcode']}} {{$seller['city']}}<br>
                                @if ($seller['vat_id'])
                                    NIP: {{$seller['vat_id']}} <br>
                                @endif
                                BDO: 1212545<br>
                                Bank: {{$bank['bank_name']}}<br>
                                Konto: {{$bank['bank_account']}}<br>
                            @if($bank['bank_swift'])
                                SWIFT: {{$bank['bank_swift']}}
                            @endif
                            @if($bank['bank_iban'])
                                IBAN: {{$bank['bank_iban']}}
                            @endif
                        </td>
                        <td style="text-align: right;">
                            Nr: <strong>{{$record->full_doc_number}}</strong><br>
                            Data wystawienia: {{$record->issued_at->format('Y-m-d')}}<br>
                            <br>
                            <strong>Dokument korygowany</strong><br>
                            Nr: <strong>{{$sourcedoc->full_doc_number}}</strong><br>
                            Data wystawienia: {{$sourcedoc->issued_at->format('Y-m-d')}}<br>
                            Data sprzedaży: {{$sourcedoc->sells_date->format('Y-m-d')}}<br>
                            Waluta: {{$sourcedoc->currency}}<br>
                            @if($document->getOption('reverse_charge', false))
                                <strong>{{__('app.trade_docs._.reverse_charge')}}</strong><br>
                            @endif
                            @if($document->getOption('mpp', false))
                                <strong>{{__('app.trade_docs._.mpp')}}</strong><br>
                            @endif
                            <strong>VAT </strong>{{$record->vat_method->label()}} <br>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <!-- Informacje o nabywcy -->
        <tr class="information">
            <td colspan="4">
                <table>
                    <tr>
                        <td>
                            <strong>Nabywca:</strong><br>
                            {!! nl2br($buyer['name']) !!}<br>
                            {{$buyer['address']}}<br>
                            {{$buyer['postcode']}} {{$buyer['city']}}<br>
                            @if ($buyer['vat_id'])
                            NIP: {{$buyer['vat_id']}} <br>
                            @endif
                        </td>
                        <td style="text-align: right;">
                            @if($record->notes)
                                <div style="margin-top: 2mm;">
                                    <strong>Przyczyna korekty:</strong>
                                    <div>
                                        {{$record->notes}}<br>
                                    </div>
                                </div>
                            @endif
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    @include('print.trade_docs.parts.fvk_items_l1')
    @include('print.trade_docs.parts.fvk_summary_l1')
    @include('print.trade_docs.parts.footer_l1')
@endsection
