@extends('print.zz_layout')
@section('content')
    <div>
        <div id="header">
            <h1>Zlecenie zewnętrzne (ZZ)
                <br>{{ $record->name }}
            </h1>
            <h3>Numer zlecenia: {{ $record->number }}</h3>
            {{--            @if($record->cancelled)--}}
            {{--                <h3>DOKUMENT ANULOWANY!!!</h3>--}}
            {{--            @endif--}}
            <div class="header-dates">
                <small>
                    {{--                    @if($record->cancelled)--}}
                    {{--                        <br> Data anulowania:--}}
                    {{--                        {{ $record->cancelled_at->setTimezone(new DateTimeZone('Europe/Warsaw'))->format('Y-m-d H:i:s') }},--}}
                    {{--                        anulowany przez {{$record->cancelledby->fullName()}}--}}
                    {{--                    @endif--}}
                </small>
            </div>
        </div>
        <div id="subheader">
            <div class="divider"></div>
            <div class="pull-left issuer_data">
                <strong>Data zlecenia:</strong><br>
                <div style="margin-bottom: 1em;">
                    {{ $record->created_at?->toDateString() ?? '-'}}
                </div>
                <strong>Zleceniodawca:</strong><br>
                <span>
                    {!! nl2br($record->partner->getAddressText()) !!}
                </span>
            </div>
            <div class="push-right partner_data">
                <strong>Data realizacji</strong>:<br>
                <div style="margin-bottom: 1em;">
                    {{ $record->planned_date_at?->toDateString() ?? '-' }}
                </div>
                <strong>Realizacja</strong>:<br>
                <span>
                    {!! nl2br(tenant()->getAddressText()) !!}
                </span>
            </div>
        </div>
        @if($record->configuration)
            <div class="configuration">
                <table class="table">
                    @foreach($record->getPreProductsConfig() as $config)
                        <tr>
                            <td>{{$config['name']}}</td>
                            <td>{{new \Illuminate\Support\HtmlString('<b>' . nl2br($config['value']) . '</b>')}}</td>
                        </tr>
                    @endforeach
                </table>
            </div>
            @if($prod = $record->getProductsConfig())
                <div id="content">
                    <div style="font-size: 80%; margin-bottom: 1em;"><strong>{{$prod['name']}}:</strong></div>
                    <table id="products">
                        <thead>
                        <tr>
                            <th>L.P.</th>
                            <th>Nazwa</th>
                            <th>Ilość</th>
                            <th>Uwagi</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($record->items as $row)
                            <tr>
                                <td class="text-right">{{ $loop->iteration }}.</td>
                                <td>{{ $row->product_name }}</td>
                                <td class="text-right">{{ $row->quantity ?? '--' }}</td>
                                <td>{{ $row->product->name }}</td>
                            </tr>

                        @endforeach

                        </tbody>
                    </table>
                </div>
            @endif
            <div class="configuration">
                <table class="table">
                    @foreach($record->getPostProductsConfig() as $config)
                        <tr>
                            <td>{{$config['name']}}</td>
                            <td>{{new \Illuminate\Support\HtmlString('<b>' . nl2br($config['value']) . '</b>')}}</td>
                        </tr>
                    @endforeach
                </table>
            </div>
        @endif
        @if(filled($record->comment))
            <div id="note-container">
                <div id="note">
                    <table class="table" width="100%">
                        <tr>
                            <td><strong>UWAGI</strong></td>
                        </tr>
                        <tr>
                            <td>{{new \Illuminate\Support\HtmlString('<b>' . nl2br($record->comment) . '</b>')}}</td>
                        </tr>
                    </table>
                </div>
            </div>
        @endif
        <div id="footer">
            <div id="wystawca">
                <span>Dokument wystawił</span>
                <div class="underlined">
                    {{ $record->executor?->fullName() ?? $record->creator->fullName() }}
                </div>
            </div>
            <div id="odbiorca">
                <span>Dokument odebrał</span>
                <div class="underlined">
                </div>
            </div>
            <div class="footer-line">
                Wydruk: {{ now(new DateTimeZone('Europe/Warsaw'))  }}
            </div>
        </div>
    </div>
@endsection
