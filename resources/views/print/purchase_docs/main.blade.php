<!DOCTYPE html>
<html lang="pl">
<head>
{{--    <meta charset="UTF-8">--}}
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title></title>
    <style>
        @page {
            margin: 0;
        }
        body {
            font-family: DejaVu Sans, sans-serif;
            margin: 0;
            color: #222;
            font-size: 9pt;
            line-height: 1.2;
        }

        .invoice-box {
            max-width: 190mm;
            height: 290mm;
            margin: auto;
            padding-top: 5mm;
            background-color: #fff;
            display: block;
            position: relative;
        }

        .invoice-box table {
            width: 100%;
            line-height: inherit;
            text-align: left;
            border-collapse: collapse;
        }

        .invoice-box table td {
            padding: 5px 0 5px 0;
            vertical-align: top;
        }

        .invoice-box table.header tr.top table td {
            padding-bottom: 5mm;
        }

        .invoice-box table.header tr.information table td {
            padding-bottom: 5mm;
        }

        .invoice-box table.header tr.heading td {
            background: #f2f2f2;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
        }

        .invoice-box table.header tr.details td {
            padding-bottom: 20px;
        }

        table.items {
            width: 100%;
            border-collapse: collapse;
            line-height: 1.15;
            font-size: 9pt;
        }

        table.items tr.heading td {
            background: #eaeaea;
            border-bottom: 1px solid #dadada;
            text-align: right;
            padding-left: 1mm;
            padding-right: 1mm;
        }

        table.items tr.heading td:nth-child(1) {
            text-align: left;
        }

        table.items tr.item td {
            text-align: right;
            border-bottom: 1px solid #eee;
            padding-top: 1mm;
            padding-bottom: 1mm;
        }

        table.items tr.item td:nth-child(1) {
            text-align: left;
        }

    /* total */
        table.items tr.total:first-of-type td:nth-child(2) {
            padding-top: 3mm;
        }

        table.items tr.total:first-of-type td:nth-child(3) {
            padding-top: 3mm;
        }

        table.items tr.total td:nth-child(2) {
            padding-top: 0.5mm;
            padding-bottom: 0.5mm;
            font-weight: bold;
        }

        table.items tr.total td:nth-child(3) {
            padding-top: 0.5mm;
            padding-bottom: 0.5mm;
        }

        table.items tr.total:last-of-type td {
            padding-top: 1mm;
            border-bottom: 0;
        }

        tr.item-diff td {
            text-align: right;
            border-bottom: 1px solid #555;
            padding-top: 1mm;
            padding-bottom: 1mm;
            font-weight: bold;
        }

        table.vat-table tr td {
            font-weight: bold;
            text-align: right;
        }


        /* innertotal */
        table.inner-total tr:first-of-type td {
            padding-top: 3mm;
            font-weight: normal;
        }

        table.inner-total tr td {
            padding-top: 0.5mm;
            padding-bottom: 0.5mm;
            font-weight: normal;
        }

        table.inner-total tr:last-of-type td {
            padding-top: 1mm;
            border-bottom: 0;
        }


        table.summary {
            margin-top: 3mm;
            width: 100%;
            border-collapse: collapse;
            font-size: 9pt;
        }

        table.summary-half {
            margin-top: 3mm;
            width: 50%;
            border-collapse: collapse;
            font-size: 9pt;
        }

        .alert {
            color: red;
            font-weight: bold;
        }

        .footer {
            position: absolute;
            width: 180mm;
            height: 7mm;
            left: 5mm;
            bottom: 8mm;
        }

        .table-caption {
            font-size: 9pt;
            font-weight: bold;
            text-align: center;
            padding-top: 5px;
            padding-bottom: 5px;
        }

        .footer .footer-line {
            font-size: 8pt;
            clear: both;
            text-align: center;
        }

        /* Print Styles */
        @media print {
            .invoice-box {
                border: 0;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
<div class="invoice-box">
   @yield('content')
</div>
</body>
</html>
