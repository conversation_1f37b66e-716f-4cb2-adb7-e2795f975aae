<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title><PERSON><PERSON></title>

    <!-- Styles -->
    <style>
        /* ! tailwindcss v3.2.4 | MIT License | https://tailwindcss.com */
        *, ::after, ::before {
            box-sizing: border-box;
            border-width: 0;
            border-style: solid;
            border-color: #e5e7eb
        }

        ::after, ::before {
            --tw-content: ''
        }

        @page {
            margin: 0;
        }

        html {
            /*line-height: 1.5;*/
            /*-webkit-text-size-adjust: 100%;*/
            /*-moz-tab-size: 4;*/
            /*tab-size: 4;*/
            /*font-family: Figtree, sans-serif;*/
            /*font-feature-settings: normal*/
        }

        body {
            margin: 0;
            line-height: inherit;
            font-family: <PERSON>ja<PERSON><PERSON>, sans-serif;
            font-size: 11pt;
        }

        hr {
            height: 0;
            color: inherit;
            border-top-width: 1px
        }

        abbr:where([title]) {
            -webkit-text-decoration: underline dotted;
            text-decoration: underline dotted
        }

        h1, h2, h3, h4, h5, h6 {
            font-size: inherit;
            font-weight: inherit
        }

        a {
            color: inherit;
            text-decoration: inherit
        }

        b, strong {
            font-weight: bolder
        }

        code, kbd, pre, samp {
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
            font-size: 1em
        }

        small {
            font-size: 80%
        }

        sub, sup {
            font-size: 75%;
            line-height: 0;
            position: relative;
            vertical-align: baseline
        }

        sub {
            bottom: -.25em
        }

        sup {
            top: -.5em
        }

        table {
            text-indent: 0;
            border-color: inherit;
            border-collapse: collapse
        }

        button, input, optgroup, select, textarea {
            font-family: inherit;
            font-size: 100%;
            font-weight: inherit;
            line-height: inherit;
            color: inherit;
            margin: 0;
            padding: 0
        }

        button, select {
            text-transform: none
        }

        [type=button], [type=reset], [type=submit], button {
            -webkit-appearance: button;
            background-color: transparent;
            background-image: none
        }

        :-moz-focusring {
            outline: auto
        }

        :-moz-ui-invalid {
            box-shadow: none
        }

        progress {
            vertical-align: baseline
        }

        ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
            height: auto
        }

        [type=search] {
            -webkit-appearance: textfield;
            outline-offset: -2px
        }

        ::-webkit-search-decoration {
            -webkit-appearance: none
        }

        ::-webkit-file-upload-button {
            -webkit-appearance: button;
            font: inherit
        }

        summary {
            display: list-item
        }

        blockquote, dd, dl, figure, h1, h2, h3, h4, h5, h6, hr, p, pre {
            margin: 0
        }

        fieldset {
            margin: 0;
            padding: 0
        }

        legend {
            padding: 0
        }

        menu, ol, ul {
            list-style: none;
            margin: 0;
            padding: 0
        }

        textarea {
            resize: vertical
        }

        input::placeholder, textarea::placeholder {
            opacity: 1;
            color: #9ca3af
        }

        [role=button], button {
            cursor: pointer
        }

        :disabled {
            cursor: default
        }

        audio, canvas, embed, iframe, img, object, svg, video {
            display: block;
            vertical-align: middle
        }

        img, video {
            max-width: 100%;
            height: auto
        }

        main{
            margin-top: 7mm;
            width: 195mm;
            height: 290mm;
            margin-left:auto;
            margin-right:auto;
            /*border: 1px solid black;*/
            display: block;
            position: relative;
        }

        #header {
            text-align: center;
        }

        #header h1{
            margin-bottom: 3mm;
        }

        #header h3{
            margin-bottom: 2mm;
        }

        #header .header-dates small {
        }

        #subheader {
            margin-top: 10mm;
            clear: both;
            font-size: 9pt;
        }

        #subheader .pull-left {
            float: left;
        }

        #note-container {
            clear: both;
            margin-top: 2mm;
        }
        #note {
            font-size: 9pt;
            margin-top: 5mm;
        }

        #note table tr td {
            padding: 3mm;
        }

        #subheader .push-right {
            float: right;
        }

        #content{
            clear: both;
            margin-top: 5mm;
            padding-top: 8mm;
        }

        .configuration {
            margin-top: 5mm;
            padding-top: 10mm;
            clear: both;
        }

        .configuration table {
            width: 100%;
            border: 1px solid black;
        }

        .configuration table tr, th, td {
            border: 1px solid black;
            padding-left: 0.4em;
            padding-right: 0.4em;
        }

        .configuration table td {
            font-size: 9pt;
        }

        .configuration table th {
            font-size: 10pt;
        }

        #content table {
            width: 100%;
            border: 1px solid black;
        }

        #content table tr, th, td{
            border: 1px solid black;
            padding: 0.4em;
        }

        #content table td {
            font-size: 9pt;
        }

        #content table th {
            font-size: 10pt;
        }

        .divider {
            clear: both;
            height: 1em!important;
        }

        #footer {
            position: absolute;
            width: 180mm;
            height: 17mm;
            left: 7mm;
            bottom: 15mm;
        }

        #footer #wystawca{
            float: left;
            border-bottom: 1px solid black;
            font-size: 10pt;
        }

        #footer #wystawca span{
            font-size: 90%;
        }

        #footer #odbiorca{
            float: right;
            border-bottom: 1px solid black;
            font-size: 10pt;
        }

        #footer #odbiorca span{
            font-size: 90%;
        }

        #footer .underlined {
            text-align: center;
            margin-top: 3mm;
        }

        .text-right {
            text-align: right;
        }

        #footer .footer-line {
            font-size: 8pt;
            clear: both;
            text-align: center;
        }


    </style>
</head>
<body class="antialiased">
<main>
    <div>
        @yield('content')

    </div>
</main>

</body>
</html>
