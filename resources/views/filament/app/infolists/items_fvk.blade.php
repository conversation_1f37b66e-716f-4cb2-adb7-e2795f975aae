@php
    $meta = $record->getMeta();
//    dd($meta);
@endphp
<div class="text-xs text-center py-1.5 font-bold">Produkty</div>
<div class="fi-ta-content relative overflow-x-auto">
    <table class="fi-ta-table w-full border-t table-auto">
        <tbody>
        @if(blank($items))
            <tr class="fi-ta-row">
                <td class="fi-ta-cell text-sm text-center" colspan="2">
                    <span class="fi-ta-cell-label text-sm font-semibold">
                        Brak danych
                    </span>
                </td>
            </tr>
        @else
            <tr class="fi-ta-row">
                <th class="fi-ta-header-cell text-xs font-semibold py-1.5">
                    {{__('app.trade_docs.add_item.label')}}
                </th>
                <th class="fi-ta-header-cell text-xs font-semibold text-right py-1.5 px-1.5 whitespace-nowrap">
                    {{__('app.trade_docs.add_item.amount')}}
                </th>
                <th class="fi-ta-header-cell text-xs font-semibold text-right py-1.5 px-1.5 whitespace-nowrap">
                    {{__('app.trade_docs.add_item.discounted_unit_price')}}
                </th>
                <th class="fi-ta-header-cell text-xs font-semibold text-right py-1.5 px-1.5 whitespace-nowrap">
                    {{__('app.trade_docs.add_item.net_value')}}
                </th>
                    <th class="fi-ta-header-cell text-xs font-semibold text-right py-1.5 px-1.5 whitespace-nowrap hidden md:table-cell">
                        {{__('app.trade_docs.add_item.vat_value')}}
                    </th>
                <th class="fi-ta-header-cell text-xs font-semibold text-right py-1.5 px-1.5 whitespace-nowrap">
                    {{__('app.trade_docs.add_item.gross_value')}}
                </th>
            </tr>
            @foreach($items as $item)
                @if($meta->items[$item->uuid] ?? false)
                    @php($itemscr = $meta->items[$item->uuid])

                <tr class="fi-ta-row">
                    <td class="fi-ta-cell text-xs py-1.5 w-72">
                        <div>
                            {{$itemscr['label']}}
                        </div>
                        <div class="fi-ta-cell-label text-xs font-semibold text-right">
                            Różnica
                        </div>
                    </td>
                    <td class="fi-ta-cell text-xs text-right py-1.5">
                        <div>
                            {{number_format($itemscr['amount'], 2, ',', '')}} {{$itemscr['unit_type']}}
                        </div>
                        <div class="fi-ta-cell-label text-xs font-semibold">
                            {{number_format($item->amount, 2, ',', '')}} {{$item->unit_type}}
                        </div>
                    </td>
                    <td class="fi-ta-cell text-xs text-right py-1.5">
                        <div>
                            {{$itemscr['discounted_unit_price']}}
                        </div>
                        <div class="fi-ta-cell-label text-xs font-semibold">
                            {{$item->discounted_unit_price}}
                        </div>
                    </td>
                    <td class="fi-ta-cell text-xs text-right py-1.5">
                        <div>
                            {{$itemscr['net_value']}}
                        </div>
                        <div class="fi-ta-cell-label text-xs font-semibold">
                            {{$item->discounted_unit_price}}
                        </div>
                    </td>
                    <td class="fi-ta-cell text-xs text-right py-1.5 hidden md:table-cell">
                        <div>
                            {{$itemscr['vat_value']}}
                        </div>
                        <div class="fi-ta-cell-label text-xs font-semibold">
                            {{$item->vat_value}}
                        </div>
                    </td>
                    <td class="fi-ta-cell text-xs text-right py-1.5">
                        <div>
                            {{$itemscr['gross_value']}}
                        </div>
                        <div class="fi-ta-cell-label text-xs font-semibold">
                            {{$item->gross_value}}
                        </div>
                    </td>
                </tr>
                @else
                    <tr class="fi-ta-row">
                        <td class="fi-ta-cell text-xs py-1.5 w-72 font-semibold">
                            {{$item->label}}
                        </td>
                        <td class="fi-ta-cell text-xs text-right py-1.5 font-semibold">
                            {{number_format($item->amount, 2, ',', '')}} {{$item->unit_type}}
                        </td>
                        <td class="fi-ta-cell text-xs text-right py-1.5 font-semibold">
                            {{$item->discounted_unit_price}}
                        </td>
                        <td class="fi-ta-cell text-xs text-right py-1.5 font-semibold">
                            {{$item->net_value}}
                        </td>
                        <td class="fi-ta-cell text-xs text-right py-1.5 font-semibold hidden md:table-cell">
                            {{$item->vat_value}}
                        </td>
                        <td class="fi-ta-cell text-xs text-right py-1.5 font-semibold">
                            {{$item->gross_value}}
                        </td>
                    </tr>
                @endif
            @endforeach
        @endif
        </tbody>
    </table>
</div>
