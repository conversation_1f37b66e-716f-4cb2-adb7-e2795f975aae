<div>
    <div class="text-xs text-center py-1.5 font-bold">Podsumowanie VAT</div>
    <?php
    $txt_size = $getExtraAttributes()['text_size'] ?? 'text-sm';
    ?>
{{--    <table class="fi-ta-table w-full table-auto">--}}
{{--        <caption style="font-size: 80%;">Przed korektą</caption>--}}
{{--        <thead>--}}
{{--        <tr>--}}
{{--            <th class="text-left fi-ta-header-cell">--}}
{{--                    <span class="fi-ta-header-cell-label {{$txt_size}} font-semibold">--}}
{{--                        Stawka VAT--}}
{{--                    </span>--}}
{{--            </th>--}}
{{--            <th class="text-right fi-ta-cell">--}}
{{--                    <span class="fi-ta-header-cell-label {{$txt_size}} font-semibold">--}}
{{--                    Netto--}}
{{--                    </span>--}}
{{--            </th>--}}
{{--            <th class="text-right fi-ta-cell">--}}
{{--                    <span class="fi-ta-header-cell-label {{$txt_size}} font-semibold">--}}
{{--                        Wartość VAT--}}
{{--                    </span>--}}
{{--            </th>--}}
{{--            <th class="text-right fi-ta-cell">--}}
{{--                    <span class="fi-ta-header-cell-label {{$txt_size}} font-semibold">--}}
{{--                    Brutto--}}
{{--                    </span>--}}
{{--            </th>--}}
{{--        </tr>--}}
{{--        </thead>--}}
{{--        <tbody>--}}
{{--        @if(blank($getState()['vat_source']))--}}
{{--            <tr class="fi-ta-row">--}}
{{--                <td class="fi-ta-cell {{$txt_size}} text-center" colspan="4">--}}
{{--                    <span class="fi-ta-cell-label {{$txt_size}} font-semibold">--}}
{{--                        Brak danych--}}
{{--                    </span>--}}
{{--                </td>--}}
{{--            </tr>--}}
{{--        @else--}}
{{--            @php--}}
{{--                $vat = $getState()['vat_source'];--}}
{{--                $summary = $getState()['vat_source']['summary'];--}}
{{--                unset($vat['summary']);--}}
{{--            @endphp--}}
{{--            @foreach($vat as $label => $row)--}}
{{--                <tr class="fi-ta-row">--}}
{{--                    <td class="fi-ta-cell {{$txt_size}}">{{$label}}</td>--}}
{{--                    <td class="fi-ta-cell {{$txt_size}} text-right">{{$row['net_amount']}}</td>--}}
{{--                    <td class="fi-ta-cell {{$txt_size}} text-right">{{$row['vat_amount']}}</td>--}}
{{--                    <td class="fi-ta-cell {{$txt_size}} text-right">{{$row['gross_amount']}}</td>--}}
{{--                </tr>--}}
{{--            @endforeach--}}
{{--            <tr class="fi-ta-row">--}}
{{--                <td class="fi-ta-cell {{$txt_size}}">Razem</td>--}}
{{--                <td class="fi-ta-cell {{$txt_size}} text-right">{{$summary['net_amount']}}</td>--}}
{{--                <td class="fi-ta-cell {{$txt_size}} text-right">{{$summary['vat_amount']}}</td>--}}
{{--                <td class="fi-ta-cell {{$txt_size}} text-right">{{$summary['gross_amount']}}</td>--}}
{{--            </tr>--}}
{{--        @endif--}}
{{--        </tbody>--}}
{{--    </table>--}}
    <table class="fi-ta-table w-full table-auto mt-1">
        <caption style="font-size: 80%;">Po korekcie</caption>
        <thead>
        <tr>
            <th class="text-left fi-ta-header-cell">
                    <span class="fi-ta-header-cell-label {{$txt_size}} font-semibold">
                        Stawka VAT
                    </span>
            </th>
            <th class="text-right fi-ta-cell">
                    <span class="fi-ta-header-cell-label {{$txt_size}} font-semibold">
                    Netto
                    </span>
            </th>
            <th class="text-right fi-ta-cell">
                    <span class="fi-ta-header-cell-label {{$txt_size}} font-semibold">
                        Wartość VAT
                    </span>
            </th>
            <th class="text-right fi-ta-cell">
                    <span class="fi-ta-header-cell-label {{$txt_size}} font-semibold">
                    Brutto
                    </span>
            </th>
        </tr>
        </thead>
        <tbody>
        @if(blank($getState()['vat']))
            <tr class="fi-ta-row">
                <td class="fi-ta-cell {{$txt_size}} text-center" colspan="4">
                    <span class="fi-ta-cell-label {{$txt_size}} font-semibold">
                        Brak danych
                    </span>
                </td>
            </tr>
        @else
            @php
                $vat = $getState()['vat'];
                $summary = $getState()['vat']['summary'];
                unset($vat['summary']);
            @endphp
            @foreach($vat as $label => $row)
                <tr class="fi-ta-row">
                    <td class="fi-ta-cell {{$txt_size}}">{{$label}}</td>
                    <td class="fi-ta-cell {{$txt_size}} text-right">{{$row['net_amount']}}</td>
                    <td class="fi-ta-cell {{$txt_size}} text-right">{{$row['vat_amount']}}</td>
                    <td class="fi-ta-cell {{$txt_size}} text-right">{{$row['gross_amount']}}</td>
                </tr>
            @endforeach
            <tr class="fi-ta-row">
                <td class="fi-ta-cell {{$txt_size}}">Razem</td>
                <td class="fi-ta-cell {{$txt_size}} text-right">{{$summary['net_amount']}}</td>
                <td class="fi-ta-cell {{$txt_size}} text-right">{{$summary['vat_amount']}}</td>
                <td class="fi-ta-cell {{$txt_size}} text-right">{{$summary['gross_amount']}}</td>
            </tr>
        @endif
        </tbody>
    </table>
</div>
