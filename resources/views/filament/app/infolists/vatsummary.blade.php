<?php
$txt_size = $getExtraAttributes()['text_size'] ?? 'text-sm';
?>

<div class="text-xs text-center py-1.5 font-bold">Podsumowanie VAT</div>
<div>
    <table class="fi-ta-table w-full table-auto">
        <thead>
        <tr>
            <th class="text-left fi-ta-header-cell">
                    <span class="fi-ta-header-cell-label {{$txt_size}} font-semibold">
                        Stawka VAT
                    </span>
            </th>
            <th class="text-right fi-ta-cell">
                    <span class="fi-ta-header-cell-label {{$txt_size}} font-semibold">
                    Netto
                    </span>
            </th>
            <th class="text-right fi-ta-cell">
                    <span class="fi-ta-header-cell-label {{$txt_size}} font-semibold">
                        Wartość VAT
                    </span>
            </th>
            <th class="text-right fi-ta-cell">
                    <span class="fi-ta-header-cell-label {{$txt_size}} font-semibold">
                    Brutto
                    </span>
            </th>
        </tr>
        </thead>
        <tbody>
        @if(blank($getState()))
            <tr class="fi-ta-row">
                <td class="fi-ta-cell {{$txt_size}} text-center" colspan="4">
                    <span class="fi-ta-cell-label {{$txt_size}} font-semibold">
                        Brak danych
                    </span>
                </td>
            </tr>
        @else
            @foreach($getState() as $label => $row)
                <tr class="fi-ta-row">
                    <td class="fi-ta-cell {{$txt_size}}">{{$label === 'summary' ? 'Razem' : $label}}</td>
                    <td class="fi-ta-cell {{$txt_size}} text-right">{{$row['net_amount']}}</td>
                    <td class="fi-ta-cell {{$txt_size}} text-right">{{$row['vat_amount']}}</td>
                    <td class="fi-ta-cell {{$txt_size}} text-right">{{$row['gross_amount']}}</td>
                </tr>
            @endforeach
        @endif
        </tbody>
    </table>
</div>
