<div class="text-xs text-center py-1.5 font-bold">Produkty</div>
<div class="fi-ta-content relative overflow-x-auto">
    <table class="fi-ta-table w-full border-t table-auto">
        <tbody>
        @if(blank($items))
            <tr class="fi-ta-row">
                <td class="fi-ta-cell text-sm text-center" colspan="2">
                    <span class="fi-ta-cell-label text-sm font-semibold">
                        <PERSON><PERSON> danych
                    </span>
                </td>
            </tr>
        @else
            <tr class="fi-ta-row">
                <th class="fi-ta-header-cell text-xs font-semibold py-1.5">
                    {{__('app.trade_docs.add_item.label')}}
                </th>
                <th class="fi-ta-header-cell text-xs font-semibold text-right py-1.5 px-1.5 whitespace-nowrap">
                    {{__('app.trade_docs.add_item.amount')}}
                </th>
                <th class="fi-ta-header-cell text-xs font-semibold text-right py-1.5 px-1.5 whitespace-nowrap">
                    {{__('app.trade_docs.add_item.discounted_unit_price')}}
                </th>
                @if($record->type !== \App\Enums\DocumentTypes::FAUP)
                <th class="fi-ta-header-cell text-xs font-semibold text-right py-1.5 px-1.5 whitespace-nowrap">
                    {{__('app.trade_docs.add_item.net_value')}}
                </th>
                    <th class="fi-ta-header-cell text-xs font-semibold text-right py-1.5 px-1.5 whitespace-nowrap hidden md:table-cell">
                        {{__('app.trade_docs.add_item.vat_value')}}
                    </th>
                @endif
                <th class="fi-ta-header-cell text-xs font-semibold text-right py-1.5 px-1.5 whitespace-nowrap">
                    {{__('app.trade_docs.add_item.gross_value')}}
                </th>
            </tr>
            @foreach($items as $item)
                <tr class="fi-ta-row">
                    <td class="fi-ta-cell text-xs py-1.5 w-72">
                        {{$item->label}}
                    </td>
                    <td class="fi-ta-cell text-xs text-right py-1.5">
                        {{number_format($item->amount, 2, ',', '')}} {{$item->unit_type}}
                    </td>
                    <td class="fi-ta-cell text-xs text-right py-1.5">
                        {{$item->discounted_unit_price}}
                    </td>
                    @if($record->type !== \App\Enums\DocumentTypes::FAUP)
                    <td class="fi-ta-cell text-xs text-right py-1.5">
                        {{$item->net_value}}
                    </td>
                    <td class="fi-ta-cell text-xs text-right py-1.5 hidden md:table-cell">
                        {{$item->vat_value}}
                    </td>
                    @endif
                    <td class="fi-ta-cell text-xs text-right py-1.5">
                        {{$item->gross_value}}
                    </td>
                </tr>
            @endforeach
        @endif
        </tbody>
    </table>
</div>
