{{--<x-filament-panels::page x-data="{ activeTab: 'tabBaseData'}">--}}
{{--    <x-filament::tabs :contained="true" class="rounded-xl bg-white p-2 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">--}}
{{--        <x-filament::tabs.item :alpine-active="'activeTab === \'tabBaseData\''" x-on:click="activeTab = 'tabBaseData'">--}}
{{--            Dane firmy--}}
{{--        </x-filament::tabs.item>--}}
{{--    </x-filament::tabs>--}}
{{--    <div class="mt-2 tab-panels">--}}
{{--        <div x-show="activeTab === 'tabBaseData'" id="tabBaseData">--}}
{{--            <x-filament-panels::form wire:submit="save">--}}
{{--                {{ $this->form }}--}}
{{--                <x-filament-panels::form.actions--}}
{{--                    :actions="$this->getFormAction()"--}}
{{--                />--}}
{{--            </x-filament-panels::form>--}}
{{--        </div>--}}
{{--    </div>--}}
{{--</x-filament-panels::page>--}}
<x-filament-panels::page>
    <x-filament-panels::form wire:submit="save">
        {{ $this->form }}
        <x-filament-panels::form.actions
            :actions="$this->getFormAction()"
        />
    </x-filament-panels::form>
</x-filament-panels::page>
