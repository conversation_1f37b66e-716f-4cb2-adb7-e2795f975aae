<x-filament-panels::page>
    <div class="space-y-6">
        {{ $this->form }}
    </div>
    <!-- Charts container - hidden by default -->
    <div wire:ignore  class="space-y-6 hidden" id="chartsContainer">
        <div wire:ignore class="fi-section fi-color-custom fi-color-primary p-5 bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10"
             id="statsChartContainer">
            <canvas id="statsChart" style="width: 100%; max-height: 600px;"></canvas>
        </div>

        <div class="flex flex-col md:flex-row gap-4">
            <div wire:ignore class="fi-section fi-color-custom fi-color-primary p-5 w-full md:w-1/2 bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10"
                 id="statsPieChartContainer">
                <canvas id="statsPieChart" style="width: 100%; max-height: 600px;"></canvas>
            </div>

            <div wire:ignore class="fi-section fi-color-custom fi-color-primary p-5 w-full md:w-1/2 bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10"
                 id="statsDataContainer">
                <h3 class="text-lg font-medium mb-4">Podsumowanie finansowe</h3>
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-2">
                        <div class="font-medium">Sprzedaż:</div>
                        <div id="salesAmount" class="text-right">0</div>
                    </div>
                    <div class="grid grid-cols-2 gap-2">
                        <div class="font-medium">Zakupy:</div>
                        <div id="purchaseAmount" class="text-right">0</div>
                    </div>
                    <div class="grid grid-cols-2 gap-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                        <div class="font-medium">Marża:</div>
                        <div id="marginAmount" class="text-right font-bold">0</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Message when no data is available -->
    <div wire:ignore  class="space-y-6" id="noDataMessage">
        <div class="fi-section fi-color-custom fi-color-primary p-5 bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 text-center py-8">
            <p class="text-gray-500 dark:text-gray-400">Wybierz parametry i kliknij "Pokaż wykres" aby wyświetlić dane.</p>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3.6.1/build/global/luxon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon@^1"></script>
    <script src="https://cdn.jsdelivr.net/npm/hammerjs@2.0.8"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.2.0/dist/chartjs-plugin-zoom.min.js"></script>
    <script>
        let chart = null;
        let pieChart = null;
        Chart.register({
            id: 'tooltipPositionerPlugin',
            beforeInit(chart) {
                // Store a reference to the original Chart instance
                window.ChartInstance = chart.constructor;

                const Tooltip = chart.constructor.registry.plugins.get('tooltip');

                if(!Tooltip || !Tooltip.positioners) {
                    console.error('Could not access Tooltip.positioners. The custom positioner was not registered.');
                    return;
                }

                if(Tooltip.positioners.mybottom) {
                    console.log('Custom tooltip positioner "mybottom" already registered.');
                    return;
                }

                Tooltip.positioners.mybottom = function (elements, eventPosition) {
                    if(!elements.length) {
                        return false;
                    }
                    const position = Tooltip.positioners.average.call(this, elements, eventPosition);
                    if(position === false) {
                        return false;
                    }

                    if(this.chart && this.chart.chartArea) {
                        position.y = this.chart.chartArea.bottom;
                        position.xAlign = 'center';
                        position.yAlign = 'bottom';
                    }
                    return position;
                };
                // console.log('Custom tooltip positioner "mybottom" registered successfully!');
            }
        });

        document.addEventListener('livewire:initialized', () => {
        @this.on('chartDataUpdated', async (data) => {
            // Show loading state
            document.getElementById('noDataMessage').classList.add('hidden');

            // Show the charts container now that data is loaded
            document.getElementById('chartsContainer').classList.remove('hidden');

            if(chart) {
                chart.destroy();
                console.log('Chart destroyed');
            }

            if(pieChart) {
                pieChart.destroy();
                console.log('Pie Chart destroyed');
            }

            try {
                const chartData = await getData(data[0]);
                const ctx = document.getElementById('statsChart').getContext('2d');
                chart = new Chart(ctx, {
                    type: 'line',
                    data: chartData['lineChart'],
                    options: {
                        responsive: true,
                        maintainAspectRatio: true,
                        spanGaps: true,
                        animation: false,
                        interaction: {
                            mode: 'index',
                            intersect: false,
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Wartość'
                                },
                                grid: {
                                    display: true,
                                    color: 'rgba(150, 150, 150, 0.5)',
                                },
                            },
                            x: {
                                type: 'category',
                                ticks: {
                                    source: 'auto',
                                    autoSkip: true,
                                    autoSkipPadding: 10,
                                    maxTicksLimit: 40,
                                },
                                grid: {
                                    display: true,
                                    color: 'rgba(150, 150, 150, 0.5)',
                                    border: {
                                        display: true,
                                        dash: [10, 10]
                                    }
                                },
                            }
                        },
                        plugins: {
                            tooltip: {
                                enabled: true,
                                position: 'mybottom'
                            },
                            zoom: {
                                limits: {
                                    x: {
                                        minRange: 10
                                    },
                                },
                                pan: {
                                    enabled: true,
                                    modifierKey: 'ctrl',
                                    mode: 'x',
                                    scaleMode: 'x'
                                },
                                zoom: {
                                    wheel: {
                                        enabled: true,
                                        modifierKey: 'ctrl',
                                    },
                                    drag: {
                                        enabled: false
                                    },
                                    pinch: {
                                        enabled: false
                                    },
                                    mode: 'x',
                                }
                            },
                            legend: {
                                display: true,
                                position: 'bottom',
                                labels: {
                                    usePointStyle: false,
                                    boxWidth: 12,
                                }
                            }
                        },
                        layout: {
                            padding: 20,
                        }
                    }
                });
                let myEvent = new Event('chartCreated');
                document.dispatchEvent(myEvent);
                const ctxp = document.getElementById('statsPieChart').getContext('2d');
                pieChart = new Chart(ctxp, {
                    type: 'pie',
                    data: chartData['pieChart'],
                    options: {
                        responsive: true,
                        maintainAspectRatio: true,
                        spanGaps: true,
                        animation: false,
                        interaction: {
                            mode: 'index',
                            intersect: false,
                        },
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        console.log(context);
                                        const label = context.label || '';
                                        const value = context.raw || 0;
                                        const total = chartData['pieChart'].datasets[0].data.reduce((a, b) => a + b, 0);
                                        const percentage = (value / total) * 100;
                                        return ` ${label}: ${value.toLocaleString('pl-PL', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} zł (${percentage.toFixed(2)}%)`;
                                    }
                                }
                            }
                        },
                        layout: {
                            padding: 20,
                        }
                    }
                });

                // Update the sales, purchase, and margin data
                updateFinancialSummary(chartData['pieChart']);
            } catch (error) {
                console.error('Error creating pieChart:', error);
                alert('An error occurred while creating the pie chart. Please check the console for details.');

                // Show the no data message if there's an error
                document.getElementById('noDataMessage').classList.remove('hidden');
                document.getElementById('chartsContainer').classList.add('hidden');
            }
        });
        });

        async function getData(content) {
            try {
                const response = await fetch('/charts/stats', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    },
                    body: JSON.stringify(content),
                });

                if(!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error('Error fetching chart data:', error);
                throw error;
            }
        }

        /**
         * Update the financial summary section with data from the pie chart
         * @param {Object} pieChartData - The pie chart data object
         */
        function updateFinancialSummary(pieChartData) {
            try {
                const salesAmountEl = document.getElementById('salesAmount');
                const purchaseAmountEl = document.getElementById('purchaseAmount');
                const marginAmountEl = document.getElementById('marginAmount');

                // Extract data from the pie chart
                if (!pieChartData || !pieChartData.datasets || !pieChartData.datasets[0] || !pieChartData.datasets[0].data) {
                    console.error('Invalid pie chart data structure:', pieChartData);
                    return;
                }

                const data = pieChartData.datasets[0].data;
                const labels = pieChartData.labels || ['Sprzedaż', 'Zakupy'];

                // Get sales and purchase values
                const salesValue = data[0] || 0;
                const purchaseValue = data[1] || 0;
                const marginValue = salesValue - purchaseValue;
                const marginPercentage = salesValue > 0 ? (marginValue / salesValue * 100) : 0;

                const formatOptions = { minimumFractionDigits: 2, maximumFractionDigits: 2 };
                const salesFormatted = `${salesValue.toLocaleString('pl-PL', formatOptions)} zł (${(salesValue / (salesValue + purchaseValue) * 100).toFixed(2)}%)`;
                const purchaseFormatted = `${purchaseValue.toLocaleString('pl-PL', formatOptions)} zł (${(purchaseValue / (salesValue + purchaseValue) * 100).toFixed(2)}%)`;
                const marginFormatted = `${marginValue.toLocaleString('pl-PL', formatOptions)} zł (${marginPercentage.toFixed(2)}%)`;

                salesAmountEl.textContent = salesFormatted;
                purchaseAmountEl.textContent = purchaseFormatted;
                marginAmountEl.textContent = marginFormatted;

                // Add color coding for margin (green for positive, red for negative)
                if (marginValue > 0) {
                    marginAmountEl.classList.add('text-green-600');
                    marginAmountEl.classList.remove('text-red-600');
                } else if (marginValue < 0) {
                    marginAmountEl.classList.add('text-red-600');
                    marginAmountEl.classList.remove('text-green-600');
                } else {
                    marginAmountEl.classList.remove('text-green-600', 'text-red-600');
                }
            } catch (error) {
                console.error('Error updating financial summary:', error);
            }
        }

        // Function to download chart as PNG or JPG
        document.addEventListener('DOMContentLoaded', function () {
            // Check if pieChart already exists and update the financial summary
            if (pieChart && pieChart.data) {
                updateFinancialSummary(pieChart.data);
            }

            const downloadBtn = document.getElementsByName('downloadChartBtn');

            if(downloadBtn) {
                downloadBtn.forEach(function (element) {
                    document.addEventListener('chartCreated', function () {
                        element.classList.remove('hidden');
                    });
                    element.addEventListener('click', function (event) {
                        if(!chart) {
                            alert('Wykres nie jest dostępny. Najpierw wygeneruj wykres.');
                            return;
                        }

                        // Show loading state
                        const originalText = this.innerHTML;
                        this.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Pobieranie...';
                        this.disabled = true;

                        try {
                            // Get the canvas element
                            const canvas = document.getElementById('statsChart');

                            // Create a temporary link element
                            const link = document.createElement('a');

                            // Set the download attributes with more descriptive filename
                            const date = new Date().toISOString().slice(0, 10);
                            const time = new Date().toISOString().slice(11, 19).replace(/:/g, '-');
                            let filename = 'wykres_' + date + '_' + time;

                            // Add chart information to filename if available
                            if(chart && chart.data && chart.data.datasets && chart.data.datasets.length > 0) {
                                // Get the first dataset label as part of the filename
                                const firstDataset = chart.data.datasets[0];
                                if(firstDataset.label) {
                                    // Clean up the label for use in a filename
                                    const cleanLabel = firstDataset.label.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 30);
                                    filename += '_' + cleanLabel;
                                }
                            }

                            let type = this.getAttribute('data-type');
                            let extension = type === 'image/jpeg' ? 'jpg' : 'png';
                            let quality = type === 'image/jpeg' ? 0.90 : 1.0;
                            link.download = filename + '.' + extension;

                            // Check if we're not in dark mode before applying white background
                            if (!document.documentElement.classList.contains('dark')) {
                                let ctx = chart.canvas.getContext('2d');
                                ctx.save();
                                ctx.globalCompositeOperation = 'destination-over';
                                ctx.fillStyle = '#fff';
                                ctx.fillRect(0, 0, chart.canvas.width, chart.canvas.height);
                                ctx.restore();
                            }

                            link.href = chart.toBase64Image(this.getAttribute('data-type'), quality);

                            // Append the link to the body
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);

                            console.log('Chart download initiated');

                            // Restore button state
                            setTimeout(() => {
                                this.innerHTML = originalText;
                                this.disabled = false;
                            }, 1000);
                        } catch (error) {
                            console.error('Error downloading chart:', error);
                            alert('Wystąpił błąd podczas pobierania wykresu. Spróbuj ponownie.');

                            // Restore button state in case of error
                            this.innerHTML = originalText;
                            this.disabled = false;
                        }
                    });
                });
            }
        });
    </script>
</x-filament-panels::page>
