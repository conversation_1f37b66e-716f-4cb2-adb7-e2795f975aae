<x-filament-panels::page>

    {{ $this->table }}

    <x-filament::modal id="view-product" slide-over>
        <x-slot name="heading">
            Detale produktu
        </x-slot>
        {{ new \Illuminate\Support\HtmlString($this->info) }}
        <x-slot name="footer">
            <x-filament::button x-on:click="$dispatch('close-modal',{'id':'view-product'})">
                Zamknij
            </x-filament::button>
        </x-slot>
    </x-filament::modal>
</x-filament-panels::page>
