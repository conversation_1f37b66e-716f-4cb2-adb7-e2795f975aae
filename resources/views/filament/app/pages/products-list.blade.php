<x-filament-panels::page>

    {{ $this->table }}

    <x-filament::modal id="view-product">
        <x-slot name="heading">
            Detale produktu
        </x-slot>
            {{ new \Illuminate\Support\HtmlString($this->info) }}
        <x-slot name="footer">
            <x-filament::button x-on:click="$dispatch('close-modal',{'id':'view-product'})">
                Zamknij
            </x-filament::button>
        </x-slot>
    </x-filament::modal>
    <div wire:loading>
        <x-filament::loading-indicator class="h-5 w-5" />
    </div>

</x-filament-panels::page>
