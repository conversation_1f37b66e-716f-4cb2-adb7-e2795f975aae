<x-filament-panels::page>

    {{ $this->table }}

    <x-filament::modal id="print" alignment="center" :close-by-clicking-away="false">
        <x-slot name="heading">
            <h3>Drukuj</h3>
        </x-slot>
        <x-filament::link id="targetLink" href="{{$this->printUrl}}" target="_blank" icon="heroicon-o-printer">
                Wydruk
        </x-filament::link>
        <x-slot name="footer">
            <x-filament::button x-on:click="$dispatch('close-modal', {'id':'print'})">
                Zamknij
            </x-filament::button>
        </x-slot>
    </x-filament::modal>

    <x-filament::modal id="scan-modal" width="5xl">
        <div id="reader"></div>
        <x-slot name="footer">
            <x-filament::button x-on:click="$dispatch('change-camera')">
                <PERSON><PERSON><PERSON> kamerę
            </x-filament::button>
            <x-filament::button x-on:click="$dispatch('close-scan-modal')">
                Zamknij
            </x-filament::button>
        </x-slot>
    </x-filament::modal>

    <x-filament::modal id="get-camera-modal">
        <x-slot name="heading">
            Wybierz kamerę
        </x-slot>
        <div id="get-camera-content"></div>
        <x-slot name="footer">
            <x-filament::button x-on:click="$dispatch('close-modal',{'id':'get-camera-modal'})">
                Zamknij
            </x-filament::button>
        </x-slot>
    </x-filament::modal>

    @push('scripts')
        <script src="/js/html5-qrcode.min.js"></script>

        <script type="text/javascript">
            var skaner = null;

            var bcscanner = function (render, targetElement) {
                var devicesId = '';
                var renderElement = document.getElementById(render);
                var scannerEngine = null;
                var that = this;
                var scannerOnline = false;
                var shouldRunCamera = false;

                this.saveCamera = function(cameraIdNameString) {
                    [cameraId, cameraName] = cameraIdNameString.split('||')
                    localStorage.setItem('cameraId', cameraId);
                    localStorage.setItem('cameraName', cameraName);
                    closeModal('get-camera-modal');
                }

                const clearCamera = function() {
                    localStorage.removeItem('cameraId');
                    localStorage.removeItem('cameraName');
                }

                const getCamera = function () {
                    return {
                        'cameraId': localStorage.getItem('cameraId'),
                        'cameraName': localStorage.getItem('cameraName')
                    };
                };

                let getConfig = function() {
                    return {
                        fps: 10,
                        qrbox: { width: 250, height: 250 }
                    }
                }

                let setUp = function () {
                    devicesId = getCamera().cameraId;
                    scannerEngine = new Html5Qrcode(render);
                    openModal('scan-modal');
                    startEngine()
                }

                let onScanSuccess = async function (decodedText, decodedResult) {
                    console.log(`Scan result: ${decodedText}`, decodedResult);
                    await stopEngine();
                    let target = document.getElementById(targetElement);
                    target.focus();
                    typeTextIntoInput(decodedText, targetElement);
                }

                const startEngine = function () {
                    if(!scannerOnline) {
                        scannerOnline = true;
                        scannerEngine.start(
                            devicesId,
                            getConfig(),
                            (decodedText, decodedResult) => {
                                onScanSuccess(decodedText, decodedResult)
                            },
                            (errorMessage) => {
                                console.log('Error: ', errorMessage);
                            })
                            .catch((err) => {
                                console.log('Exception: ', err);
                            })
                    } else {
                        stopEngine()
                    }
                }

                const stopEngine = async function () {
                    scannerEngine.stop().then(() => {
                        renderElement.innertHtml = '';
                        closeModal('scan-modal');
                        scannerOnline = false;
                    }).catch(err => {
                        console.log(err);
                    });
                }

                const selectCamera = async function () {
                    Html5Qrcode.getCameras().then(devices => {
                        if(devices && devices.length) {
                            console.log(devices);
                            let list = '';
                            document.getElementById('get-camera-content').innerHTML = list;
                            for (const [key, value] of Object.entries(devices)) {
                                list += '<br><input ' +
                                    'type=radio ' +
                                    'name=cameraString ' +
                                    'onclick="skaner.saveCamera(this.value)" ' +
                                    'value="' + value.id + '||' + value.label + '"> ' + value.label;
                            }
                            document.getElementById('get-camera-content').innerHTML = list;
                            openModal('get-camera-modal');
                        } else {
                            console.log('no devices');
                        }
                    }).catch(err => {
                        console.log(err);
                    });
                }

                this.init = async function () {
                    if(null === getCamera().cameraId || getCamera().cameraId.length === 0) {
                        shouldRunCamera = true;
                        await selectCamera();
                    } else {
                        setUp();
                    }

                    addEventListener('close-scan-modal', async function () {
                        await stopEngine().then(() => {

                        }).catch( reason => {});
                    });
                    addEventListener('change-camera', async function () {
                        await stopEngine();
                        clearCamera();
                        setTimeout(() => selectCamera(), 300);
                    });
                }
            };


            function typeTextIntoInput(text, targetElement) {
                var inputField = document.getElementById(targetElement);

                function triggerKeyboardEvent(element, eventType, letter) {
                    var event = new KeyboardEvent(eventType, {
                        bubbles: true,
                        cancelable: true,
                        key: letter
                    });
                    element.dispatchEvent(event);
                }

                function triggerEvent(element, eventType, letter) {
                    var event = new Event(eventType, {
                        bubbles: true,
                        cancelable: true,
                        data: letter
                    });
                    element.dispatchEvent(event);
                }

                function typeLetter(index) {
                    if(index < text.length) {
                        triggerKeyboardEvent(inputField, 'keydown', text[index]);
                        triggerEvent(inputField, 'beforeinput', inputField.value);
                        inputField.value += text[index];
                        triggerEvent(inputField, 'input', text[index]);
                        setTimeout(function () {
                            triggerKeyboardEvent(inputField, 'keyup', text[index]);
                            typeLetter(index + 1);
                        }, 10);
                    }
                }

                typeLetter(0);
            }

            function openModal(id) {
                fireFEvent(window, 'open-modal', { 'id': id });
            }

            function closeModal(id) {
                fireFEvent(window, 'close-modal', { 'id': id });
            }

            function closePrintModal(id) {
                fireFEvent(window, 'close-modal', { 'id': id });
                setTimeout(() => {
                    const html = document.querySelector('html')
                    html.setAttribute('style',  '');
                }, 700);

            }

            function fireFEvent(element, eventType, data) {
                var event = new CustomEvent(eventType, {
                    bubbles: true,
                    cancelable: true,
                    detail: data
                });
                element.dispatchEvent(event);
            }

            var sb = document.getElementById('scan-button');
            sb.addEventListener('click', function () {
                if (null === skaner) {
                    skaner = new bcscanner('reader', 'tableFilters.gtin.qrcode');
                }
                skaner.init();
            });

            function handleClick(data){
                console.log(data);
                let el =document.getElementById('targetLink');
                el.setAttribute('href', data);
                console.log(el);
            }

        </script>
    @endpush
</x-filament-panels::page>
