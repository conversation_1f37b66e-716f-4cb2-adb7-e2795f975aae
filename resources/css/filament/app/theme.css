@import '/vendor/filament/filament/resources/css/theme.css';

@config 'tailwind.config.js';

.fi-topbar nav {
    @apply bg-slate-100;
}

.rounded-xl {
    border-radius: 0rem;
}

.rounded-lg {
    border-radius: 0rem;
}

.gap-6 {
    gap: 1.2rem;
}

.gap-4 {
    gap: 0.8rem;
}

.gap-2 {
    gap: 0.4rem;
}

.gap-1 {
    gap: 0.25rem;
}

.p-6 {
    padding: 1.2rem;
}

.p-4 {
    padding: 0.8rem;
}

.p-2 {
    padding: 0.4rem;
}

.p-1 {
    padding: 0.25rem;
}

.p-0\.5 {
    padding: 0.125rem;
}

.p-0\.25 {
    padding: 0.0625rem;
}

.fi-dropdown-list-item {
    padding: 0.5rem !important;
}

aside.fi-sidebar {
    @apply bg-gray-100;
    border-right-width: thin;
}

.fi-fieldset-two-c .fi-in-entry-wrp > div {
    @apply sm:grid-cols-2;
}

.fi-fieldset-two-c .fi-in-entry-wrp > div > :last-child {
    @apply sm:col-span-1;
}

.accent-color-custom {
    background-color: ghostwhite;
}

.fi-body {
    @apply bg-gradient-to-br from-slate-50 to-slate-100;
    /*@apply bg-white;*/
}

.fi-fieldset {
    @apply border border-slate-300 shadow-slate-200 shadow-sm bg-white;
}

.fi-fieldset legend {
    @apply border-b-2 border-b-slate-50 bg-slate-600 text-slate-50;
}

.fi-section {
    @apply border border-slate-300 shadow-slate-200 shadow-sm bg-white;
}

.fi-section .fi-section-content-ctn {
    @apply border-t border-slate-100;
}
 .fi-ta {
     @apply before:border-slate-300 after:border-slate-300  shadow-slate-200 shadow-sm;
 }
