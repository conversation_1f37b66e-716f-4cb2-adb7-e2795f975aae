const filterInput = {
    allowKeys: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON>R<PERSON>', '<PERSON>U<PERSON>', 'ArrowDown', 'Delete', 'Backspace', ' ', 'End', 'Home', 'Tab'],
    int: (e) => {
        if(filterInput.allowKeys.includes(e.key)) {return true;}
        return [48, 49, 50, 51, 52, 53, 54, 55, 56, 57].includes(e.which);
    },
    price: (e) => {
        if(filterInput.allowKeys.includes(e.key)) {return true;}
        return [43, 44, 46, 45, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57].includes(e.which);
    },
};
