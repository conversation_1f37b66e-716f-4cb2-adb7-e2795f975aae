## MC3 
### My Company Control Center (MC3) is a web application for managing your company's employees, contracts, invoices, expenses and more.

#### Main modules (ready or in development):
- Employees
- Invoices
- Expenses
- Payments
- Tasks
- Reports
- Warehouse
- Stock

#### Technologies used:
- PHP ^8.2 (8.3)
- <PERSON><PERSON> 10
- FilamentPHP (v3)
- MariaDB
- Docker
- Redis
- MongoDB
- Meilisearch


### Installation

Local development setup:
- Docker
- <PERSON>er-compose

* clone the repo
```bash
<NAME_EMAIL>:kamart/warehouse_backend.git
```
* prepare `.env` file
```bash
cp .env.example .env
```
* change the values in `.env` file
```bash
DB_HOST=mariadb
DB_PORT=3306
DB_DATABASE=wc3db
DB_USERNAME=wc3user
DB_PASSWORD=wc3pass
---
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379
---
MAIL_HOST=mailhog_server
MAIL_PORT=1025
MAIL_FROM_ADDRESS="<EMAIL>"
---
BASE_APP_DOMAIN="mc3.test"
BASE_ADMIN_DOMAIN="managemc3.test"
---
WWWUSER=1000
WWWGROUP=1000
COMPOSE_PROJECT_NAME=mc3
```
* run `echo "127.0.0.1 mc3.test" | sudo tee -a /etc/hosts`
* run `echo "127.0.0.1 managemc3.test" | sudo tee -a /etc/hosts`
* run `bash setupapp.sh` script
* open browser and go to `http://managemc3.test`
* login with `<EMAIL>` and `F4jN3H4Sl0`
