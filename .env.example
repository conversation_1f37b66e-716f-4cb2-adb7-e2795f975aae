APP_NAME=MC3
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOCALE=pl

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=mariadb
DB_PORT=3306
DB_DATABASE=wc3db
DB_USERNAME=wc3user
DB_PASSWORD=wc3pass

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog_server
#MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

DRUG_IMPORT_INCREMENTAL_URL="https://rejestry.ezdrowie.gov.pl/api/rpl/medicinal-products/public-pl-report/4.0.0/incremental.xml"
DRUG_IMPORT_OVERALL_URL="https://rejestry.ezdrowie.gov.pl/api/rpl/medicinal-products/public-pl-report/4.0.0/overall.xml"

BASE_APP_DOMAIN="http://localhost:8000"
BASE_ADMIN_DOMAIN="http://localhost:8000/admin"

#DOCKER
WWWUSER=1000
WWWGROUP=1000
COMPOSE_PROJECT_NAME=tf2
ARTISAN_SERVICE="app"
QUEUE_SERVICE="queue"
FORWARD_DB_PORT='127.0.0.1:3306'
FORWARD_DB_REPLICA_PORT='*************:3306'
MARIADB_REPLICATION_USER: 'username'
MARIADB_REPLICATION_PASSWORD: 'password'
MARIADB_MASTER_HOST:'********:3306'

#GUS
GUS_USER_KEY='abcde12345abcde12345'
GUS_ENV=dev

APP_ALLOW_REGISTER=false
