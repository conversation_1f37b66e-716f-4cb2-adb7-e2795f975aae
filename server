#!/usr/bin/env bash

set -o allexport
source .env set
set +o allexport

args=("$@")


if [ "$APP_ENV" == "production" ]; then
    export NGINX_CONF=default_ssl.conf
    export NGINX_BUILD=nginx.production
    dcfile=$(dirname $0)/server.docker-compose.yml
    echo "Running in production mode"
elif [ "$APP_ENV" == "staging" ]; then
    export NGINX_CONF=stage_ssl.conf
    export NGINX_BUILD=nginx
    dcfile=$(dirname $0)/stage.docker-compose.yml
    echo "Running in staging mode"
elif [ "$APP_ENV" == "replica" ]; then
    export NGINX_CONF=stage_ssl.conf
    export NGINX_BUILD=nginx
    dcfile=$(dirname $0)/replica.docker-compose.yml
    echo "Running in replica mode"
else
    dcfile=$(dirname $0)/server.docker-compose.yml
    export NGINX_CONF=default.conf
    export NGINX_BUILD=nginx
    echo "Running in development mode"
fi

echo "Docker Compose File: $dcfile"
echo "Docker Compose ENV: ${APP_ENV}"

if [ $# -eq 0 ]; then
    echo "Usage: $0 [up|down|dev-up|dev-down|restart|start|build|shell|rootshell|logs|exec|artisan|queue|backup-db]"
    exit 1
fi

case $1 in
    up)
        docker compose -f $dcfile up -d $2
        ;;
    down)
        docker compose -f $dcfile down $2
        ;;
    dev-up)
        if [ "$APP_ENV" != "local" ]; then
            echo "Running in production is not allowed"
            exit 1
        fi
        docker compose -f $dcfile --profile development up -d $2
        ;;
    dev-down)
        docker compose -f $dcfile --profile development down $2
        ;;
    restart)
        docker compose -f $dcfile restart $2
        ;;
    backup-db)
        echo "Backing up database"
        echo "Clean up previous backup database"
        docker compose -f $dcfile exec mariadb rm -rf /backup/
        echo "Change ownership of backup directory"
        docker compose -f $dcfile exec mariadb chown -R mysql:mysql /backup
        docker compose -f $dcfile exec mariadb chmod 777 /backup
        echo "Starting backup container"
        docker compose -f $dcfile exec -it mariadb /usr/bin/bash /scripts/do-backup.sh
        if [ -f "docker/mysql/backup/mariadb_backup_info" ]; then
            echo "Backup file found. Compressing..."
            tar -czf storage/db_backup_"$(date +%Y%m%d%H%M%S)".tar.gz docker/mysql/backup
            echo "Compressing completed"
        fi
        echo "Backup completed"

        ;;
    start)
        docker compose -f $dcfile start $2
        ;;
    build)
        docker compose -f $dcfile build "${args[@]:1}"
        ;;
    shell)
        docker compose -f $dcfile exec -it -u 1000:1000 $2 /bin/bash
        ;;
    rootshell)
        docker compose -f $dcfile exec -it -u root $2 /bin/bash
        ;;
    logs)
        docker compose -f $dcfile logs -f $2
        ;;
    exec)
        docker compose -f $dcfile exec -it -u 1000:1000 $2 "${args[@]:2}"
        ;;
    artisan)
        docker compose -f $dcfile exec -it -u 1000:1000 "$ARTISAN_SERVICE" php artisan "${args[@]:1}"
        ;;
    queue)
        if [ $# -gt 1 ]; then
            params=":${args[*]:1}"
        else
            params=""
        fi
        docker compose -f $dcfile exec -it -u 1000:1000 "$QUEUE_SERVICE" php artisan queue$params
        ;;
    *)
        echo "Usage: $0 [up|down|dev-up|dev-down|restart|start|build|shell|rootshell|logs|exec|artisan|queue|backup-db]"
        exit 1
        ;;
esac
