<?php

use App\Filament\App\Pages\Auth\ConfirmRegistration;
use App\Filament\App\Pages\Auth\ConfirmRegistrationData;
use App\Http\Controllers\ExampleWelcomeEmailController;
use App\Http\Controllers\PrintController;
use App\Http\Middleware\UserAllowedToUse;
use Filament\Http\Middleware\Authenticate;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/


Route::domain(config('app.domain.admin'))->get('/', function () {
    return redirect('/adm');
});


Route::get('/', function () {
    return redirect('/app');
});

Route::middleware([
    Authenticate::class,
    UserAllowedToUse::class
])->group(function () {
    //TODO: fix other routes
    Route::get('/print/{doctype}/{doc}/{output}', [PrintController::class,'print'])
        ->name('print');
    Route::get('/print/whd/{doc}/html', [PrintController::class,'html']);
    Route::get('/print/whd/{doc}/pdf', [PrintController::class,'pdf'])
        ->name('printPDF');
    Route::get('/print/{type}/{doc}/html', [PrintController::class,'htmlDoc'])
        ->name('previewPrint');

    Route::post('/charts/stats', [\App\Http\Controllers\ChartsController::class, 'getData'])->name('chart-stats');
});

Route::get('/welcome-email-test', [ExampleWelcomeEmailController::class, 'sendWelcomeEmail']);
Route::get('/pass-reset-email-test', [ExampleWelcomeEmailController::class, 'sendPassResetEmail']);

// Registration confirmation route
Route::name('filament.app.auth.register.confirm')
    ->get('/app/register/confirm/{hash}', ConfirmRegistration::class)
    ->middleware(['web']);
Route::name('filament.app.auth.register.confirm-data')
    ->get('/app/register/confirm-data/{hash}', ConfirmRegistrationData::class)
    ->middleware(['web']);
