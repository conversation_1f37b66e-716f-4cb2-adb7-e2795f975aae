INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (1, 'view-any Installation', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (2, 'view-any Installation', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (3, 'view Installation', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (4, 'view Installation', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (5, 'create Installation', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (6, 'create Installation', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (7, 'update Installation', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (8, 'update Installation', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (9, 'delete Installation', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (10, 'delete Installation', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (11, 'restore Installation', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (12, 'restore Installation', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (13, 'force-delete Installation', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (14, 'force-delete Installation', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (15, 'replicate Installation', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (16, 'replicate Installation', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (17, 'reorder Installation', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (18, 'reorder Installation', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (19, 'view-any Manufacturer', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (20, 'view-any Manufacturer', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (21, 'view Manufacturer', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (22, 'view Manufacturer', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (23, 'create Manufacturer', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (24, 'create Manufacturer', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (25, 'update Manufacturer', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (26, 'update Manufacturer', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (27, 'delete Manufacturer', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (28, 'delete Manufacturer', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (29, 'restore Manufacturer', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (30, 'restore Manufacturer', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (31, 'force-delete Manufacturer', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (32, 'force-delete Manufacturer', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (33, 'replicate Manufacturer', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (34, 'replicate Manufacturer', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (35, 'reorder Manufacturer', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (36, 'reorder Manufacturer', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (37, 'view-any Products', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (38, 'view-any Products', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (39, 'view Products', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (40, 'view Products', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (41, 'create Products', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (42, 'create Products', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (43, 'update Products', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (44, 'update Products', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (45, 'delete Products', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (46, 'delete Products', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (47, 'restore Products', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (48, 'restore Products', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (49, 'force-delete Products', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (50, 'force-delete Products', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (51, 'replicate Products', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (52, 'replicate Products', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (53, 'reorder Products', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (54, 'reorder Products', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (55, 'view-any Tenant', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (56, 'view-any Tenant', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (57, 'view Tenant', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (58, 'view Tenant', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (59, 'create Tenant', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (60, 'create Tenant', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (61, 'update Tenant', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (62, 'update Tenant', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (63, 'delete Tenant', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (64, 'delete Tenant', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (65, 'restore Tenant', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (66, 'restore Tenant', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (67, 'force-delete Tenant', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (68, 'force-delete Tenant', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (69, 'replicate Tenant', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (70, 'replicate Tenant', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (71, 'reorder Tenant', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (72, 'reorder Tenant', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (73, 'view-any User', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (74, 'view-any User', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (75, 'view User', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (76, 'view User', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (77, 'create User', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (78, 'create User', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (79, 'update User', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (80, 'update User', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (81, 'delete User', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (82, 'delete User', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (83, 'restore User', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (84, 'restore User', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (85, 'force-delete User', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (86, 'force-delete User', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (87, 'replicate User', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (88, 'replicate User', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (89, 'reorder User', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (90, 'reorder User', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (91, 'view-any Warehouse', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (92, 'view-any Warehouse', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (93, 'view Warehouse', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (94, 'view Warehouse', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (95, 'create Warehouse', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (96, 'create Warehouse', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (97, 'update Warehouse', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (98, 'update Warehouse', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (99, 'delete Warehouse', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (100, 'delete Warehouse', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (101, 'restore Warehouse', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (102, 'restore Warehouse', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (103, 'force-delete Warehouse', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (104, 'force-delete Warehouse', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (105, 'replicate Warehouse', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (106, 'replicate Warehouse', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (107, 'reorder Warehouse', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (108, 'reorder Warehouse', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (109, 'view-any WarehouseLog', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (110, 'view-any WarehouseLog', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (111, 'view WarehouseLog', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (112, 'view WarehouseLog', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (113, 'create WarehouseLog', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (114, 'create WarehouseLog', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (115, 'update WarehouseLog', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (116, 'update WarehouseLog', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (117, 'delete WarehouseLog', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (118, 'delete WarehouseLog', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (119, 'restore WarehouseLog', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (120, 'restore WarehouseLog', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (121, 'force-delete WarehouseLog', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (122, 'force-delete WarehouseLog', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (123, 'replicate WarehouseLog', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (124, 'replicate WarehouseLog', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (125, 'reorder WarehouseLog', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (126, 'reorder WarehouseLog', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (127, 'view-any WarehouseState', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (128, 'view-any WarehouseState', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (129, 'view WarehouseState', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (130, 'view WarehouseState', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (131, 'create WarehouseState', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (132, 'create WarehouseState', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (133, 'update WarehouseState', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (134, 'update WarehouseState', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (135, 'delete WarehouseState', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (136, 'delete WarehouseState', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (137, 'restore WarehouseState', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (138, 'restore WarehouseState', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (139, 'force-delete WarehouseState', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (140, 'force-delete WarehouseState', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (141, 'replicate WarehouseState', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (142, 'replicate WarehouseState', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (143, 'reorder WarehouseState', 'web', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
INSERT INTO permissions (id, name, guard_name, created_at, updated_at) VALUES (144, 'reorder WarehouseState', 'api', '2024-02-28 14:42:58', '2024-02-28 14:42:58');
